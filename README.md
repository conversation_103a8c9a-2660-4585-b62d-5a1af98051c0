# Valuit Tokenization Platform

<div align="center">

![Valuit Logo](src/public/images/logo.png)

**A comprehensive asset tokenization platform supporting real estate, equity, and other asset classes with blockchain integration, KYC/KYB verification, governance voting, and compliant trading.**

[![Node.js](https://img.shields.io/badge/Node.js-20.10.0-green.svg)](https://nodejs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.6.3-blue.svg)](https://www.typescriptlang.org/)
[![MongoDB](https://img.shields.io/badge/MongoDB-Latest-green.svg)](https://www.mongodb.com/)
[![Express.js](https://img.shields.io/badge/Express.js-4.19.2-lightgrey.svg)](https://expressjs.com/)
[![License](https://img.shields.io/badge/License-Proprietary-red.svg)](https://valuit.com/license)

</div>

## 📋 Table of Contents

- [Project Overview](#-project-overview)
- [Features](#-features)
- [Technologies Used](#-technologies-used)
- [Installation & Setup](#-installation--setup)
- [Environment Configuration](#-environment-configuration)
- [Running the Application](#-running-the-application)
- [API Documentation](#-api-documentation)
- [Authentication Flow](#-authentication-flow)
- [Directory Structure](#-directory-structure)
- [Developer Guidelines](#-developer-guidelines)
- [Testing](#-testing)
- [Deployment](#-deployment)
- [Contributing](#-contributing)
- [Support](#-support)

## 🚀 Project Overview

Valuit Tokenization Platform is a comprehensive backend service for asset tokenization, enabling institutions and investors to create, manage, and trade tokenized real estate, equity, and other asset classes. The platform provides a complete ecosystem for compliant digital asset management with blockchain integration.

### Key Capabilities

- **Asset Tokenization**: Create and manage tokenized offerings for various asset classes
- **User Management**: Multi-tier user system (investors, institutions, representatives)
- **KYC/KYB Integration**: Automated compliance verification using SumSub
- **Governance System**: Proposal creation and voting mechanisms
- **Trading & Transfers**: Secure token transfer and redemption system
- **Dividend Distribution**: Automated dividend calculations and distributions
- **Document Management**: DocuSign integration for legal documents
- **Multi-signature Support**: Enterprise-grade security with multisig wallets

## ✨ Features

### Core Features

- 🏠 **Real Estate Tokenization**: Fractional ownership of real estate assets
- 📊 **Equity Tokenization**: Digitize company shares and equity instruments
- 👥 **Multi-User System**: Support for investors, institutions, and representatives
- 🔐 **Advanced Security**: JWT authentication with 2FA support
- 📋 **KYC/KYB Compliance**: Automated identity verification
- 🗳️ **Governance Voting**: Decentralized decision-making system
- 💰 **Dividend Management**: Automated distribution calculations
- 📝 **Document Signing**: Integrated DocuSign workflow

### Advanced Features

- 🔗 **Blockchain Integration**: Ethereum-compatible smart contracts
- 📧 **Multi-channel Communications**: Email and SMS notifications
- 🔄 **Real-time Updates**: WebSocket integration for live data
- 📊 **Analytics & Reporting**: Comprehensive investment tracking
- 🌐 **Microservices Architecture**: gRPC-based service communication
- 🚀 **High Performance**: Redis caching and Kafka messaging

## 🛠 Technologies Used

### Backend Core

- **Runtime**: Node.js 20.10.0
- **Language**: TypeScript 5.6.3
- **Framework**: Express.js 4.19.2
- **Database**: MongoDB 8.11.0
- **Cache**: Redis 4.6.14
- **Message Queue**: Kafka (KafkaJS 2.2.4)

### Blockchain & Web3

- **Blockchain**: Ethereum (Ethers.js 6.13.4)
- **Smart Contracts**: Custom tokenization contracts
- **Wallet Integration**: Multi-signature wallet support

### Security & Authentication

- **Authentication**: JWT with 2FA (OTP)
- **Encryption**: bcrypt for password hashing
- **Rate Limiting**: Express rate limiter
- **Security Headers**: Helmet.js

### External Integrations

- **KYC/KYB**: SumSub API
- **Document Signing**: DocuSign eSignature API
- **Email Service**: SendGrid
- **SMS Service**: Twilio
- **Cloud Storage**: Google Cloud Storage
- **Communication**: gRPC, Socket.IO

### Development & Deployment

- **Container**: Docker
- **Process Manager**: Nodemon (development)
- **Code Quality**: ESLint, Prettier
- **API Documentation**: Swagger/OpenAPI 3.0
- **Logging**: Winston with daily rotation

## 🚀 Installation & Setup

### Prerequisites

Ensure you have the following installed:

- **Node.js**: v20.10.0
- **npm**: v10.2.3
- **MongoDB**: Latest version
- **Redis**: Latest version
- **Docker** (optional, for containerized deployment)

### Step 1: Clone Repository

```bash
git clone https://github.com/valuit-official/valuit-backed-marketplace.git
cd valuit-backed-marketplace
```

### Step 2: Install Dependencies

```bash
npm install
```

### Step 3: Environment Setup

```bash
# Copy environment template
cp env.example .env

# Edit environment variables
nano .env  # or your preferred editor
```

### Step 4: Database Setup

Ensure MongoDB and Redis are running on your system:

```bash
# MongoDB (if using local installation)
mongod

# Redis (if using local installation)
redis-server
```

### Step 5: Build Application

```bash
npm run build
```

## ⚙️ Environment Configuration

Configure the following environment variables in your `.env` file:

### Application Settings

```env
NODE_ENV=development              # Environment: development, staging, production
PROJECT_NAME=Valuit-Tokenization  # Project identifier
LOG_LEVEL=debug                   # Logging level
PORT=4000                        # Server port
```

### Database Configuration

```env
# MongoDB
MONGODB_HOST=localhost           # MongoDB host
MONGODB_USER=your_username       # Database username
MONGODB_PASSWORD=your_password   # Database password
MONGODB_PORT=27017              # MongoDB port
MONGODB_DATABASE=valuit_db      # Database name

# Redis
REDIS_HOST=redis://localhost    # Redis connection string
LOGIN_MAX_ATTEMPT=5             # Max login attempts
LOGIN_BLOCK_TIME=900            # Block time in seconds
OTP_EXPIRY=300                  # OTP expiry in seconds
```

### Authentication & Security

```env
# JWT Configuration
JWT_AUTH_SECRET=your_secret_key          # JWT signing secret
JWT_AUTH_EXPIRE=7h                       # Access token expiry
JWT_REFRESH_SECRET=your_refresh_secret   # Refresh token secret
JWT_REFRESH_EXPIRE=86400                 # Refresh token expiry (seconds)
JWT_FORGOT_EXPIRE=10m                    # Forgot password token expiry
```

### External Services

```env
# SendGrid (Email)
SENDGRID_API_KEY=your_sendgrid_key
SENDER=<EMAIL>

# Twilio (SMS)
TWILIO_ACCOUNT_SID=your_account_sid
TWILIO_AUTH_TOKEN=your_auth_token
TWILIO_PHONE_NUMBER=your_phone_number

# Google Cloud Storage
GOOGLE_CLIENT_ID=your_client_id
GOOGLE_PROJECT_ID=your_project_id
BUCKET_NAME=your_bucket_name
MAX_SIZE=5                               # Max file size in MB

# SumSub (KYC/KYB)
SUMSUB_SECRET_KEY=your_secret_key
SUMSUB_TOKEN=your_access_token

# DocuSign
DOCUSIGN_CLIENT_ID=your_client_id
DOCUSIGN_IMPERSONATED_USER_ID=your_user_id
DOCUSIGN_ACCOUNT_ID=your_account_id
DOCUSIGN_OAUTH_URL=https://account-d.docusign.com
DOCUSIGN_WEBHOOK_USERNAME=webhook_user
DOCUSIGN_WEBHOOK_PWD=webhook_password
DOCUSIGN_PRIVATE_KEY="-----BEGIN RSA PRIVATE KEY-----
...your private key...
-----END RSA PRIVATE KEY-----"
```

### Blockchain Configuration

```env
# Ethereum Network
RPC_URL=https://your-ethereum-rpc-url
FUND_CONTRACT_ADDRESS=0x...           # Fund contract address
USDC_ADDRESS=0x...                    # USDC token address
```

### Microservices (gRPC)

```env
# Service Ports
USER_SERVICE_GRPC_PORT=40001
ADMIN_SERVICE_GRPC_PORT=7001
NOTIFICATION_SERVICE_GRPC_PORT=8001
SOCKET_PORT=9004

# Service Hosts
USER_SERVICE_GRPC_CONTAINER_NAME=localhost
ADMIN_SERVICE_GRPC_CONTAINER_NAME=localhost
NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME=localhost
```

### Message Queue

```env
# Kafka Configuration
KAFKA_BROKER=localhost
KAFKA_BROKER_PORT=9092
```

## 🏃‍♂️ Running the Application

### Development Mode

```bash
# Start with auto-reload
npm run dev

# The application will be available at:
# - API: http://localhost:4000
# - Health Check: http://localhost:4000/health-check
# - Swagger UI: http://localhost:4000/v1/api-docs
```

### Production Mode

```bash
# Build and start
npm run build
npm start
```

### Docker Deployment

```bash
# Using Docker Compose
docker-compose up -d

# The application will be available at:
# http://localhost:7000
```

### Services Status

After starting the application, verify all services are running:

1. **Main API**: http://localhost:4000/health-check
2. **Swagger Documentation**: http://localhost:4000/v1/api-docs
3. **gRPC Services**: Check logs for gRPC server startup
4. **Socket.IO**: Running on configured port (default: 9004)

## 📚 API Documentation

### Swagger UI Access

The API documentation is available through Swagger UI at different endpoints based on your environment:

#### Environment-Specific URLs

| Environment           | Swagger UI URL                             |
| --------------------- | ------------------------------------------ |
| **Local Development** | http://localhost:4000/v1/api-docs          |
| **Staging**           | https://api-staging.valuit.com/v1/api-docs |
| **Production**        | https://api.valuit.com/v1/api-docs         |

### API Endpoints Overview

#### Authentication Endpoints

- `POST /v1/signup` - User registration
- `POST /v1/login` - User authentication
- `POST /v1/social-login` - Social media authentication
- `POST /v1/verify` - OTP verification
- `POST /v1/forgot-password` - Password reset initiation
- `POST /v1/reset-password` - Password reset completion

#### User Management

- `GET /v1/auth/profile` - Get user profile
- `PUT /v1/auth/profile` - Update user profile
- `POST /v1/auth/upload-docs` - Upload KYC documents
- `GET /v1/auth/enable-2fa` - Enable two-factor authentication

#### Offerings Management

- `POST /v1/auth/offering` - Create new offering (issuer only)
- `GET /v1/auth/offering` - List user offerings
- `GET /v1/auth/offering/{id}` - Get offering details
- `PUT /v1/auth/offering/update-offering` - Update offering
- `DELETE /v1/auth/offering/{id}` - Delete offering

#### Order Management

- `POST /v1/order/create` - Create investment order
- `GET /v1/order/user-orders` - Get user orders
- `GET /v1/order/portfolio` - Get portfolio summary
- `GET /v1/order/{orderId}` - Get order details

#### Transfer & Redemption

- `POST /v1/auth/transfer` - Create transfer request
- `GET /v1/auth/transfer` - List transfer requests
- `POST /v1/auth/redeem` - Create redemption request
- `GET /v1/auth/redeem/{id}` - Get redemption requests

#### Governance & Proposals

- `POST /v1/proposal` - Create proposal
- `GET /v1/proposal/investor` - Get investor proposals
- `POST /v1/proposal/vote` - Vote on proposal
- `GET /v1/proposal/details` - Get proposal details

#### Dividend Management

- `POST /v1/dividend` - Create dividend distribution
- `GET /v1/dividend/issuer` - Get issuer dividends
- `GET /v1/dividend/history` - Get dividend history

### Response Format

All API responses follow a consistent format:

```json
{
  "message": "Success message",
  "status": 200,
  "data": {
    // Response data
  },
  "error": false
}
```

Error responses:

```json
{
  "message": "Error message",
  "status": 400,
  "data": {},
  "error": true
}
```

## 🔐 Authentication Flow

### 1. User Registration

```mermaid
sequenceDiagram
    participant Client
    participant API
    participant Database
    participant EmailService

    Client->>API: POST /v1/signup
    API->>Database: Check if user exists
    API->>Database: Create pending user
    API->>EmailService: Send OTP
    API-->>Client: Registration initiated
    Client->>API: POST /v1/verify (with OTP)
    API->>Database: Activate user account
    API-->>Client: Registration complete
```

### 2. User Login

```mermaid
sequenceDiagram
    participant Client
    participant API
    participant Database
    participant Redis

    Client->>API: POST /v1/login
    API->>Database: Validate credentials
    API->>Redis: Check 2FA status
    alt 2FA Enabled
        API->>EmailService: Send 2FA OTP
        API-->>Client: 2FA required
        Client->>API: POST /v1/verify-login-2fa
    end
    API->>Redis: Store access token
    API-->>Client: JWT tokens + user info
```

### 3. JWT Token Structure

**Access Token Payload:**

```json
{
  "userId": "user_id",
  "email": "<EMAIL>",
  "userType": "investor|institution",
  "isKyc": true,
  "isIssuer": false,
  "exp": **********
}
```

### 4. Authentication Middleware

All protected routes require the `Authorization` header:

```
Authorization: Bearer <jwt_token>
```

**User Types & Permissions:**

- **Investor**: Can invest in offerings, view portfolio, vote on proposals
- **Institution**: Can create offerings, manage investors, access reports
- **Representative**: Limited access, can only view data (read-only)

### 5. Two-Factor Authentication (2FA)

- **Setup**: Generate QR code and secret for authenticator apps
- **Verification**: TOTP-based verification for enhanced security
- **Backup**: OTP delivery via email/SMS as fallback

## 📁 Directory Structure

```
valuit-backed-marketplace/
├── 📁 src/                          # Source code
│   ├── 📁 _grpc/                    # gRPC services
│   │   ├── 📁 clients/              # gRPC client implementations
│   │   ├── 📁 proto/                # Protocol buffer definitions
│   │   └── index.ts                 # gRPC server setup
│   │
│   ├── 📁 component/                # Business logic modules
│   │   ├── 📁 dividends/            # Dividend management
│   │   ├── 📁 docuSign/             # DocuSign integration
│   │   ├── 📁 multisig/             # Multi-signature wallets
│   │   ├── 📁 notification/         # Notification system
│   │   ├── 📁 offerings/            # Investment offerings
│   │   ├── 📁 order/                # Order management
│   │   ├── 📁 proposals/            # Governance proposals
│   │   ├── 📁 redeem/               # Token redemption
│   │   ├── 📁 representative/       # Representative management
│   │   ├── 📁 transactions/         # Transaction history
│   │   ├── 📁 transfer/             # Token transfers
│   │   └── 📁 userAuthentications/  # User management
│   │
│   ├── 📁 config/                   # Configuration files
│   │   ├── 📁 connection/           # Database connections
│   │   ├── 📁 env/                  # Environment configs
│   │   ├── 📁 error/                # Error handling
│   │   ├── 📁 middleware/           # Auth middleware
│   │   └── 📁 server/               # Server setup
│   │
│   ├── 📁 helpers/                  # Utility functions
│   │   ├── 📁 logging/              # Logging utilities
│   │   ├── cloud.helper.ts          # Cloud storage
│   │   ├── common.helper.ts         # Common utilities
│   │   ├── email.helper.ts          # Email services
│   │   ├── kafka.helper.ts          # Kafka messaging
│   │   ├── redis.helper.ts          # Redis operations
│   │   └── socket.helper.ts         # WebSocket management
│   │
│   ├── 📁 middleware/               # Express middleware
│   │   ├── dividend.middleware.ts   # Dividend validation
│   │   ├── offering.middleware.ts   # Offering validation
│   │   ├── rate-limiter.ts          # Rate limiting
│   │   └── ...                      # Other middleware
│   │
│   ├── 📁 public/                   # Static assets
│   │   └── 📁 images/               # Application images
│   │
│   ├── 📁 routes/                   # API route definitions
│   │   ├── auth.router.ts           # Authentication routes
│   │   ├── offering.router.ts       # Offering routes
│   │   ├── order.router.ts          # Order routes
│   │   └── ...                      # Other routes
│   │
│   ├── 📁 service/                  # Background services
│   │   ├── cronHandler.ts           # Scheduled tasks
│   │   └── kafkaService.ts          # Kafka consumers
│   │
│   └── 📁 utils/                    # Utilities & constants
│       ├── 📁 abi/                  # Smart contract ABIs
│       ├── 📁 emailTemplate/        # Email templates
│       ├── common.interface.ts      # Type definitions
│       ├── constant.ts              # Application constants
│       ├── responseUtils.ts         # Response helpers
│       └── swaggerDef.ts            # API documentation
│
├── 📄 docker-compose.yaml           # Docker configuration
├── 📄 Dockerfile                    # Docker build file
├── 📄 env.example                   # Environment template
├── 📄 package.json                  # Dependencies & scripts
├── 📄 tsconfig.json                 # TypeScript configuration
└── 📄 README.md                     # Project documentation
```

### Component Architecture

Each component follows a consistent structure:

```
component/
├── index.ts          # Component exports
├── interface.ts      # Type definitions
├── service.ts        # Business logic
├── validation.ts     # Input validation
└── models/          # Database models
    └── *.model.ts
```

## 👨‍💻 Developer Guidelines

### Code Style & Standards

#### TypeScript Guidelines

- Use strict TypeScript configuration
- Define interfaces for all data structures
- Implement proper error handling with try-catch blocks
- Use async/await instead of promises

#### Naming Conventions

- **Variables**: camelCase (`userName`, `isVerified`)
- **Functions**: camelCase (`createUser`, `validateInput`)
- **Classes**: PascalCase (`UserService`, `DatabaseConnection`)
- **Constants**: UPPER_SNAKE_CASE (`API_VERSION`, `MAX_RETRY_ATTEMPTS`)
- **Files**: kebab-case (`user-service.ts`, `auth-middleware.ts`)

#### Code Organization

- Keep functions small and focused (max 20-30 lines)
- Use descriptive variable and function names
- Group related functionality in modules
- Implement proper separation of concerns

### Development Workflow

#### 1. Setting Up Development Environment

```bash
# Clone repository
git clone <repository-url>
cd valuit-backed-marketplace

# Install dependencies
npm install

# Set up environment
cp env.example .env
# Configure your .env file

# Start development server
npm run dev
```

#### 2. Making Changes

```bash
# Create feature branch
git checkout -b feature/your-feature-name

# Make your changes
# Run linting and formatting
npm run format

# Test your changes
# Commit your changes
git add .
git commit -m "feat: add new feature description"

# Push changes
git push origin feature/your-feature-name
```

#### 3. Code Quality Tools

```bash
# Format code
npm run format

# Lint code (included in format script)
npx eslint --fix './src/**/*.{ts,tsx,js}'

# Build project
npm run build
```

### Database Guidelines

#### MongoDB Best Practices

- Use proper indexing for frequently queried fields
- Implement data validation at the schema level
- Use aggregation pipelines for complex queries
- Handle database errors gracefully

#### Model Conventions

```typescript
// Example model structure
import { Schema, model, Document } from 'mongoose';

export interface IUser extends Document {
  email: string;
  password: string;
  userType: 'investor' | 'institution';
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const userSchema = new Schema<IUser>(
  {
    email: { type: String, required: true, unique: true },
    password: { type: String, required: true },
    userType: { type: String, enum: ['investor', 'institution'], required: true },
    isActive: { type: Boolean, default: true },
  },
  {
    timestamps: true,
  },
);

export const User = model<IUser>('User', userSchema);
```

### API Development Guidelines

#### Route Structure

```typescript
// routes/example.router.ts
import { Router } from 'express';
import { isAuthenticated } from '../middleware/auth';
import ExampleController from '../controllers/example.controller';

const router = Router();

router.post('/', isAuthenticated, ExampleController.create);
router.get('/', isAuthenticated, ExampleController.list);
router.get('/:id', isAuthenticated, ExampleController.getById);

export default router;
```

#### Error Handling

```typescript
// Use consistent error handling
try {
  const result = await someAsyncOperation();
  return ResponseHandler.success(res, {
    message: 'Operation successful',
    data: result,
  });
} catch (error) {
  logger.error(error, 'Operation failed');
  return ResponseHandler.error(res, {
    message: 'Operation failed',
    status: 500,
  });
}
```

### Testing Guidelines

#### Unit Testing Setup

```bash
# Install testing dependencies
npm install --save-dev mocha chai supertest @types/mocha @types/chai @types/supertest

# Run tests
npm test
```

#### Test Structure

```typescript
// tests/user.test.ts
import { expect } from 'chai';
import request from 'supertest';
import app from '../src/app';

describe('User Authentication', () => {
  describe('POST /v1/signup', () => {
    it('should create a new user', async () => {
      const response = await request(app).post('/v1/signup').send({
        email: '<EMAIL>',
        password: 'TestPass123!',
        userType: 'investor',
      });

      expect(response.status).to.equal(201);
      expect(response.body.error).to.be.false;
    });
  });
});
```

### Security Guidelines

#### Input Validation

- Validate all user inputs using Joi schemas
- Sanitize data before database operations
- Implement rate limiting on sensitive endpoints

#### Authentication Security

- Use strong JWT secrets
- Implement token rotation
- Add proper CORS configuration
- Use HTTPS in production

#### Data Protection

- Hash passwords using bcrypt
- Encrypt sensitive data at rest
- Implement proper access controls
- Log security events

### Performance Guidelines

#### Database Optimization

- Use proper indexing strategies
- Implement pagination for large datasets
- Use aggregation pipelines efficiently
- Monitor query performance

#### Caching Strategy

- Use Redis for session management
- Cache frequently accessed data
- Implement cache invalidation strategies
- Monitor cache hit rates

#### API Optimization

- Implement response compression
- Use proper HTTP status codes
- Minimize payload sizes
- Implement request/response logging

## 🧪 Testing

### Running Tests

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```

### Test Structure

- **Unit Tests**: Test individual functions and methods
- **Integration Tests**: Test API endpoints and database interactions
- **E2E Tests**: Test complete user workflows

### Testing Environment

Ensure you have a separate testing database and Redis instance for running tests:

```env
# .env.test
NODE_ENV=test
MONGODB_DATABASE=valuit_test_db
REDIS_HOST=redis://localhost:6380
```

## 🚀 Deployment

### Docker Deployment

#### Building Docker Image

```bash
# Build the image
docker build -t valuit-tokenization .

# Run the container
docker run -p 4000:4000 --env-file .env valuit-tokenization
```

#### Using Docker Compose

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Production Deployment

#### Environment Setup

1. Set `NODE_ENV=production`
2. Configure production database connections
3. Set up proper logging and monitoring
4. Configure SSL certificates
5. Set up load balancing if needed

#### Health Checks

- **Application Health**: `GET /health-check`
- **Database Health**: Monitor MongoDB connection
- **Cache Health**: Monitor Redis connection
- **External Services**: Monitor third-party API connectivity

### Monitoring & Logging

#### Application Logs

- Logs are stored in the `logs/` directory
- Use structured logging with Winston
- Monitor error rates and response times

#### Performance Monitoring

- Monitor API response times
- Track database query performance
- Monitor memory and CPU usage
- Set up alerting for critical metrics

## 🤝 Contributing

### Contribution Guidelines

1. **Fork the Repository**
2. **Create Feature Branch**: `git checkout -b feature/amazing-feature`
3. **Make Changes**: Follow coding standards and guidelines
4. **Add Tests**: Ensure your changes are tested
5. **Commit Changes**: Use conventional commit messages
6. **Push to Branch**: `git push origin feature/amazing-feature`
7. **Create Pull Request**: Provide detailed description of changes

### Commit Message Format

```
type(scope): description

[optional body]

[optional footer]
```

**Types:**

- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

**Examples:**

```
feat(auth): add two-factor authentication
fix(api): resolve issue with order creation
docs(readme): update installation instructions
```


### Code Review Process

1. All changes must be reviewed by at least one team member
2. Ensure all tests pass before merging
3. Follow the style guide and linting rules
4. Update documentation when necessary
5. Consider security implications of changes

## 📞 Support

### Getting Help

- **Documentation**: Check this README and API documentation
- **Issues**: Report bugs and request features via GitHub Issues
- **Email Support**: [<EMAIL>](mailto:<EMAIL>)
- **Development Team**: Contact the development team for technical questions

### Troubleshooting Common Issues

#### Application Won't Start

1. Check environment variables are set correctly
2. Ensure MongoDB and Redis are running
3. Verify port availability
4. Check application logs for specific errors

#### Database Connection Issues

1. Verify MongoDB connection string
2. Check database server status
3. Ensure proper network connectivity
4. Verify authentication credentials

#### Authentication Problems

1. Check JWT secret configuration
2. Verify token expiry settings
3. Ensure Redis is accessible for session storage
4. Check user account status in database

#### API Errors

1. Check Swagger documentation for correct request format
2. Verify authentication headers
3. Ensure proper user permissions
4. Check rate limiting status

---

<div align="center">

**Built with ❤️ by the Valuit Team**

[Website](https://valuit.com) • [Documentation](https://docs.valuit.com) • [Support](mailto:<EMAIL>)

© 2025 Valuit. All rights reserved.
</div>
