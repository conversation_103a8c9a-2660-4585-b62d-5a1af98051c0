{"name": "Valuit Tokenization", "version": "0.0.1", "main": "./build/config/server/index.js", "description": "", "scripts": {"build": "tsc && npm run copy-ejs --skipLibCheck && npm run copy-proto --skipLibCheck && npm run copy-public --skipLibCheck", "copy-ejs": "cp -r ./src/utils/emailTemplate build/utils/emailTemplate", "copy-proto": "cp -r ./src/_grpc/proto build/_grpc/proto", "copy-public": "cp -r ./src/public build/public", "start": "node ./build/config/server/index.js", "sonar": "node sonar.js", "dev": "npm run format && nodemon ./src/server.ts", "start:dev": "concurrently \"nodemon ./build/config/server/index.js\"", "format": "npx prettier --write . './src/**/*.{ts,tsx,js,jsx,json}' && npx eslint --fix './src/**/*.{ts,tsx,js}'"}, "dependencies": {"@google-cloud/storage": "^7.12.1", "@grpc/grpc-js": "^1.12.2", "@grpc/proto-loader": "^0.7.13", "@sendgrid/mail": "^8.1.3", "@types/moment": "^2.13.0", "aws-sdk": "^2.1692.0", "axios": "^1.7.3", "bcrypt": "^5.1.1", "big.js": "^6.2.2", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "docusign-esign": "^8.0.1", "dotenv": "^16.4.5", "ejs": "^3.1.10", "ethers": "^6.13.4", "express": "^4.19.2", "express-rate-limit": "^7.5.0", "fs": "^0.0.1-security", "helmet": "^7.1.0", "i18n-iso-countries": "^7.13.0", "joi": "^17.13.1", "jsonwebtoken": "^9.0.2", "kafkajs": "^2.2.4", "libphonenumber-js": "^1.12.2", "moment": "^2.30.1", "mongoose": "^8.11.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "otplib": "^12.0.1", "qrcode": "^1.5.3", "redis": "^4.6.14", "socket.io": "^4.8.1", "swagger-ui-express": "^5.0.1", "twilio": "^5.3.2", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.20.0", "@types/bcrypt": "^5.0.2", "@types/big.js": "^6.2.2", "@types/chai": "^4.3.16", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.7", "@types/cors": "^2.8.17", "@types/debug": "^4.1.12", "@types/docusign-esign": "^5.19.7", "@types/ejs": "^3.1.5", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.6", "@types/mocha": "^10.0.6", "@types/multer": "^1.4.11", "@types/node": "^22.5.1", "@types/node-cron": "^3.0.11", "@types/qrcode": "^1.5.5", "@types/supertest": "^6.0.2", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@typescript-eslint/eslint-plugin": "^8.25.0", "@typescript-eslint/parser": "^8.25.0", "chai": "^5.1.1", "eslint": "^8.57.1", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.31.0", "globals": "^15.14.0", "jsdoc": "^4.0.3", "mocha": "^10.4.0", "nodemon": "^3.1.4", "prettier": "^3.4.2", "sonarqube-scanner": "^4.2.0", "supertest": "^7.0.0", "ts-node": "^10.9.2", "typescript": "^5.6.3", "typescript-eslint": "^8.24.0"}}