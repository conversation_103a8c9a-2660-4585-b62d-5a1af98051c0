import { Router } from 'express';
import * as jwtAuthenticated from '../config/middleware/jwtAuthenticated';
import { transactionsValidationReq } from '../middleware/transactions.middleware';
import { TransactionController } from '../component';

/**
 * Express router for blockchain transaction management and tracking.
 * Handles transaction history retrieval, status monitoring, and analytics
 * for tokenized asset operations. Provides comprehensive transaction data
 * for issuers to monitor token lifecycle events and regulatory compliance.
 *
 * @constant {Router} router - Express router instance for transaction management
 * @description Complete transaction tracking API for tokenized asset monitoring
 *
 * @middleware Global authentication required for all routes via jwtAuthenticated.isAuthenticated
 *
 * // Get transactions for specific offering
 * GET /v1/transactions/507f1f77bcf86cd799439011?page=1&limit=20&status=MINTED
 *
 * // Filter transactions by type and date
 * GET /v1/transactions/507f1f77bcf86cd799439011?status=FREEZE&startDate=2024-01-01
 * ```
 */
const router: Router = Router();

/**
 * Global authentication middleware applied to all transaction routes.
 * Ensures all transaction operations require valid JWT authentication.
 *
 * @middleware jwtAuthenticated.isAuthenticated - Validates JWT token and user session
 */
router.use(jwtAuthenticated.isAuthenticated);

/**
 * Retrieves comprehensive transaction history for a specific tokenized offering.
 * Provides detailed blockchain transaction data including token operations,
 * status changes, and user activities for offering management and compliance reporting.
 * Supports advanced filtering, pagination, and search capabilities.
 *
 * @route GET /transactions/:offeringId
 * @middleware jwtAuthenticated.isIssuerCompleted - Ensures issuer has completed verification process
 * @middleware transactionsValidationReq - Validates transaction query parameters
 * @handler TransactionController.getTransactions - Retrieves offering transaction history
 * @requires Authentication - Valid JWT token required
 * @requires Authorization - Completed issuer verification required
 *
 * @description
 * Transaction retrieval features:
 * - Complete transaction history for specific offering
 * - Filter by transaction type (MINTED, FREEZE, UNFREEZE, CONVERT, TRANSFER)
 * - Filter by transaction status (PENDING, APPROVED, REJECTED, COMPLETED)
 * - Search by user name, email, or wallet address
 * - Paginated results with metadata
 * - Real-time transaction status updates
 * - Comprehensive audit trail for compliance
 *
 * Transaction types supported:
 * - MINTED: Token creation and issuance transactions
 * - FREEZE: Token freezing for compliance or security
 * - UNFREEZE: Token unfreezing and activation
 * - CONVERT: Token standard conversion operations
 * - TRANSFER: Token transfer between addresses
 * - REDEEM: Token redemption and buyback operations
 *
 * @param {string} offeringId - Unique identifier of the tokenized offering
 * @query {number} [page=1] - Page number for pagination
 * @query {number} [limit=10] - Number of transactions per page
 * @query {string} [status] - Filter by transaction status or type
 * @query {string} [search] - Search term for user name or email
 * @query {string} [startDate] - Filter transactions from date (ISO format)
 * @query {string} [endDate] - Filter transactions to date (ISO format)
 * @query {string} [sortBy] - Sort field (createdAt, orderMinted, amount)
 * @query {string} [sortOrder] - Sort direction (asc, desc)
 * @query {boolean} [isCsv] - Export data in CSV format
 *
 * @returns {Object} 200 - Paginated transaction history with metadata
 * @returns {Object} 400 - Invalid offering ID or query parameters
 * @returns {Object} 401 - Invalid or expired authentication token
 * @returns {Object} 403 - Insufficient permissions or incomplete issuer setup
 * @returns {Object} 404 - Offering not found or access denied
 *
 * // Response includes complete transaction data:
 * {
 *   "status": 200,
 *   "message": "Transactions retrieved successfully",
 *   "data": {
 *     "transactions": [
 *       {
 *         "_id": "507f1f77bcf86cd799439012",
 *         "userId": "507f1f77bcf86cd799439013",
 *         "name": "John Doe",
 *
 * // Filter by transaction type
 * GET /v1/transactions/507f1f77bcf86cd799439011?status=FREEZE
 *
 * // Search transactions by user
 *
 * // Export to CSV
 * GET /v1/transactions/507f1f77bcf86cd799439011?isCsv=true
 *
 * // Date range filtering
 * GET /v1/transactions/507f1f77bcf86cd799439011?startDate=2024-01-01&endDate=2024-01-31
 * ```
 */
router.get('/transactions/:offeringId', jwtAuthenticated.isIssuerCompleted, transactionsValidationReq, TransactionController.getTransactions);

/**
 * Export the configured transactions router for use in the main application.
 * This router handles all transaction-related operations and should be mounted
 * at the appropriate base path for transaction management APIs.
 *
 * @exports {Router} router - Configured Express router with all transaction routes
 */
export default router;
