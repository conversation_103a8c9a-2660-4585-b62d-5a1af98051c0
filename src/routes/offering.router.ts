import { Router } from 'express';
import { offeringComponent } from '../component';
import {
  createOfferingsValidationReq,
  updateOfferingsValidationReq,
  getOrdersFromOfferingIdValidationReq,
  subscribeValidationReq,
  updateWhitelistedOffering,
  offeringReportValidationReq,
  topOfferingValidationReq,
  offeringListValidationReq,
} from '../middleware';
import { isIssuerCompleted, isKycCompleted } from '../config/middleware/jwtAuthenticated';
import * as jwtAuthenticated from '../config/middleware/jwtAuthenticated';

/**
 * @constant {express.Router}
 */

const router: Router = Router();
router.use(jwtAuthenticated.isAuthenticated);

/**
 * Create a new offering
 * @param {Request} req
 * @param {Response} res
 */
router.post('/', isKycCompleted, isIssuerCompleted, createOfferingsValidationReq, offeringComponent.createOffering);

/**
 * Update an existing offering
 * @param {Request} req
 * @param {Response} res
 */
router.patch('/update-offering', isKycCompleted, isIssuerCompleted, updateOfferingsValidationReq, offeringComponent.updateOffering);

/**
 * Get list of offerings for a user
 * @param {Request} req
 * @param {Response} res
 */
router.get('/', offeringListValidationReq, offeringComponent.getUserOfferingList);
/**
 * Get price history
 * @param {Request} req
 * @param {Response} res
 */
router.get('/nav-history/:id', offeringComponent.getNavHistory);

/**
 * market offerings
 * @param {Request} req
 * @param {Response} res
 */
router.get('/primary-market-offerings', offeringComponent.primaryMarketOfferings);

/**
 * Request for an offering to be added to the platform
 * @param {Request} req
 * @param {Response} res
 */
router.post('/request-for-Offering', isIssuerCompleted, offeringComponent.requestOffering);

router.get('/requested-offerings', offeringComponent.requestedOfferings);

/**
 * Duplicate an offering
 * @param {Request} req
 * @param {Response} res
 */
router.post('/duplicate-offering/:offeringId', isIssuerCompleted, offeringComponent.duplicateOffering);

/**
 * Subscribe to an offering
 * @param {Request} req
 * @param {Response} res
 */
router.post('/subscribe', isKycCompleted, subscribeValidationReq, offeringComponent.subscribe);

/**
 * Get list of subscribers for an offering
 * @param {Request} req
 * @param {Response} res
 */
router.get('/subscribers/:id', offeringComponent.getOfferingSubscriberList);

/**
 * Get list of orders for an offering
 * @param {Request} req
 * @param {Response} res
 */
router.get('/order/:offeringId', getOrdersFromOfferingIdValidationReq, offeringComponent.getOrdersFromOfferingId);

/**
 * Get list of offerings a user has invested in
 * @param {Request} req
 * @param {Response} res
 */
router.get('/invested-offering', offeringComponent.investedOffering);

/**
 * Update an offering's whitelist status
 * @param {Request} req
 * @param {Response} res
 */
router.put('/whitelist-status', isIssuerCompleted, updateWhitelistedOffering, offeringComponent.rejectWalletWhiteList);

/**
 * Get total count of offerings
 * @param {Request} req
 * @param {Response} res
 */
router.get('/total-count', isIssuerCompleted, offeringComponent.getTotalCount);

/**
 * Get a report for a specific offering
 * @param {Request} req
 * @param {Response} res
 */
router.get('/report/:offeringId', isIssuerCompleted, offeringComponent.singleOfferingReport);
/**
 * Get top offerings
 * @param {Request} req
 * @param {Response} res
 */
router.get('/top-offering', isIssuerCompleted, topOfferingValidationReq, offeringComponent.getTopOffering);

/**
 * Get a report for all offerings
 * @param {Request} req
 * @param {Response} res
 */
router.get('/report', isIssuerCompleted, offeringReportValidationReq, offeringComponent.offeringReport);

/**
 * Get details of an offering
 * @param {Request} req
 * @param {Response} res
 */
router.get('/:id', offeringComponent.getOfferingDetails);

/**
 * Delete an offering
 * @param {Request} req
 * @param {Response} res
 */
router.delete('/:id', isIssuerCompleted, offeringComponent.deleteOffering);

/**
 * @export {express.Router}
 */

export default router;
