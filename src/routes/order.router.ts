import { Router } from 'express';
import * as jwtAuthenticated from '../config/middleware/jwtAuthenticated';
import { OrderComponent } from '../component';
import { createOrderValidationReq, getOrdersByUserValidationReq, getOrderByIdValidationReq, getPortfolioPerformanceValidationReq, exportUserOrdersValidationReq, rejectOrderValidationReq } from '../middleware/order.middleware';

const router: Router = Router();

// Ensure the user is authenticated before accessing the routes
router.use(jwtAuthenticated.isAuthenticated);

// Create a new order with validation
router.post('/create', createOrderValidationReq, OrderComponent.createOrder);

// Get all orders by user ID with query validation (pagination)
router.get('/', getOrdersByUserValidationReq, OrderComponent.getOrdersByUser);

router.get('/csv', exportUserOrdersValidationReq, OrderComponent.getOrdersInCsv);

// get top holdings
router.get('/topHoldings', getOrdersByUserValidationReq, OrderComponent.getTopHoldings);

router.get('/portfolio-report', getPortfolioPerformanceValidationReq, OrderComponent.portfolioPerformance);

// Get a specific order by order ID with validation
router.get('/:orderId', getOrderByIdValidationReq, OrderComponent.getOrderById);

// update order status
router.put('/reject', jwtAuthenticated.isIssuerCompleted, rejectOrderValidationReq, OrderComponent.rejectOrder);

export default router;
