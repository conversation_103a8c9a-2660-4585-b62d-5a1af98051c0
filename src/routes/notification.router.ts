import { Router } from 'express';
import * as jwtAuthenticated from '../config/middleware/jwtAuthenticated';
import { getNotification, seenNotification } from '../middleware';
import { Notification } from '../component/index';

/**
 * @constant {express.Router}
 */

const router: Router = Router();
router.use(jwtAuthenticated.isAuthenticated);

router.get('/', getNotification, Notification.getOfferingNotification);
router.post('/seen', seenNotification, Notification.seenOfferingNotification);

export default router;
