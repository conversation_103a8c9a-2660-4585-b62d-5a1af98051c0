import { Router } from 'express';
import { isIssuerCompleted, isKyc<PERSON>ompleted } from '../config/middleware/jwtAuthenticated';
import * as jwtAuthenticated from '../config/middleware/jwtAuthenticated';
import { createRedeemReq, validateGetRedeemReq } from '../middleware/redeem.middleware';
import { RedeemController } from '../component';

/**
 * Express router for token redemption request management and liquidity operations.
 * Handles token buyback requests, redemption processing, and liquidity management
 * for tokenized asset investments. Provides comprehensive redemption workflow
 * for both investors seeking liquidity and issuers managing token supply.
 *
 * @constant {Router} router - Express router instance for redemption management
 * @description Complete token redemption API for tokenized asset liquidity
 *
 * @middleware Global authentication required for all routes via jwtAuthenticated.isAuthenticated
 *
 * // Create redemption request (investor)
 * POST /v1/redeem
 * Content-Type: application/json
 *
 * // Get user redemption requests
 * GET /v1/redeem/507f1f77bcf86cd799439011
 *
 * // Get issuer redemption orders (issuer only)
 * GET /v1/redeem/orders/507f1f77bcf86cd799439011
 * ```
 */
const router: Router = Router();

/**
 * Global authentication middleware applied to all redemption routes.
 * Ensures all redemption operations require valid JWT authentication for security.
 *
 * @middleware jwtAuthenticated.isAuthenticated - Validates JWT token and user session
 */
router.use(jwtAuthenticated.isAuthenticated);

/**
 * Creates a new token redemption request for liquidity and token buyback.
 * Allows verified investors to request redemption of their tokenized asset holdings
 * with comprehensive validation, pricing calculations, and approval workflow initiation.
 * Validates investor eligibility, token holdings, and redemption policy compliance.
 *
 * @route POST /
 * @middleware isKycCompleted - Ensures investor has completed KYC verification
 * @middleware createRedeemReq - Validates redemption request data and parameters
 * @handler RedeemController.createRedeemRequest - Processes redemption request creation
 * @requires Authentication - Valid JWT token required
 * @requires Authorization - Completed KYC verification required
 *
 * @description
 * Redemption request creation features:
 * - Comprehensive token holding verification and balance checking
 * - Market pricing and valuation calculations for redemption amount
 * - Lock-up period and redemption restriction validation
 * - Liquidity pool and funding source verification
 * - Automated approval workflow based on redemption policies
 * - Stakeholder notification system (investor, issuer, administrators)
 *
 * Validation features:
 * - Token balance verification for requested redemption quantity
 * - Minimum and maximum redemption amount compliance
 * - Lock-up period and transfer restriction checking
 * - Market conditions and liquidity availability assessment
 * - Fee calculation including redemption and processing charges
 * - Regulatory compliance and tax implications review
 *
 * Workflow initiation:
 * - Immediate confirmation to requesting investor
 * - Issuer notification for review and approval
 * - Automated pricing and valuation updates
 * - Processing timeline communication
 * - Status tracking and progress monitoring
 *
 * @body {Object} redemptionRequest - Token redemption request data
 * @body {string} redemptionRequest.offeringId - ID of offering containing tokens to redeem
 * @body {string} redemptionRequest.quantity - Number of tokens to redeem
 * @body {string} redemptionRequest.redemptionType - Type of redemption (PARTIAL, FULL, EMERGENCY)
 * @body {string} [redemptionRequest.reason] - Reason for redemption request
 * @body {string} [redemptionRequest.preferredPaymentMethod] - Preferred payment method for proceeds
 * @body {string} [redemptionRequest.bankDetails] - Bank account details for payment
 * @body {string} [redemptionRequest.notes] - Additional notes or special instructions
 *
 * @returns {Object} 201 - Redemption request created successfully
 * @returns {Object} 400 - Invalid request data or validation errors
 * @returns {Object} 401 - Invalid or expired authentication token
 * @returns {Object} 403 - KYC not completed or redemption restrictions
 * @returns {Object} 404 - Offering not found or no token holdings
 * @returns {Object} 409 - Insufficient token balance or lock-up period active
 *
 * {
 *   "offeringId": "507f1f77bcf86cd799439011",
 *   "quantity": "500",
 *   "redemptionType": "PARTIAL",
 *   "reason": "Portfolio rebalancing and liquidity needs",
 *   "preferredPaymentMethod": "BANK_TRANSFER",
 *   "bankDetails": {
 *     "accountNumber": "**********",
 *     "routingNumber": "*********",
 *     "accountHolder": "John Doe"
 *   },
 *   "notes": "Please process within standard timeframe"
 * }
 *
 * // Response:
 * {
 *   "status": 201,
 *   "message": "Redemption request created successfully",
 *   "data": {
 *     "redemptionId": "507f1f77bcf86cd799439016",
 *     "status": "PENDING_REVIEW",
 *     "estimatedValue": "25000.00",
 *     "redemptionFees": "250.00",
 *     "netProceeds": "24750.00",
 *     "estimatedProcessingTime": "5-7 business days",
 *     "requiresApproval": true
 *   }
 * }
 * ```
 */
router.post('/', isKycCompleted, createRedeemReq, RedeemController.createRedeemRequest);

/**
 * Retrieves all redemption requests for a specific user with detailed status tracking.
 * Provides comprehensive redemption history and status information for investor
 * portfolio management and liquidity tracking. Includes detailed request information,
 * processing status, and timeline updates for transparency.
 *
 * @route GET /:id
 * @middleware validateGetRedeemReq - Validates redemption retrieval parameters
 * @handler RedeemController.getUserRedeemRequests - Retrieves user's redemption requests
 * @requires Authentication - Valid JWT token required
 *
 * @description
 * User redemption retrieval features:
 * - Complete redemption request history for authenticated user
 * - Real-time status tracking and processing updates
 * - Detailed financial information including valuations and fees
 * - Timeline tracking with processing milestones
 * - Document links and communication history
 * - Performance analytics and liquidity patterns
 *
 * @param {string} id - User ID to retrieve redemption requests for
 * @query {number} [page=1] - Page number for pagination
 * @query {number} [limit=10] - Number of requests per page
 * @query {string} [status] - Filter by redemption status
 * @query {string} [offeringId] - Filter by specific offering
 * @query {string} [startDate] - Filter requests from date (ISO format)
 * @query {string} [endDate] - Filter requests to date (ISO format)
 *
 * @returns {Object} 200 - User redemption requests with detailed information
 * @returns {Object} 400 - Invalid user ID or query parameters
 * @returns {Object} 401 - Invalid or expired authentication token
 * @returns {Object} 403 - Access denied (not user's own requests)
 * @returns {Object} 404 - No redemption requests found for user
 *
 * // Response includes redemption history:
 * {
 *   "status": 200,
 *   "data": {
 *     "redemptions": [
 *       {
 *         "redemptionId": "507f1f77bcf86cd799439016",
 *         "offeringId": "507f1f77bcf86cd799439011",
 *         "offeringName": "Downtown Office Complex",
 *         "quantity": "500",
 *         "redemptionType": "PARTIAL",
 *         "status": "PENDING_REVIEW",
 *         "requestedAt": "2024-01-20T10:00:00.000Z",
 *         "estimatedValue": "25000.00",
 *         "redemptionFees": "250.00",
 *         "netProceeds": "24750.00",
 *         "estimatedCompletion": "2024-01-27T17:00:00.000Z"
 *       }
 *     ],
 *     "summary": {
 *       "totalRedemptions": 3,
 *       "totalValue": "75000.00",
 *       "pendingRedemptions": 1,
 *       "completedRedemptions": 2
 *     },
 *     "pagination": {
 *       "currentPage": 1,
 *       "totalPages": 1,
 *       "totalItems": 3
 *     }
 *   }
 * }
 * ```
 */
router.get('/:id', validateGetRedeemReq, RedeemController.getUserRedeemRequests);

/**
 * Retrieves all redemption orders for issuer review and management.
 * Provides comprehensive redemption management dashboard for issuers including
 * approval workflows, liquidity management, and redemption analytics.
 * Supports issuer oversight of token redemption requests and processing.
 *
 * @route GET /orders/:id
 * @middleware isIssuerCompleted - Ensures issuer has completed verification process
 * @middleware validateGetRedeemReq - Validates redemption order retrieval parameters
 * @handler RedeemController.getRedeemRequests - Retrieves issuer redemption orders
 * @requires Authentication - Valid JWT token required
 * @requires Authorization - Completed issuer verification required
 *
 * @description
 * Issuer redemption management features:
 * - Complete view of all redemption requests for issuer's offerings
 * - Approval workflow management and processing tools
 * - Liquidity pool monitoring and funding source tracking
 * - Redemption analytics including volume and timing patterns
 * - Investor communication and status update tools
 * - Compliance reporting and regulatory documentation
 *
 * Management capabilities:
 * - Review and approve pending redemption requests
 * - Monitor liquidity requirements and funding availability
 * - Track redemption processing timelines and bottlenecks
 * - Analyze redemption patterns and investor behavior
 * - Generate compliance reports and audit documentation
 * - Manage fee structures and pricing calculations
 *
 * @param {string} id - Offering ID to retrieve redemption orders for
 * @query {number} [page=1] - Page number for pagination
 * @query {number} [limit=10] - Number of orders per page
 * @query {string} [status] - Filter by redemption status
 * @query {string} [redemptionType] - Filter by redemption type
 * @query {string} [investorId] - Filter by specific investor
 * @query {string} [sortBy] - Sort field (createdAt, quantity, value)
 * @query {string} [sortOrder] - Sort direction (asc, desc)
 *
 * @returns {Object} 200 - Issuer redemption orders with management data
 * @returns {Object} 400 - Invalid offering ID or query parameters
 * @returns {Object} 401 - Invalid or expired authentication token
 * @returns {Object} 403 - Insufficient permissions or incomplete issuer setup
 * @returns {Object} 404 - Offering not found or no redemption orders
 *
 * // Response includes issuer redemption dashboard:
 * {
 *   "status": 200,
 *   "data": {
 *     "redemptions": [
 *       {
 *         "redemptionId": "507f1f77bcf86cd799439016",
 *         "investorId": "507f1f77bcf86cd799439013",
 *         "investorName": "John Doe",
 *         "quantity": "500",
 *         "redemptionType": "PARTIAL",
 *         "status": "PENDING_REVIEW",
 *         "requestedAt": "2024-01-20T10:00:00.000Z",
 *         "estimatedValue": "25000.00",
 *         "redemptionFees": "250.00"
 *       }
 *     ],
 *     "analytics": {
 *       "totalRedemptionRequests": 15,
 *       "pendingApproval": 5,
 *       "totalRedemptionValue": "375000.00",
 *       "averageProcessingTime": "6.5 days",
 *       "redemptionRate": "12.5%"
 *     },
 *     "liquidity": {
 *       "availableFunds": "500000.00",
 *       "requiredForPending": "125000.00",
 *       "liquidityRatio": "75%"
 *     }
 *   }
 * }
 * ```
 */
router.get('/orders/:id', isIssuerCompleted, validateGetRedeemReq, RedeemController.getRedeemRequests);

/**
 * Export the configured redemption router for use in the main application.
 * This router handles all token redemption operations and should be mounted
 * at the appropriate base path for redemption management APIs.
 *
 * @exports {Router} router - Configured Express router with all redemption routes
 */
export default router;
