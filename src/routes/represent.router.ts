import { Router } from 'express';
import * as jwtAuthenticated from '../config/middleware/jwtAuthenticated';
import { Representative, Multisig } from '../component';
import { inviteRequestValidationReq, addUpdateRequestValidation } from '../middleware/representative.middleware';

/**
 * Express router for representative management and multi-signature wallet operations.
 * Handles representative invitation, authorization, and multi-signature wallet configuration
 * for enhanced security and governance in tokenized asset management. Provides comprehensive
 * delegation and authorization features for issuers and institutional operations.
 *
 * @constant {Router} router - Express router instance for representative management
 * @description Complete representative and multi-signature API for tokenized asset delegation
 *
 * @middleware Global authentication required for all routes via jwtAuthenticated.isAuthenticated
 *
 * // Invite representative (issuer only)
 * POST /v1/represent/invite
 * Content-Type: application/json
 *
 * // Configure multisig wallet (issuer only)
 * POST /v1/represent/multisig
 * Content-Type: application/json
 *
 * // Add multisig signature
 * PUT /v1/represent/addMultisig/507f1f77bcf86cd799439011
 * ```
 */
const router: Router = Router();

/**
 * Global authentication middleware applied to all representative routes.
 * Ensures all representative operations require valid JWT authentication for security.
 *
 * @middleware jwtAuthenticated.isAuthenticated - Validates JWT token and user session
 */
router.use(jwtAuthenticated.isAuthenticated);

/**
 * Invites a representative to manage tokenized asset operations with delegated authority.
 * Allows issuers to delegate specific operational responsibilities to authorized representatives
 * with defined permissions and access controls. Supports comprehensive invitation workflow
 * with role definition, permission assignment, and onboarding management.
 *
 * @route POST /invite
 * @middleware jwtAuthenticated.isIssuerCompleted - Ensures issuer has completed verification process
 * @middleware inviteRequestValidationReq - Validates representative invitation data and parameters
 * @handler Representative.invite - Processes representative invitation and authorization
 * @requires Authentication - Valid JWT token required
 * @requires Authorization - Completed issuer verification required
 *
 * @description
 * Representative invitation features:
 * - Comprehensive role definition and permission assignment
 * - Email-based invitation workflow with secure onboarding
 * - Multi-level authorization and access control configuration
 * - Delegation scope definition (specific offerings, operations, timeframes)
 * - Automated compliance checking and verification requirements
 * - Audit trail and activity monitoring for delegated actions
 *
 * Role types supported:
 * - Asset Manager: Property management and operational decisions
 * - Financial Representative: Financial reporting and dividend management
 * - Compliance Officer: Regulatory compliance and legal oversight
 * - Operations Manager: Day-to-day operational management
 * - Investment Advisor: Investment strategy and portfolio guidance
 *
 * Permission granularity:
 * - Offering-specific access and management rights
 * - Transaction approval and signing authority levels
 * - Reporting and analytics access permissions
 * - Investor communication and engagement rights
 * - Document access and management capabilities
 *
 * @body {Object} invitationData - Representative invitation information
 * @body {string} invitationData.email - Email address of representative to invite
 * @body {string} invitationData.name - Full name of representative
 * @body {string} invitationData.role - Representative role type (ASSET_MANAGER, FINANCIAL_REP, etc.)
 * @body {string[]} invitationData.permissions - Array of specific permissions to grant
 * @body {string[]} [invitationData.offeringIds] - Specific offerings to grant access to
 * @body {string} [invitationData.expirationDate] - Delegation expiration date (ISO format)
 * @body {string} [invitationData.notes] - Additional notes or instructions
 *
 * @returns {Object} 201 - Representative invitation sent successfully
 * @returns {Object} 400 - Invalid invitation data or validation errors
 * @returns {Object} 401 - Invalid or expired authentication token
 * @returns {Object} 403 - Insufficient permissions or incomplete issuer setup
 * @returns {Object} 409 - Representative already exists or invitation pending
 *
 * {
 *
 * // Response:
 * {
 *   "status": 201,
 *   "message": "Representative invitation sent successfully",
 *   "data": {
 *     "invitationId": "507f1f77bcf86cd799439017",
 *     "representativeId": "507f1f77bcf86cd799439018",
 *     "status": "PENDING",
 *     "invitationExpires": "2024-02-20T10:00:00.000Z",
 *     "emailSent": true
 *   }
 * }
 * ```
 */
router.post('/invite', jwtAuthenticated.isIssuerCompleted, inviteRequestValidationReq, Representative.invite);

/**
 * Retrieves all representatives associated with the issuer's offerings.
 * Provides comprehensive representative management dashboard including status,
 * permissions, activity tracking, and performance analytics for delegated operations.
 *
 * @route GET /invite
 * @middleware jwtAuthenticated.isIssuerCompleted - Ensures issuer has completed verification process
 * @handler Representative.getRepresentative - Retrieves issuer's representatives
 * @requires Authentication - Valid JWT token required
 * @requires Authorization - Completed issuer verification required
 *
 * @description
 * Representative management features:
 * - Complete list of invited and active representatives
 * - Role and permission tracking with access control details
 * - Activity monitoring and performance analytics
 * - Status management (pending, active, suspended, expired)
 * - Communication history and interaction tracking
 * - Audit trail for delegated actions and decisions
 *
 * @query {number} [page=1] - Page number for pagination
 * @query {number} [limit=10] - Number of representatives per page
 * @query {string} [status] - Filter by representative status (PENDING, ACTIVE, SUSPENDED)
 * @query {string} [role] - Filter by representative role type
 * @query {string} [offeringId] - Filter by specific offering access
 *
 * @returns {Object} 200 - Issuer representatives with management data
 * @returns {Object} 401 - Invalid or expired authentication token
 * @returns {Object} 403 - Insufficient permissions or incomplete issuer setup
 * @returns {Object} 404 - No representatives found
 *
 */
router.get('/invite', jwtAuthenticated.isIssuerCompleted, Representative.getRepresentative);

/**
 * Configures or updates multi-signature wallet settings for enhanced transaction security.
 * Allows issuers to set up multi-signature requirements for high-value transactions,
 * governance decisions, and critical operational activities with customizable approval thresholds.
 *
 * @route POST /multisig
 * @middleware jwtAuthenticated.isIssuerCompleted - Ensures issuer has completed verification process
 * @middleware addUpdateRequestValidation - Validates multisig configuration data
 * @handler Multisig.addOrUpdateMultisig - Processes multisig wallet configuration
 * @requires Authentication - Valid JWT token required
 * @requires Authorization - Completed issuer verification required
 *
 * @description
 * Multi-signature configuration features:
 * - Flexible signature threshold settings (M-of-N signature schemes)
 * - Authorized signer management with role-based permissions
 * - Transaction type and value threshold configuration
 * - Emergency procedures and backup signing mechanisms
 * - Comprehensive audit logging and signature verification
 * - Integration with hardware wallets and security devices
 *
 * Security enhancements:
 * - Prevent single points of failure in transaction approval
 * - Implement checks and balances for critical operations
 * - Ensure compliance with institutional security requirements
 * - Provide audit trail for all multi-signature activities
 * - Support emergency recovery and key rotation procedures
 *
 * @body {Object} multisigConfig - Multi-signature wallet configuration
 * @body {string} multisigConfig.walletAddress - Primary wallet address for multisig setup
 * @body {number} multisigConfig.requiredSignatures - Number of signatures required (M in M-of-N)
 * @body {string[]} multisigConfig.authorizedSigners - Array of authorized signer addresses
 * @body {string[]} multisigConfig.transactionTypes - Transaction types requiring multisig
 * @body {string} [multisigConfig.valueThreshold] - Minimum transaction value requiring multisig
 * @body {Object} [multisigConfig.emergencyProcedures] - Emergency access and recovery settings
 *
 * @returns {Object} 201 - Multi-signature wallet configured successfully
 * @returns {Object} 400 - Invalid configuration data or validation errors
 * @returns {Object} 401 - Invalid or expired authentication token
 * @returns {Object} 403 - Insufficient permissions or incomplete issuer setup
 * @returns {Object} 409 - Multisig configuration conflicts or already exists
 *
 * {
 *   "walletAddress": "0x742d35Cc6634C0532925a3b8D7389c5e8dc07C",
 *   "requiredSignatures": 2,
 *   "authorizedSigners": [
 *     "******************************************",
 *     "******************************************",
 *     "0x9f8e7d6c5b4a39281726354869708172635486"
 *   ],
 *   "transactionTypes": ["TOKEN_TRANSFER", "DIVIDEND_DISTRIBUTION", "GOVERNANCE_VOTE"],
 *   "valueThreshold": "10000.00",
 *   "emergencyProcedures": {
 *     "enableEmergencyAccess": true,
 *     "emergencySignerCount": 1,
 *     "emergencyTimeout": "72h"
 *   }
 * }
 * ```
 */
router.post('/multisig', jwtAuthenticated.isIssuerCompleted, addUpdateRequestValidation, Multisig.addOrUpdateMultisig);

/**
 * Retrieves multi-signature wallet configuration and status information.
 * Provides comprehensive multisig management dashboard including signer status,
 * pending transactions, signature requirements, and security analytics.
 *
 * @route GET /multisig
 * @middleware jwtAuthenticated.isIssuerCompleted - Ensures issuer has completed verification process
 * @handler Multisig.getRepresentative - Retrieves multisig wallet information
 * @requires Authentication - Valid JWT token required
 * @requires Authorization - Completed issuer verification required
 *
 * @description
 * Multi-signature information features:
 * - Current multisig wallet configuration and settings
 * - Authorized signer list with status and activity tracking
 * - Pending transactions requiring additional signatures
 * - Signature threshold and security policy details
 * - Transaction history and approval audit trail
 * - Security analytics and compliance reporting
 *
 * @returns {Object} 200 - Multi-signature wallet information
 * @returns {Object} 401 - Invalid or expired authentication token
 * @returns {Object} 403 - Insufficient permissions or incomplete issuer setup
 * @returns {Object} 404 - No multisig configuration found
 *
 */
router.get('/multisig', jwtAuthenticated.isIssuerCompleted, Multisig.getRepresentative);

/**
 * Adds a signature to a pending multi-signature transaction for approval completion.
 * Allows authorized signers to provide their cryptographic signature for pending
 * transactions that require multi-signature approval according to configured policies.
 *
 * @route PUT /addMultisig/:id
 * @handler Multisig.addMultisig - Processes signature addition to multisig transaction
 * @requires Authentication - Valid JWT token required (verified signer)
 *
 * @description
 * Signature addition features:
 * - Cryptographic signature verification and validation
 * - Automatic transaction execution upon threshold completion
 * - Comprehensive audit logging for signature events
 * - Real-time notification of signature status updates
 * - Anti-replay and signature uniqueness validation
 * - Integration with hardware wallets and signing devices
 *
 * Security validations:
 * - Verify signer authorization and permissions
 * - Validate cryptographic signature authenticity
 * - Ensure transaction hasn't been modified since creation
 * - Check signature threshold requirements and completion
 * - Prevent duplicate signatures from same signer
 * - Audit all signature events for compliance
 *
 * @param {string} id - Multisig transaction ID requiring signature
 * @body {Object} signatureData - Cryptographic signature information
 * @body {string} signatureData.signature - Cryptographic signature string
 * @body {string} signatureData.signerAddress - Address of the signing wallet
 * @body {string} [signatureData.hardwareWalletId] - Hardware wallet identifier if applicable
 *
 * @returns {Object} 200 - Signature added successfully
 * @returns {Object} 400 - Invalid signature or transaction data
 * @returns {Object} 401 - Invalid or expired authentication token
 * @returns {Object} 403 - Unauthorized signer or insufficient permissions
 * @returns {Object} 404 - Multisig transaction not found
 * @returns {Object} 409 - Signature already provided or transaction completed
 *
 * {
 *   "signature": "0x1234567890abcdef...",
 *   "signerAddress": "******************************************",
 *   "hardwareWalletId": "ledger_nano_x_001"
 * }
 *
 * // Response when threshold reached:
 * {
 *   "status": 200,
 *   "message": "Signature added and transaction executed",
 *   "data": {
 *     "transactionId": "507f1f77bcf86cd799439019",
 *     "signaturesReceived": 2,
 *     "requiredSignatures": 2,
 *     "status": "EXECUTED",
 *     "txHash": "0xabc123def456...",
 *     "executedAt": "2024-01-20T15:30:00.000Z"
 *   }
 * }
 * ```
 */
router.put('/addMultisig/:id', Multisig.addMultisig);

/**
 * Export the configured representative router for use in the main application.
 * This router handles all representative and multi-signature operations and should be mounted
 * at the appropriate base path for representative management APIs.
 *
 * @exports {Router} router - Configured Express router with all representative routes
 */
export default router;
