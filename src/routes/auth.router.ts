import { Router } from 'express';
import { authComponent } from '../component';
import { validateEmailReq, validateVerificationReq, validateLoginReq, validateSignUpReq, socialLoginValidationReq, resendOtpValidationReq, validateResetPasswordReq, verify2FAReq, tokenValidationReq } from '../middleware';

/**
 * Express router for user authentication and account management endpoints.
 * Provides comprehensive authentication flow including registration, login, verification,
 * password management, and two-factor authentication (2FA) functionality.
 * All routes are publicly accessible without authentication requirements.
 *
 * @constant {Router} router - Express router instance for authentication routes
 * @description Public authentication endpoints for user account management
 *
 * // Available endpoints:
 * // POST /v1/signup - User registration
 * // POST /v1/login - User authentication
 * // POST /v1/verify - Account verification
 * // POST /v1/forgot-password - Password reset request
 * // POST /v1/reset-password - Password reset confirmation
 * ```
 */
const router: Router = Router();

/**
 * Authentication API Module
 * @description This module contains all routes related to user authentication, registration,
 * verification, and account security. Includes comprehensive validation middleware
 * and integration with external authentication providers.
 *
 * Security features:
 * - Input validation and sanitization
 * - Rate limiting for sensitive operations
 * - Email and SMS verification support
 * - Two-factor authentication (2FA)
 * - Secure password reset workflows
 * - Social media authentication integration
 */

/**
 * User registration endpoint for creating new user accounts.
 * Handles complete user signup process including email validation, password hashing,
 * and initial verification setup. Supports multiple verification methods.
 *
 * @route POST /signup
 * @middleware validateSignUpReq - Validates registration data including email format, password strength, and required fields
 * @handler authComponent.signUp - Processes user registration and sends verification
 *
 * @description
 * Registration process:
 * 1. Validates user input (email, password, phone, etc.)
 * 2. Checks for existing accounts with same email/phone
 * 3. Hashes password securely
 * 4. Creates user account with pending verification status
 * 5. Sends verification OTP via chosen method (email/SMS)
 * 6. Returns success response with next steps
 *
 * @body {Object} registrationData - User registration information
 * @body {string} registrationData.email - User's email address (required, must be valid)
 * @body {string} registrationData.password - User's password (required, must meet strength requirements)
 * @body {string} registrationData.confirmPassword - Password confirmation (must match password)
 * @body {string} registrationData.name - User's full name (required)
 * @body {string} registrationData.mobile - User's mobile number (required for SMS verification)
 * @body {string} registrationData.countryCode - Country code for mobile number (required)
 * @body {string} registrationData.otpMethods - Verification method ('email', 'sms', or 'both')
 * @body {string} [registrationData.userType] - User type ('investor', 'issuer', 'representative')
 *
 * @returns {Object} 201 - Registration successful with verification instructions
 * @returns {Object} 400 - Validation errors or registration failures
 * @returns {Object} 409 - Email or phone number already exists
 *
 * {
 *
 * // Success response
 * {
 *   "status": 201,
 *   "error": false,
 *   "message": "Registration successful. Please verify your email account.",
 *   "data": {
 *     "userId": "507f1f77bcf86cd799439011",
 */
router.post('/signup', validateSignUpReq, authComponent.signUp);

/**
 * User authentication endpoint for secure login and session management.
 * Handles user credential verification, rate limiting, and token generation.
 * Supports both email and mobile number authentication.
 *
 * @route POST /login
 * @middleware validateLoginReq - Validates login credentials and enforces rate limiting
 * @handler authComponent.login - Processes authentication and generates tokens
 *
 * @description
 * Login process:
 * 1. Validates user credentials (email/mobile and password)
 * 2. Checks account status and verification
 * 3. Verifies password against stored hash
 * 4. Applies rate limiting for failed attempts
 * 5. Generates JWT access and refresh tokens
 * 6. Updates last login timestamp
 * 7. Returns authentication tokens and user data
 *
 * @body {Object} credentials - User login credentials
 * @body {string} credentials.userName - Email address or mobile number for login
 * @body {string} credentials.password - User's password
 * @body {boolean} [credentials.rememberMe] - Extended session flag
 *
 * @returns {Object} 200 - Authentication successful with tokens and user data
 * @returns {Object} 400 - Invalid credentials or validation errors
 * @returns {Object} 401 - Authentication failed or account locked
 * @returns {Object} 403 - Account not verified or suspended
 * @returns {Object} 429 - Too many failed login attempts
 *
 * {
 *
 * // Success response
 * {
 *   "status": 200,
 *   "error": false,
 *   "message": "Login successful",
 *   "data": {
 *     "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
 *     "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
 *     "user": {
 *       "id": "507f1f77bcf86cd799439011",
 */
router.post('/login', validateLoginReq, authComponent.login);

/**
 * Social media authentication endpoint for third-party login integration.
 * Supports OAuth authentication with major social media platforms including
 * Google, Facebook, LinkedIn, and other supported providers.
 *
 * @route POST /social-login
 * @middleware socialLoginValidationReq - Validates social login tokens and provider data
 * @handler authComponent.socialLogin - Processes social authentication and account linking
 *
 * @description
 * Social login process:
 * 1. Validates social media authentication token
 * 2. Retrieves user profile from social provider
 * 3. Checks for existing account with same email
 * 4. Creates new account or links to existing account
 * 5. Generates JWT tokens for platform access
 * 6. Returns authentication response
 *
 * @body {Object} socialData - Social media authentication data
 * @body {string} socialData.provider - Social media provider ('google', 'facebook', 'linkedin')
 * @body {string} socialData.accessToken - OAuth access token from provider
 * @body {string} [socialData.idToken] - OAuth ID token (for Google)
 * @body {Object} [socialData.profile] - User profile data from provider
 *
 * @returns {Object} 200 - Social authentication successful with platform tokens
 * @returns {Object} 400 - Invalid social token or provider data
 * @returns {Object} 401 - Social authentication failed
 * @returns {Object} 409 - Account exists with different provider
 *
 * {
 *   "provider": "google",
 *   "accessToken": "ya29.a0AfH6SMC...",
 *   "idToken": "eyJhbGciOiJSUzI1NiIs...",
 *   "profile": {
 *     "email": "<EMAIL>",
 *     "name": "John Doe",
 *     "picture": "https://lh3.googleusercontent.com/..."
 *   }
 * }
 * ```
 */
router.post('/social-login', socialLoginValidationReq, authComponent.socialLogin);

/**
 * Account verification endpoint for confirming user email and mobile number.
 * Handles OTP verification for account activation and security confirmation.
 * Supports both email and SMS-based verification methods.
 *
 * @route POST /verify
 * @middleware validateVerificationReq - Validates OTP code and user identification
 * @handler authComponent.verify - Processes verification and activates account
 *
 * @description
 * Verification process:
 * 1. Validates OTP code format and expiration
 * 2. Matches OTP against stored verification code
 * 3. Checks verification attempt limits
 * 4. Activates user account upon successful verification
 * 5. Clears verification codes and temporary data
 * 6. Returns confirmation with account status
 *
 * @body {Object} verificationData - Account verification information
 * @body {string} verificationData.email - User's email address for identification
 * @body {string} verificationData.otp - Verification code received via email/SMS
 * @body {string} [verificationData.verificationType] - Type of verification ('email', 'sms', 'login')
 *
 * @returns {Object} 200 - Verification successful, account activated
 * @returns {Object} 400 - Invalid or expired OTP code
 * @returns {Object} 401 - Too many verification attempts
 * @returns {Object} 404 - User not found or already verified
 *
 * {
 *
 * // Success response
 * {
 *   "status": 200,
 *   "error": false,
 *   "message": "Account verified successfully",
 *   "data": {
 *     "verified": true,
 *     "accountStatus": "active"
 *   }
 * }
 * ```
 */
router.post('/verify', validateVerificationReq, authComponent.verify);

/**
 * OTP resend endpoint for requesting new verification codes.
 * Allows users to request fresh verification codes when previous codes expire
 * or are not received. Includes rate limiting to prevent abuse.
 *
 * @route POST /resend-otp
 * @middleware resendOtpValidationReq - Validates resend request and enforces cooldown periods
 * @handler authComponent.reSendOtp - Generates and sends new OTP codes
 *
 * @description
 * OTP resend process:
 * 1. Validates user identification and resend eligibility
 * 2. Checks cooldown period since last OTP send
 * 3. Generates new secure OTP code
 * 4. Sends OTP via requested method (email/SMS)
 * 5. Updates rate limiting counters
 * 6. Returns confirmation of OTP delivery
 *
 * @body {Object} resendData - OTP resend request information
 * @body {string} resendData.email - User's email address for identification
 * @body {string} [resendData.otpMethod] - Preferred delivery method ('email', 'sms')
 * @body {string} [resendData.requestType] - Type of OTP request ('verification', 'login', 'reset')
 *
 * @returns {Object} 200 - New OTP sent successfully
 * @returns {Object} 400 - Invalid request or user data
 * @returns {Object} 429 - Rate limit exceeded, too many requests
 * @returns {Object} 404 - User not found
 *
 * {
 */
router.post('/resend-otp', resendOtpValidationReq, authComponent.reSendOtp);

/**
 * Password reset request endpoint for initiating secure password recovery.
 * Handles forgotten password scenarios by sending secure reset links or codes.
 * Includes security measures to prevent unauthorized password resets.
 *
 * @route POST /forgot-password
 * @middleware validateEmailReq - Validates email format and user existence
 * @handler authComponent.forgotPassword - Initiates password reset process
 *
 * @description
 * Password reset initiation:
 * 1. Validates email address format and existence
 * 2. Checks account status and verification
 * 3. Generates secure password reset token
 * 4. Sends reset instructions via email
 * 5. Sets expiration time for reset token
 * 6. Logs security event for audit trail
 *
 * @body {Object} resetRequest - Password reset request data
 * @body {string} resetRequest.email - User's email address for password reset
 *
 * @returns {Object} 200 - Password reset instructions sent
 * @returns {Object} 400 - Invalid email format
 * @returns {Object} 404 - Email address not found
 * @returns {Object} 429 - Too many reset requests
 *
 * {
 *
 * // Success response
 * {
 *   "status": 200,
 *   "error": false,
 *   "message": "Password reset instructions sent to your email",
 *   "data": {
 *     "resetInitiated": true,
 *     "expiresIn": "15 minutes"
 *   }
 * }
 * ```
 */
router.post('/forgot-password', validateEmailReq, authComponent.forgotPassword);

/**
 * Password reset confirmation endpoint for completing password recovery.
 * Handles the actual password update using secure reset tokens.
 * Validates token authenticity and enforces password policies.
 *
 * @route POST /reset-password
 * @middleware validateResetPasswordReq - Validates reset token and new password requirements
 * @handler authComponent.resetPassword - Completes password reset process
 *
 * @description
 * Password reset completion:
 * 1. Validates reset token authenticity and expiration
 * 2. Verifies new password meets security requirements
 * 3. Checks password history to prevent reuse
 * 4. Hashes new password securely
 * 5. Updates user password and invalidates reset token
 * 6. Sends confirmation email and logs security event
 *
 * @body {Object} resetData - Password reset confirmation data
 * @body {string} resetData.token - Secure password reset token from email
 * @body {string} resetData.newPassword - New password (must meet requirements)
 * @body {string} resetData.confirmPassword - Password confirmation (must match)
 *
 * @returns {Object} 200 - Password reset completed successfully
 * @returns {Object} 400 - Invalid token, expired, or password validation errors
 * @returns {Object} 401 - Invalid or expired reset token
 * @returns {Object} 422 - Password does not meet requirements
 *
 * {
 *   "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
 *   "newPassword": "NewSecurePass123!",
 *   "confirmPassword": "NewSecurePass123!"
 * }
 * ```
 */
router.post('/reset-password', validateResetPasswordReq, authComponent.resetPassword);

/**
 * Two-factor authentication verification endpoint for enhanced login security.
 * Handles 2FA code verification during login process for users with 2FA enabled.
 * Supports TOTP (Time-based One-Time Password) authentication.
 *
 * @route POST /verify-2fa
 * @middleware verify2FAReq - Validates 2FA code format and user session
 * @handler authComponent.verifyLogin2FA - Processes 2FA verification and completes login
 *
 * @description
 * 2FA verification process:
 * 1. Validates 2FA code format and timing
 * 2. Verifies code against user's TOTP secret
 * 3. Checks for code replay attacks
 * 4. Completes login process upon successful verification
 * 5. Generates final authentication tokens
 * 6. Logs successful 2FA authentication
 *
 * @body {Object} twoFactorData - 2FA verification information
 * @body {string} twoFactorData.email - User's email for identification
 * @body {string} twoFactorData.code - 6-digit TOTP code from authenticator app
 * @body {string} [twoFactorData.tempToken] - Temporary token from initial login
 *
 * @returns {Object} 200 - 2FA verification successful, login completed
 * @returns {Object} 400 - Invalid 2FA code format
 * @returns {Object} 401 - Incorrect 2FA code or expired session
 * @returns {Object} 429 - Too many 2FA attempts
 *
 * {
 */
router.post('/verify-2fa', verify2FAReq, authComponent.verifyLogin2FA);

/**
 * Two-factor authentication disable endpoint for emergency access recovery.
 * Allows users to disable 2FA when they lose access to their authenticator device.
 * Requires existing authentication and additional security verification.
 *
 * @route PATCH /forgot-2fa
 * @middleware tokenValidationReq - Validates user authentication token
 * @handler authComponent.forgot2FA - Processes 2FA disable request with security checks
 *
 * @description
 * 2FA disable process:
 * 1. Validates user authentication and identity
 * 2. Performs additional security verification
 * 3. Sends security notification to user's email
 * 4. Disables 2FA for the user account
 * 5. Generates new backup codes
 * 6. Logs security event for audit trail
 *
 * @headers {string} Authorization - Bearer token for user authentication
 *
 * @returns {Object} 200 - 2FA disabled successfully with new backup codes
 * @returns {Object} 401 - Invalid or expired authentication token
 * @returns {Object} 403 - Insufficient permissions or security verification failed
 *
 * // Success response
 * {
 *   "status": 200,
 *   "error": false,
 *   "message": "2FA disabled successfully",
 *   "data": {
 *     "twoFactorEnabled": false,
 *     "backupCodes": ["code1", "code2", "..."]
 *   }
 * }
 * ```
 */
router.patch('/forgot-2fa', tokenValidationReq, authComponent.forgot2FA);

/**
 * Export the configured authentication router for use in the main application.
 * This router handles all public authentication endpoints and should be mounted
 * at the appropriate base path in the main Express application.
 *
 * @exports {Router} router - Configured Express router with all authentication routes
 */
export default router;
