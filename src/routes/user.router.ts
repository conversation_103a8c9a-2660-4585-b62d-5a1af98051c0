import { Router } from 'express';
import { authComponent } from '../component';
import { changePasswordReq, docsValidationReq, getProfileReq, getUserPortfolio, getUserProfileReq, tokenValidationReq, transferAgentListValidationReq, validateUpdateKycReq, validateUpdateProfileReq } from '../middleware';
import * as file from '../middleware/googleCloud.middleware';
import * as jwtAuthenticated from '../config/middleware/jwtAuthenticated';

/**
 * Express router for authenticated user operations and profile management.
 * Provides comprehensive user account management including profile updates, password changes,
 * KYC operations, document management, and portfolio access. All routes require authentication.
 *
 * @constant {Router} router - Express router instance for authenticated user routes
 * @description Protected user management endpoints requiring valid authentication tokens
 *
 * // Available endpoints (all require authentication):
 * // GET /v1/auth/profile - Get current user profile
 * // PUT /v1/auth/profile - Update user profile
 * // PATCH /v1/auth/change-password - Change password
 * // POST /v1/auth/upload-docs - Upload KYC documents
 * // GET /v1/auth/portfolio - Get user portfolio
 * ```
 */
const router: Router = Router();

/**
 * Global authentication middleware for all user routes.
 * Ensures all endpoints in this router require valid JWT authentication.
 * Validates tokens, extracts user information, and enforces access control.
 *
 * @middleware jwtAuthenticated.isAuthenticated - JWT token validation and user context setup
 */
router.use(jwtAuthenticated.isAuthenticated);

/**
 * Retrieves the current authenticated user's complete profile information.
 * Provides comprehensive user data including personal details, preferences, and account status.
 * Supports role-based data filtering and privacy controls.
 *
 * @route GET /profile
 * @middleware getProfileReq - Validates profile access permissions and request parameters
 * @handler authComponent.getUserProfile - Retrieves and formats user profile data
 * @requires Authentication - Valid JWT token required
 *
 * @description
 * Profile retrieval includes:
 * - Personal information (name, email, phone)
 * - Account status and verification details
 * - User preferences and settings
 * - Role and permission information
 * - KYC status and compliance data
 * - Portfolio summary (if applicable)
 *
 * @headers {string} Authorization - Bearer JWT token for authentication
 *
 * @returns {Object} 200 - User profile data successfully retrieved
 * @returns {Object} 401 - Invalid or expired authentication token
 * @returns {Object} 403 - Insufficient permissions to access profile
 * @returns {Object} 404 - User profile not found
 *
 * // Success response
 * {
 *   "status": 200,
 *   "error": false,
 *   "message": "Profile retrieved successfully",
 *   "data": {
 *     "id": "507f1f77bcf86cd799439011",
 */
router.get('/profile', getProfileReq, authComponent.getUserProfile);

/**
 * Retrieves detailed profile information for a specific user by their ID.
 * Provides user lookup functionality with privacy controls and role-based access.
 * Useful for admin operations, user directory, and social features.
 *
 * @route GET /profile/:id
 * @middleware getUserProfileReq - Validates user ID format and access permissions
 * @handler authComponent.getUserProfileDetails - Retrieves specified user profile with privacy filtering
 * @requires Authentication - Valid JWT token required
 * @requires Authorization - Appropriate permissions to view other user profiles
 *
 * @param {string} id - Target user's unique identifier (MongoDB ObjectId format)
 *
 * @description
 * User lookup features:
 * - Privacy-filtered profile information
 * - Role-based data access control
 * - Public vs private information filtering
 * - Admin-level detailed access
 * - Audit trail for profile access
 *
 * @headers {string} Authorization - Bearer JWT token for authentication
 *
 * @returns {Object} 200 - Target user profile data (filtered based on permissions)
 * @returns {Object} 401 - Invalid or expired authentication token
 * @returns {Object} 403 - Insufficient permissions to view user profile
 * @returns {Object} 404 - Target user not found
 * @returns {Object} 422 - Invalid user ID format
 *
 * // Success response (privacy-filtered)
 * {
 *   "status": 200,
 *   "error": false,
 *   "message": "User profile retrieved successfully",
 *   "data": {
 *     "id": "507f1f77bcf86cd799439011",
 *     "name": "John Doe",
 *     "userType": "investor",
 *     "joinedDate": "2024-01-01T00:00:00.000Z",
 *     "publicInfo": {
 *       "bio": "Experienced real estate investor",
 *       "location": "New York, USA"
 *     }
 *   }
 * }
 * ```
 */
router.get('/profile/:id', getUserProfileReq, authComponent.getUserProfileDetails);

/**
 * Updates the authenticated user's profile information with comprehensive validation.
 * Handles profile updates including personal details, preferences, and file uploads.
 * Supports partial updates and maintains data integrity with validation.
 *
 * @route PUT /profile
 * @middleware file.validateImageFiles - Validates and processes profile image uploads
 * @middleware validateUpdateProfileReq - Validates profile update data and permissions
 * @handler authComponent.updateProfile - Processes profile updates and file uploads
 * @requires Authentication - Valid JWT token required
 *
 * @description
 * Profile update capabilities:
 * - Personal information updates (name, phone, address)
 * - Profile image upload and management
 * - User preferences and settings
 * - Contact information updates
 * - Privacy settings configuration
 * - Notification preferences
 *
 * @body {Object} profileData - Profile update information (multipart/form-data for file uploads)
 * @body {string} [profileData.name] - Updated full name
 * @body {string} [profileData.mobile] - Updated mobile number
 * @body {string} [profileData.address] - Updated address information
 * @body {Object} [profileData.preferences] - Updated user preferences
 * @body {File} [profileData.profileImage] - Profile image file (JPEG, PNG, max 5MB)
 * @body {string} [profileData.bio] - User biography or description
 *
 * @headers {string} Authorization - Bearer JWT token for authentication
 * @headers {string} Content-Type - multipart/form-data for file uploads
 *
 * @returns {Object} 200 - Profile updated successfully with new data
 * @returns {Object} 400 - Validation errors or invalid file format
 * @returns {Object} 401 - Invalid or expired authentication token
 * @returns {Object} 413 - File size exceeds maximum limit
 * @returns {Object} 422 - Profile data validation errors
 *
 * FormData:
 * - name: "John Smith"
 * - mobile: "+19********"
 * - profileImage: [file]
 * - preferences: {"notifications": true}
 * ```
 */
router.put('/profile', file.validateImageFiles, validateUpdateProfileReq, authComponent.updateProfile);

/**
 * Changes the authenticated user's password with security validation.
 * Implements secure password change workflow with current password verification
 * and password history checking to prevent reuse.
 *
 * @route PATCH /change-password
 * @middleware changePasswordReq - Validates password change request and security requirements
 * @handler authComponent.changePassword - Processes password change with security checks
 * @requires Authentication - Valid JWT token required
 *
 * @description
 * Password change security features:
 * - Current password verification
 * - New password strength validation
 * - Password history checking (prevents reuse)
 * - Secure password hashing
 * - Session invalidation for security
 * - Email notification of password change
 * - Audit logging for security events
 *
 * @body {Object} passwordData - Password change information
 * @body {string} passwordData.currentPassword - User's current password for verification
 * @body {string} passwordData.newPassword - New password (must meet strength requirements)
 * @body {string} passwordData.confirmPassword - Password confirmation (must match newPassword)
 *
 * @headers {string} Authorization - Bearer JWT token for authentication
 *
 * @returns {Object} 200 - Password changed successfully
 * @returns {Object} 400 - Password validation errors or weak password
 * @returns {Object} 401 - Invalid current password or expired token
 * @returns {Object} 422 - Password reuse detected or policy violations
 *
 * {
 *   "currentPassword": "OldPassword123!",
 *   "newPassword": "NewSecurePass456!",
 *   "confirmPassword": "NewSecurePass456!"
 * }
 *
 * // Success response
 * {
 *   "status": 200,
 *   "error": false,
 *   "message": "Password changed successfully",
 *   "data": {
 *     "passwordUpdated": true,
 *     "sessionInvalidated": true
 *   }
 * }
 * ```
 */
router.patch('/change-password', changePasswordReq, authComponent.changePassword);

/**
 * Generates QR code and setup instructions for two-factor authentication (2FA).
 * Provides TOTP secret and configuration data for authenticator app setup.
 * Enables enhanced account security through time-based one-time passwords.
 *
 * @route GET /enable-2fa
 * @handler authComponent.enable2FA - Generates 2FA setup data and QR code
 * @requires Authentication - Valid JWT token required
 *
 * @description
 * 2FA setup process:
 * - Generates unique TOTP secret for user
 * - Creates QR code for easy authenticator app setup
 * - Provides manual entry key as backup
 * - Returns setup instructions and verification steps
 * - Maintains security during setup process
 *
 * @headers {string} Authorization - Bearer JWT token for authentication
 *
 * @returns {Object} 200 - 2FA setup data generated successfully
 * @returns {Object} 401 - Invalid or expired authentication token
 * @returns {Object} 409 - 2FA already enabled for this account
 *
 * // Success response
 * {
 *   "status": 200,
 *   "error": false,
 *   "message": "2FA setup data generated",
 *   "data": {
 *     "qrCodeUrl": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
 *     "manualEntryKey": "JBSWY3DPEHPK3PXP",
 *     "backupCodes": ["********", "********", "..."],
 *     "setupInstructions": "Scan QR code with authenticator app..."
 *   }
 * }
 * ```
 */
router.get('/enable-2fa', authComponent.enable2FA);

/**
 * Verifies and activates two-factor authentication for the user account.
 * Completes 2FA setup by verifying the first TOTP code from authenticator app.
 * Enables 2FA protection for future login attempts.
 *
 * @route POST /verify-2fa
 * @middleware tokenValidationReq - Validates authentication token and user context
 * @handler authComponent.verify2FA - Verifies 2FA code and enables 2FA protection
 * @requires Authentication - Valid JWT token required
 *
 * @description
 * 2FA activation process:
 * - Validates TOTP code from authenticator app
 * - Confirms 2FA secret is correctly configured
 * - Enables 2FA protection for the account
 * - Generates backup codes for emergency access
 * - Sends confirmation email about 2FA activation
 * - Updates user security settings
 *
 * @body {Object} verificationData - 2FA verification information
 * @body {string} verificationData.code - 6-digit TOTP code from authenticator app
 * @body {string} [verificationData.backupCode] - Alternative backup code for verification
 *
 * @headers {string} Authorization - Bearer JWT token for authentication
 *
 * @returns {Object} 200 - 2FA enabled successfully with backup codes
 * @returns {Object} 400 - Invalid 2FA code format
 * @returns {Object} 401 - Incorrect 2FA code or expired session
 * @returns {Object} 409 - 2FA already enabled
 *
 * {
 *   "code": "123456"
 * }
 *
 * // Success response
 * {
 *   "status": 200,
 *   "error": false,
 *   "message": "2FA enabled successfully",
 *   "data": {
 *     "twoFactorEnabled": true,
 *     "backupCodes": ["********", "********", "..."],
 *     "enabledAt": "2024-01-15T10:30:00.000Z"
 *   }
 * }
 * ```
 */
router.post('/verify-2fa', tokenValidationReq, authComponent.verify2FA);

/**
 * Disables two-factor authentication for the user account.
 * Removes 2FA protection with proper security verification and audit trail.
 * Requires additional verification to prevent unauthorized disabling.
 *
 * @route PATCH /disable-2fa
 * @middleware tokenValidationReq - Validates authentication token and user permissions
 * @handler authComponent.disable2FA - Disables 2FA with security verification
 * @requires Authentication - Valid JWT token required
 * @requires TwoFactorAuth - Current 2FA code or backup code for verification
 *
 * @description
 * 2FA disable process:
 * - Requires current 2FA code for verification
 * - Disables 2FA protection for the account
 * - Invalidates all backup codes
 * - Sends security notification email
 * - Logs security event for audit trail
 * - Updates user security settings
 *
 * @body {Object} disableData - 2FA disable verification
 * @body {string} disableData.code - Current 6-digit TOTP code or backup code
 * @body {string} [disableData.reason] - Optional reason for disabling 2FA
 *
 * @headers {string} Authorization - Bearer JWT token for authentication
 *
 * @returns {Object} 200 - 2FA disabled successfully
 * @returns {Object} 400 - Invalid 2FA code or missing verification
 * @returns {Object} 401 - Incorrect 2FA code or expired session
 * @returns {Object} 404 - 2FA not currently enabled
 *
 * {
 *   "code": "123456",
 *   "reason": "Switching to new authenticator app"
 * }
 * ```
 */
router.patch('/disable-2fa', tokenValidationReq, authComponent.disable2FA);

/**
 * Uploads KYC (Know Your Customer) documents for identity verification.
 * Handles secure document upload, validation, and processing for compliance.
 * Supports multiple document types and formats with comprehensive validation.
 *
 * @route POST /upload-docs
 * @middleware file.validateFiles - Validates document files (type, size, format)
 * @middleware docsValidationReq - Validates document upload request and metadata
 * @handler authComponent.uploadDocs - Processes document upload and initiates verification
 * @requires Authentication - Valid JWT token required
 *
 * @description
 * Document upload features:
 * - Multiple document type support (ID, passport, utility bills, etc.)
 * - File format validation (PDF, JPEG, PNG)
 * - File size limits and compression
 * - Secure cloud storage with encryption
 * - Automatic OCR and data extraction
 * - Document verification workflow initiation
 * - Compliance with regulatory requirements
 *
 * @body {FormData} documentData - Document upload data (multipart/form-data)
 * @body {File} documentData.frontIdCard - Front side of ID card/license
 * @body {File} documentData.backIdCard - Back side of ID card/license
 * @body {File} [documentData.passport] - Passport document
 * @body {File} [documentData.utilityBill] - Proof of address document
 * @body {File} [documentData.bankStatement] - Bank statement for verification
 * @body {string} documentData.documentType - Primary document type being uploaded
 * @body {string} [documentData.notes] - Additional notes about documents
 *
 * @headers {string} Authorization - Bearer JWT token for authentication
 * @headers {string} Content-Type - multipart/form-data
 *
 * @returns {Object} 200 - Documents uploaded successfully, verification initiated
 * @returns {Object} 400 - Invalid file format or missing required documents
 * @returns {Object} 401 - Invalid or expired authentication token
 * @returns {Object} 413 - File size exceeds maximum limit
 * @returns {Object} 422 - Document validation errors
 *
 * FormData:
 * - frontIdCard: [file]
 * - backIdCard: [file]
 * - utilityBill: [file]
 * - documentType: "national_id"
 * - notes: "Current address verification"
 * ```
 */
router.post('/upload-docs', file.validateFiles, docsValidationReq, authComponent.uploadDocs);

/**
 * Updates KYC (Know Your Customer) information and verification status.
 * Handles comprehensive KYC data updates including personal details,
 * address information, and verification status management.
 *
 * @route PUT /kyc
 * @middleware validateUpdateKycReq - Validates KYC update data and permissions
 * @handler authComponent.updateKyc - Processes KYC information updates
 * @requires Authentication - Valid JWT token required
 * @requires Authorization - Appropriate permissions for KYC updates
 *
 * @description
 * KYC update capabilities:
 * - Personal information updates
 * - Address and contact details
 * - Identity document information
 * - Verification status updates
 * - Compliance status tracking
 * - Risk assessment data
 * - Regulatory requirement fulfillment
 *
 * @body {Object} kycData - KYC information to update
 * @body {Object} [kycData.personalInfo] - Personal details (name, DOB, nationality)
 * @body {Object} [kycData.addressInfo] - Address and location information
 * @body {Object} [kycData.identityInfo] - Identity document details
 * @body {string} [kycData.verificationStatus] - KYC verification status
 * @body {Object} [kycData.complianceData] - Regulatory compliance information
 *
 * @headers {string} Authorization - Bearer JWT token for authentication
 *
 * @returns {Object} 200 - KYC information updated successfully
 * @returns {Object} 400 - Validation errors or invalid KYC data
 * @returns {Object} 401 - Invalid or expired authentication token
 * @returns {Object} 403 - Insufficient permissions for KYC updates
 * @returns {Object} 422 - KYC data validation errors
 *
 * {
 *   "personalInfo": {
 *     "fullName": "John Doe",
 *     "dateOfBirth": "1990-01-15",
 *     "nationality": "US"
 *   },
 *   "addressInfo": {
 *     "street": "123 Main St",
 *     "city": "New York",
 *     "state": "NY",
 *     "zipCode": "10001"
 *   },
 *   "verificationStatus": "PENDING"
 * }
 * ```
 */
router.put('/kyc', validateUpdateKycReq, authComponent.updateKyc);

/**
 * Route to log out the user.
 * @name logOut
 * @function
 * @inner
 * @param {Request} req - The incoming request.
 * @param {Response} res - The outgoing response.
 */
router.get('/log-out', authComponent.logOut);

/**
 * Route to become an issuer.
 * @name becomeIssuer
 * @function
 * @inner
 * @param {Request} req - The incoming request.
 * @param {Response} res - The outgoing response.
 */
router.patch('/become', authComponent.becomeIssuer);

/**
 * Route to disable OTP.
 * @name disableOTP
 * @function
 * @inner
 * @param {Request} req - The incoming request.
 * @param {Response} res - The outgoing response.
 */
router.post('/disable-otp', authComponent.disableOTP);

/**
 * Route to send verification OTP.
 * @name verification
 * @function
 * @inner
 * @param {Request} req - The incoming request.
 * @param {Response} res - The outgoing response.
 */
router.post('/send-verification-otp', authComponent.verification);

/**
 * Route to get the user's portfolio.
 * @name getUserPortfolio
 * @function
 * @inner
 * @param {Request} req - The incoming request.
 * @param {Response} res - The outgoing response.
 */
router.get('/portfolio', getUserPortfolio, authComponent.getUserPortfolio);

/**
 * Route to get the list of transfer agents.
 * @name getTransferAgent
 * @function
 * @inner
 * @param {Request} req - The incoming request.
 * @param {Response} res - The outgoing response.
 */
router.get('/transferagent', transferAgentListValidationReq, authComponent.getTransferAgent);

/**
 * Route to get the details of a transfer agent by their ID.
 * @name getTransferAgentDetails
 * @function
 * @inner
 * @param {Request} req - The incoming request.
 * @param {Response} res - The outgoing response.
 */
router.get('/transferagent/:transferAgentId', authComponent.getTransferAgentDetails);

/**
 * @export {express.Router}
 */

export default router;
