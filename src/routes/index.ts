import * as express from 'express';
import * as swaggerUi from 'swagger-ui-express';
import AuthRouter from './auth.router';
import UserRouter from './user.router';
import DocuSignRouter from './docusign.router';
import OfferingRouter from './offering.router';
import transactions from './transactions.router';
import redeem from './redeem.router';
import { RES_MSG, RESPONSES } from '../utils/responseUtils';
import { ResponseHandler } from '../helpers/response.helper';
import { swaggerDefinition } from '../utils/swaggerDef';
import KycRouter from './kyc.router';
import TransferRouter from './transfer.router';
import OrderRouter from './order.router';
import DividentsRouter from './dividends.router';
import ProposalRouter from './proposal.router';
import NotificationRouter from './notification.router';
import RepresentRouter from './represent.router';

/**
 * Initializes all application routes and middleware for the Express application.
 * Sets up versioned API routes, authentication-protected endpoints, health checks,
 * and comprehensive error handling. All routes are prefixed with '/v1' for API versioning.
 *
 * @export
 * @function init
 * @param {express.Application} app - The Express application instance to configure with routes
 * @returns {void}
 *
 * @description
 * Route Configuration:
 * - Authentication routes (login, signup, verification)
 * - KYC/KYB verification and compliance routes
 * - Order management and trading routes
 * - Dividend distribution and management routes
 * - Governance proposal and voting routes
 * - Real-time notification routes
 * - Token transfer and transaction routes
 * - DocuSign integration for document signing
 * - Asset offering and tokenization routes
 * - Representative and institutional user routes
 * - Redemption and withdrawal routes
 * - Health monitoring and system status
 * - API documentation via Swagger UI
 *
 */
export function init(app: express.Application): void {
  // Configure rate limiting
  // configureRateLimiting(app);

  /**
   * Create a base router with the "v1" prefix for API versioning.
   * All API endpoints will be accessible under /v1/ path.
   *
   * @constant {express.Router} router - The main API router instance
   */
  const router: express.Router = express.Router();

  /**
   * Request logging middleware that logs all incoming requests.
   * Helps with debugging and monitoring API usage patterns.
   *
   * @middleware
   * @param {express.Request} req - The incoming request object
   * @param {express.Response} res - The outgoing response object
   * @param {express.NextFunction} next - The next middleware function
   */
  app.use((req, res, next) => {
    //console.info(`Requested URL ====>>>>>>>>>>>>>> ${req.originalUrl}`);
    next();
  });

  /**
   * Authentication Router - Handles user authentication and authorization.
   * Includes endpoints for login, signup, password reset, OTP verification,
   * and session management. These routes are publicly accessible.
   *
   * @description Auth Router
   * @constructs
   * @param {express.Application} app - The express application
   * @returns {void}
   * @route {GET|POST} / - Various authentication endpoints
   */
  router.use('/', AuthRouter);

  /**
   * KYC/KYB Router - Handles Know Your Customer and Know Your Business verification.
   * Manages identity verification, document uploads, compliance checks,
   * and integration with SumSub verification service.
   *
   * @description Kyc Router
   * @constructs
   * @param {express.Application} app - The express application
   * @returns {void}
   * @route {GET|POST|PUT} / - KYC verification and status endpoints
   */
  router.use('/', KycRouter);

  /**
   * Order Router - Handles trading orders and order management.
   * Manages buy/sell orders, order status tracking, order matching,
   * and integration with blockchain for order settlement.
   *
   * @description Order Router
   * @constructs
   * @param {express.Application} app - The express application
   * @returns {void}
   * @route {GET|POST|PUT|DELETE} /order - Order management endpoints
   */

  router.use('/order', OrderRouter);

  /**
   * Dividend Router - Handles dividend announcements and distributions.
   * Manages dividend calculations, distribution schedules, payment processing,
   * and investor notifications for tokenized asset returns.
   *
   * @description Dividend Router
   * @constructs
   * @param {express.Application} app - The express application
   * @returns {void}
   * @route {GET|POST|PUT} /dividend - Dividend management endpoints
   */
  router.use('/dividend', DividentsRouter);

  /**
   * Proposal Router - Handles governance proposals and voting.
   * Manages proposal creation, voting mechanisms, vote counting,
   * and execution of approved governance decisions.
   *
   * @description proposal Router
   * @constructs
   * @param {express.Application} app - The express application
   * @returns {void}
   * @route {GET|POST|PUT} /proposal - Governance proposal endpoints
   */
  router.use('/proposal', ProposalRouter);

  /**
   * Notification Router - Handles real-time notifications and messaging.
   * Manages user notifications, system alerts, email/SMS delivery,
   * and notification preferences. Requires authentication.
   *
   * @description notification Router
   * @constructs
   * @param {express.Application} app - The express application
   * @returns {void}
   * @route {GET|POST|PUT|DELETE} /auth/notification - Notification endpoints
   */
  router.use('/auth/notification', NotificationRouter);

  /**
   * User Router - Handles authenticated user operations and profile management.
   * Manages user profiles, settings, preferences, account management,
   * and user-specific data operations. All routes require authentication.
   *
   * @description Authenticated user routes
   * @route {GET|POST|PUT|DELETE} /auth - User management endpoints
   */
  router.use('/auth', UserRouter);

  /**
   * Transfer Router - Handles token transfers and transaction management.
   * Manages peer-to-peer token transfers, transfer approvals, whitelist validation,
   * and blockchain transaction processing. Requires authentication.
   *
   * @description Token transfer Router
   * @constructs
   * @param {express.Application} app - The express application
   * @returns {void}
   * @route {GET|POST|PUT} /auth/transfer - Token transfer endpoints
   */
  router.use('/auth/transfer', TransferRouter);

  /**
   * DocuSign Router - Handles electronic document signing integration.
   * Manages document preparation, signing workflows, signature collection,
   * and integration with DocuSign eSignature API. Requires authentication.
   *
   * @description Docusign Router
   * @constructs
   * @param {express.Application} app - The express application
   * @returns {void}
   * @route {GET|POST|PUT} /auth/docusign - Document signing endpoints
   */
  router.use('/auth/docusign', DocuSignRouter);

  /**
   * Offering Router - Handles asset tokenization and offering management.
   * Manages offering creation, deployment, investor onboarding, token minting,
   * and offering lifecycle management. Requires authentication.
   *
   * @description offering Router
   * @constructs
   * @param {express.Application} app - The express application
   * @returns {void}
   * @route {GET|POST|PUT|DELETE} /auth/offering - Asset offering endpoints
   */
  router.use('/auth/offering', OfferingRouter);

  /**
   * Transactions Router - Handles transaction history and blockchain interactions.
   * Manages transaction tracking, blockchain integration, wallet operations,
   * and transaction status monitoring. Requires authentication.
   *
   * @description Transactions Router
   * @constructs
   * @param {express.Application} app - The express application
   * @returns {void}
   * @route {GET|POST} /auth - Transaction management endpoints
   */
  router.use('/auth', transactions);

  /**
   * Redeem Router - Handles token redemption and asset liquidation.
   * Manages redemption requests, approval workflows, asset liquidation,
   * and payment processing for token holders. Requires authentication.
   *
   * @description Redeem Router
   * @constructs
   * @param {express.Application} app - The express application
   * @returns {void}
   * @route {GET|POST|PUT} /auth/redeem - Token redemption endpoints
   */
  router.use('/auth/redeem', redeem);

  /**
   * Swagger API Documentation Routes.
   * Provides interactive API documentation using Swagger UI.
   * Accessible at /v1/api-docs for developers and integrators.
   *
   * @description Swagger Routes
   * @route {GET} /api-docs - Interactive API documentation
   */
  router.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDefinition));

  /**
   * Representative Router - Handles institutional representative management.
   * Manages representative user accounts, permissions, delegation,
   * and institutional user operations. Requires authentication.
   *
   * @description Representative Router
   * @constructs
   * @param {express.Application} app - The express application
   * @returns {void}
   * @route {GET|POST|PUT|DELETE} /auth/representative - Representative endpoints
   */
  router.use('/auth/representative', RepresentRouter);

  /**
   * Attach the base "v1" router to the app.
   * All API routes will be accessible under the /v1 prefix.
   */
  app.use('/v1', router);

  /**
   * Health Check Endpoint - Provides system health and status information.
   * Returns server status, timestamp, and basic system health indicators.
   * Used for monitoring, load balancing, and service discovery.
   *
   * @description HealthCheck
   * @constructs
   * @route {GET} /health-check - System health status endpoint
   * @returns {Object} Health status with timestamp and success indicator
   *
   */
  app.use('/health-check', (req, res) => {
    try {
      return ResponseHandler.success(res, { message: RES_MSG?.SUCCESS_MSG.HEALTH_CHECK, status: RESPONSES.SUCCESS, error: false, data: { timestamp: new Date() } });
    } catch (error: any) {
      return ResponseHandler.error(res, { message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true });
    }
  });

  /**
   * 404 Not Found Handler - Catches all unmatched routes.
   * Returns standardized error response for requests to non-existent endpoints.
   * Must be the last route handler to catch all unmatched requests.
   *
   * @description if page not found
   * @constructs
   * @middleware
   * @param {express.Request} req - The incoming request object
   * @param {express.Response} res - The outgoing response object
   * @returns {Object} 404 error response
   *
   */
  app.use((req, res) => {
    ResponseHandler.error(res, { error: true, message: RES_MSG.ERROR_MSG.PAGE_NOT_FOUND, status: RESPONSES.NOTFOUND });
  });
}
