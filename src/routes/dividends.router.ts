import { Router } from 'express';
import * as jwtAuthenticated from '../config/middleware/jwtAuthenticated';
import { Dividents } from '../component';
import * as file from '../middleware/googleCloud.middleware';
import { investorDividendHistoryValidationReq, DividendCalculateValidationReq, DividendFetchValidationReq, DividendInvestorFetchValidationReq, DividendValidationReq } from '../middleware/dividend.middleware';

/**
 * Express router for dividend distribution and management endpoints.
 * Handles all dividend-related operations including creation, calculation, distribution,
 * and history tracking for tokenized asset investments. Provides comprehensive
 * dividend management for both issuers and investors with proper authentication and validation.
 *
 * @constant {Router} router - Express router instance for dividend management
 * @description Complete dividend management API for tokenized asset returns
 *
 * @middleware Global authentication required for all routes via jwtAuthenticated.isAuthenticated
 *
 * // Create dividend request
 * POST /v1/dividends
 * Content-Type: multipart/form-data
 *
 * // Calculate dividends for offering
 * POST /v1/dividends/calculate
 * Content-Type: application/json
 *
 * // Get dividend history
 * GET /v1/dividends/investor-history?offeringId=123&page=1&limit=10
 * ```
 */
const router: Router = Router();

/**
 * Global authentication middleware applied to all dividend routes.
 * Ensures all dividend operations require valid JWT authentication.
 *
 * @middleware jwtAuthenticated.isAuthenticated - Validates JWT token and user session
 */
router.use(jwtAuthenticated.isAuthenticated);

/**
 * Creates a new dividend distribution request for a tokenized offering.
 * Allows issuers to announce and configure dividend payments to token holders.
 * Supports document upload for dividend announcements and legal documentation.
 * Validates dividend parameters and ensures proper offering ownership.
 *
 * @route POST /
 * @middleware file.validateImageFiles - Validates uploaded dividend documentation files
 * @middleware jwtAuthenticated.isIssuerCompleted - Ensures issuer has completed verification process
 * @middleware DividendValidationReq - Validates dividend request data and parameters
 * @handler Dividents.createDividentReq - Processes dividend request creation
 * @requires Authentication - Valid JWT token required
 * @requires Authorization - Completed issuer verification required
 *
 * @description
 * Dividend creation features:
 * - Configure dividend amount and distribution type
 * - Set record date and payment date for distributions
 * - Upload dividend announcement documents
 * - Validate offering ownership and token holder eligibility
 * - Calculate total distribution amounts and fees
 * - Schedule automated dividend processing
 *
 * @body {Object} dividendRequest - Dividend distribution configuration
 * @body {string} dividendRequest.offeringId - ID of offering for dividend distribution
 * @body {string} dividendRequest.amount - Total dividend amount to distribute
 * @body {string} dividendRequest.currency - Currency for dividend payment (USD, USDC, etc.)
 * @body {string} dividendRequest.recordDate - Record date for determining eligible holders
 * @body {string} dividendRequest.paymentDate - Scheduled payment date for distribution
 * @body {string} dividendRequest.type - Distribution type (cash, stock, special)
 * @body {string} [dividendRequest.description] - Optional dividend announcement description
 * @body {File[]} [files] - Optional dividend announcement documents
 *
 * @returns {Object} 201 - Dividend request created successfully
 * @returns {Object} 400 - Invalid request data or validation errors
 * @returns {Object} 401 - Invalid or expired authentication token
 * @returns {Object} 403 - Insufficient permissions or incomplete issuer setup
 * @returns {Object} 404 - Offering not found or access denied
 * @returns {Object} 409 - Dividend already exists for the specified period
 *
 * {
 *   "offeringId": "507f1f77bcf86cd799439011",
 *   "amount": "50000.00",
 *   "currency": "USDC",
 *   "recordDate": "2024-12-31T23:59:59.000Z",
 *   "paymentDate": "2025-01-15T12:00:00.000Z",
 *   "type": "quarterly",
 *   "description": "Q4 2024 dividend distribution"
 * }
 *
 * // Response:
 * {
 *   "status": 201,
 *   "message": "Dividend request created successfully",
 *   "data": {
 *     "dividendId": "507f1f77bcf86cd799439012",
 *     "totalHolders": 150,
 *     "estimatedPerShare": "333.33"
 *   }
 * }
 * ```
 */
router.post('/', file.validateImageFiles, jwtAuthenticated.isIssuerCompleted, DividendValidationReq, Dividents.createDividentReq);

/**
 * Retrieves all dividend requests with filtering and pagination support.
 * Provides comprehensive dividend management dashboard for issuers.
 * Supports filtering by status, offering, date range, and search functionality.
 * Returns detailed dividend information including distribution status and metrics.
 *
 * @route GET /
 * @middleware DividendFetchValidationReq - Validates query parameters and filters
 * @handler Dividents.getDividentReq - Retrieves paginated dividend requests
 * @requires Authentication - Valid JWT token required
 *
 * @description
 * Dividend retrieval features:
 * - Paginated dividend request listing
 * - Filter by status (pending, approved, distributed, cancelled)
 * - Filter by offering ID or date range
 * - Search by dividend description or offering name
 * - Sort by creation date, payment date, or amount
 * - Detailed distribution metrics and analytics
 *
 * @query {number} [page=1] - Page number for pagination
 * @query {number} [limit=10] - Number of results per page
 * @query {string} [status] - Filter by dividend status
 * @query {string} [offeringId] - Filter by specific offering
 * @query {string} [startDate] - Filter dividends from date (ISO format)
 * @query {string} [endDate] - Filter dividends to date (ISO format)
 * @query {string} [search] - Search term for dividend description
 * @query {string} [sortBy] - Sort field (createdAt, paymentDate, amount)
 * @query {string} [sortOrder] - Sort direction (asc, desc)
 *
 * @returns {Object} 200 - Paginated dividend requests with metadata
 * @returns {Object} 400 - Invalid query parameters or validation errors
 * @returns {Object} 401 - Invalid or expired authentication token
 *
 * // Response:
 * {
 *   "status": 200,
 *   "data": {
 *     "dividends": [
 *       {
 *         "id": "507f1f77bcf86cd799439012",
 *         "offeringId": "507f1f77bcf86cd799439011",
 *         "offeringName": "Downtown Office Complex",
 *         "amount": "50000.00",
 *         "currency": "USDC",
 *         "status": "pending",
 *         "recordDate": "2024-12-31T23:59:59.000Z",
 *         "paymentDate": "2025-01-15T12:00:00.000Z",
 *         "totalHolders": 150,
 *         "createdAt": "2024-12-01T10:00:00.000Z"
 *       }
 *     ],
 *     "pagination": {
 *       "currentPage": 1,
 *       "totalPages": 3,
 *       "totalItems": 45,
 *       "hasNext": true,
 *       "hasPrev": false
 *     }
 *   }
 * }
 * ```
 */
router.get('/', DividendFetchValidationReq, Dividents.getDividentReq);

/**
 * Retrieves dividend information specifically for investor stakeholders.
 * Provides investor-focused dividend data including eligibility and distribution details.
 * Shows historical and upcoming dividends for investor portfolio management.
 * Includes per-share calculations and projected earnings information.
 *
 * @route GET /investors
 * @middleware DividendInvestorFetchValidationReq - Validates investor query parameters
 * @handler Dividents.investors - Retrieves investor-focused dividend information
 * @requires Authentication - Valid JWT token required
 * @requires Authorization - Completed issuer verification (for accessing investor data)
 *
 * @description
 * Investor dividend features:
 * - Token holder dividend eligibility information
 * - Per-share dividend calculations and projections
 * - Historical dividend payment records
 * - Upcoming dividend announcements and schedules
 * - Portfolio-level dividend analytics and summaries
 * - Tax reporting information for distributed dividends
 *
 * @query {string} [offeringId] - Filter by specific offering
 * @query {string} [investorId] - Filter by specific investor (admin only)
 * @query {number} [page=1] - Page number for pagination
 * @query {number} [limit=10] - Number of results per page
 * @query {string} [status] - Filter by dividend status
 * @query {string} [year] - Filter by tax year for reporting
 *
 * @returns {Object} 200 - Investor dividend information with portfolio data
 * @returns {Object} 400 - Invalid query parameters or validation errors
 * @returns {Object} 401 - Invalid or expired authentication token
 * @returns {Object} 403 - Insufficient permissions for investor data access
 *
 * // Response:
 * {
 *   "status": 200,
 *   "data": {
 *     "investors": [
 *       {
 *         "investorId": "507f1f77bcf86cd799439013",
 *         "name": "John Doe",
 */
router.get('/investors', DividendInvestorFetchValidationReq, Dividents.investors);

/**
 * Calculates dividend distribution amounts and validates distribution parameters.
 * Performs complex calculations for dividend allocation based on token holdings.
 * Validates distribution feasibility and provides detailed breakdown of payments.
 * Accounts for fees, taxes, and regulatory requirements in calculations.
 *
 * @route POST /calculate
 * @middleware jwtAuthenticated.isIssuerCompleted - Ensures issuer has completed verification process
 * @middleware DividendCalculateValidationReq - Validates calculation request parameters
 * @handler Dividents.calculateDividend - Processes dividend calculations
 * @requires Authentication - Valid JWT token required
 * @requires Authorization - Completed issuer verification required
 *
 * @description
 * Dividend calculation features:
 * - Precise per-share dividend amount calculations
 * - Token holder eligibility validation based on record date
 * - Fee and tax withholding calculations
 * - Minimum distribution threshold validation
 * - Gas fee estimation for blockchain distributions
 * - Regulatory compliance checking for distribution limits
 *
 * @body {Object} calculationRequest - Dividend calculation parameters
 * @body {string} calculationRequest.offeringId - ID of offering for calculation
 * @body {string} calculationRequest.totalAmount - Total dividend pool to distribute
 * @body {string} calculationRequest.recordDate - Record date for holder eligibility
 * @body {string} [calculationRequest.excludeHolders] - Array of holder IDs to exclude
 * @body {boolean} [calculationRequest.includeFees] - Whether to include fee calculations
 * @body {string} [calculationRequest.currency] - Distribution currency
 *
 * @returns {Object} 200 - Detailed dividend calculation results
 * @returns {Object} 400 - Invalid calculation parameters or validation errors
 * @returns {Object} 401 - Invalid or expired authentication token
 * @returns {Object} 403 - Insufficient permissions or incomplete issuer setup
 * @returns {Object} 404 - Offering not found or access denied
 *
 * {
 *   "offeringId": "507f1f77bcf86cd799439011",
 *   "totalAmount": "100000.00",
 *   "recordDate": "2024-12-31T23:59:59.000Z",
 *   "includeFees": true,
 *   "currency": "USDC"
 * }
 *
 * // Response:
 * {
 *   "status": 200,
 *   "data": {
 *     "calculation": {
 *       "totalDistribution": "100000.00",
 *       "totalTokens": "10000",
 *       "perShareAmount": "10.00",
 *       "eligibleHolders": 145,
 *       "totalHolderTokens": "9500",
 *       "estimatedGasFees": "150.00",
 *       "platformFees": "500.00",
 *       "netDistribution": "99350.00",
 *       "holders": [
 *         {
 *           "holderId": "507f1f77bcf86cd799439013",
 *           "tokens": "1000",
 *           "dividendAmount": "10000.00",
 *           "eligible": true
 *         }
 *       ]
 *     }
 *   }
 * }
 * ```
 */
router.post('/calculate', jwtAuthenticated.isIssuerCompleted, DividendCalculateValidationReq, Dividents.calculateDividend);

/**
 * Retrieves dividend payment history for individual investors.
 * Provides comprehensive dividend history for investor portfolio tracking.
 * Includes tax reporting information and payment status details.
 * Supports filtering by offering, date range, and payment status.
 *
 * @route GET /investor-history
 * @middleware investorDividendHistoryValidationReq - Validates history request parameters
 * @handler Dividents.dividendHistory - Retrieves investor dividend history
 * @requires Authentication - Valid JWT token required
 *
 * @description
 * Dividend history features:
 * - Complete dividend payment history for authenticated investor
 * - Per-offering dividend breakdown and analytics
 * - Tax year summaries for reporting purposes
 * - Payment status tracking (pending, completed, failed)
 * - Yield calculations and performance metrics
 * - Export capabilities for tax filing
 *
 * @query {string} [offeringId] - Filter by specific offering
 * @query {number} [page=1] - Page number for pagination
 * @query {number} [limit=10] - Number of results per page
 * @query {string} [startDate] - Filter payments from date (ISO format)
 * @query {string} [endDate] - Filter payments to date (ISO format)
 * @query {string} [status] - Filter by payment status
 * @query {string} [year] - Filter by specific tax year
 *
 * @returns {Object} 200 - Investor dividend history with analytics
 * @returns {Object} 400 - Invalid query parameters or validation errors
 * @returns {Object} 401 - Invalid or expired authentication token
 * @returns {Object} 404 - No dividend history found for investor
 *
 * // Response:
 * {
 *   "status": 200,
 *   "data": {
 *     "history": [
 *       {
 *         "dividendId": "507f1f77bcf86cd799439012",
 *         "offeringId": "507f1f77bcf86cd799439011",
 *         "offeringName": "Downtown Office Complex",
 *         "paymentDate": "2024-01-15T12:00:00.000Z",
 *         "amount": "1250.00",
 *         "currency": "USDC",
 *         "status": "completed",
 *         "txHash": "0xabc123...",
 *         "tokenHoldings": "125",
 *         "perShareAmount": "10.00"
 *       }
 *     ],
 *     "summary": {
 *       "totalDividendsReceived": "15000.00",
 *       "totalPayments": 12,
 *       "averageYield": "7.8%",
 *       "taxYear": "2024"
 *     },
 *     "pagination": {
 *       "currentPage": 1,
 *       "totalPages": 2,
 *       "totalItems": 12
 *     }
 *   }
 * }
 * ```
 */
router.get('/investor-history', investorDividendHistoryValidationReq, Dividents.dividendHistory);

/**
 * Export the configured dividends router for use in the main application.
 * This router handles all dividend-related operations and should be mounted
 * at the appropriate base path for dividend management APIs.
 *
 * @exports {Router} router - Configured Express router with all dividend routes
 */
export default router;
