import { Router } from 'express';
import * as jwtAuthenticated from '../config/middleware/jwtAuthenticated';
import TransferRequestController from '../component/transfer';
import { createTransferRequestValidationReq, getTransferRequestsByofferingValidationReq, getTransferRequestsValidationReq, rejectTransferRequestsValidationReq } from '../middleware/transfer.middleware';

/**
 * Express router for token transfer request management and processing.
 * Handles token transfer operations between investor wallets including validation,
 * approval workflows, and compliance checking. Provides comprehensive transfer
 * management for both regular transfers and forced compliance transfers.
 *
 * @constant {Router} router - Express router instance for transfer management
 * @description Complete token transfer API for tokenized asset mobility
 *
 * @middleware Global authentication required for all routes via jwtAuthenticated.isAuthenticated
 *
 * // Create transfer request
 * POST /v1/transfer
 * Content-Type: application/json
 *
 * // Get transfer requests for offering
 * GET /v1/transfer/507f1f77bcf86cd799439011?page=1&limit=10
 *
 * // Reject transfer request
 * PUT /v1/transfer/reject
 * ```
 */
const router: Router = Router();

/**
 * Global authentication middleware applied to all transfer routes.
 * Ensures all transfer operations require valid JWT authentication for security.
 *
 * @middleware jwtAuthenticated.isAuthenticated - Validates JWT token and user session
 */
router.use(jwtAuthenticated.isAuthenticated);

/**
 * Creates a new token transfer request with comprehensive validation and workflow initiation.
 * Allows token holders to initiate transfer requests to other verified wallet addresses.
 * Validates transfer eligibility, compliance requirements, and wallet address verification.
 * Initiates approval workflow based on transfer type and regulatory requirements.
 *
 * @route POST /
 * @middleware createTransferRequestValidationReq - Validates transfer request data and parameters
 * @handler TransferRequestController.createTransferRequest - Processes transfer request creation
 * @requires Authentication - Valid JWT token required
 *
 * @description
 * Transfer request creation features:
 * - Validate token holder eligibility and balance
 * - Verify destination wallet address and whitelist status
 * - Determine transfer type (regular vs force transfer)
 * - Apply compliance and regulatory checking
 * - Initiate appropriate approval workflow
 * - Send notifications to relevant stakeholders
 *
 * Transfer types supported:
 * - Regular Transfer: Standard token transfer between verified addresses
 * - Force Transfer: Compliance-driven transfer for regulatory requirements
 * - Emergency Transfer: Security-related transfers for account protection
 *
 * Validation features:
 * - Token balance verification for transfer amount
 * - Destination address whitelist checking
 * - Lock-up period and transfer restriction validation
 * - Minimum transfer amount requirements
 * - Anti-money laundering (AML) compliance checks
 *
 * @body {Object} transferRequest - Transfer request data and parameters
 * @body {string} transferRequest.offeringId - ID of offering containing tokens to transfer
 * @body {string} transferRequest.fromAddress - Source wallet address (must match user's address)
 * @body {string} transferRequest.toAddress - Destination wallet address for tokens
 * @body {string} transferRequest.quantity - Number of tokens to transfer
 * @body {string} transferRequest.transferType - Type of transfer (regular, force, emergency)
 * @body {string} [transferRequest.reason] - Reason for transfer (required for force transfers)
 * @body {string} [transferRequest.notes] - Additional notes or documentation
 *
 * @returns {Object} 201 - Transfer request created successfully
 * @returns {Object} 400 - Invalid request data or validation errors
 * @returns {Object} 401 - Invalid or expired authentication token
 * @returns {Object} 403 - Insufficient token balance or transfer restrictions
 * @returns {Object} 404 - Offering not found or user not a token holder
 * @returns {Object} 409 - Destination address not whitelisted or verified
 *
 * {
 *   "offeringId": "507f1f77bcf86cd799439011",
 *   "fromAddress": "0x742d35Cc6634C0532925a3b8D7389c5e8dc07C",
 *   "toAddress": "******************************************",
 *   "quantity": "500",
 *   "transferType": "regular",
 *   "reason": "Portfolio rebalancing",
 *   "notes": "Transfer to family trust account"
 * }
 *
 * // Response:
 * {
 *   "status": 201,
 *   "message": "Transfer request created successfully",
 *   "data": {
 *     "transferId": "507f1f77bcf86cd799439014",
 *     "status": "PENDING_APPROVAL",
 *     "estimatedProcessingTime": "2-3 business days",
 *     "requiresApproval": true
 *   }
 * }
 * ```
 */
router.post('/', createTransferRequestValidationReq, TransferRequestController.createTransferRequest);

/**
 * Retrieves all transfer requests with comprehensive filtering and pagination.
 * Provides transfer management dashboard for administrators and issuers.
 * Supports filtering by status, offering, date range, and search functionality.
 * Includes detailed transfer information and approval workflow status.
 *
 * @route GET /
 * @middleware getTransferRequestsValidationReq - Validates query parameters and filters
 * @handler TransferRequestController.getAllTransferRequests - Retrieves paginated transfer requests
 * @requires Authentication - Valid JWT token required
 *
 * @description
 * Transfer retrieval features:
 * - Comprehensive transfer request listing with pagination
 * - Filter by status (pending, approved, rejected, completed)
 * - Filter by transfer type and offering
 * - Search by user information or wallet addresses
 * - Sort by creation date, amount, or status
 * - Export capabilities for administrative reporting
 *
 * @query {number} [page=1] - Page number for pagination
 * @query {number} [limit=10] - Number of results per page
 * @query {string} [status] - Filter by transfer status
 * @query {string} [offeringId] - Filter by specific offering
 * @query {string} [transferType] - Filter by transfer type
 * @query {string} [search] - Search term for user or address information
 * @query {string} [startDate] - Filter transfers from date (ISO format)
 * @query {string} [endDate] - Filter transfers to date (ISO format)
 * @query {string} [sortBy] - Sort field (createdAt, quantity, status)
 * @query {string} [sortOrder] - Sort direction (asc, desc)
 *
 * @returns {Object} 200 - Paginated transfer requests with metadata
 * @returns {Object} 400 - Invalid query parameters or validation errors
 * @returns {Object} 401 - Invalid or expired authentication token
 *
 * // Response includes transfer request data:
 * {
 *   "status": 200,
 *   "data": {
 *     "transfers": [
 *       {
 *         "transferId": "507f1f77bcf86cd799439014",
 *         "offeringId": "507f1f77bcf86cd799439011",
 *         "fromAddress": "0x742d35Cc6634C0532925a3b8D7389c5e8dc07C",
 *         "toAddress": "******************************************",
 *         "quantity": "500",
 *         "status": "PENDING_APPROVAL",
 *         "transferType": "regular",
 *         "createdAt": "2024-01-15T10:00:00.000Z",
 *         "user": {
 *           "name": "John Doe",
 */
router.get('/', getTransferRequestsValidationReq, TransferRequestController.getAllTransferRequests);

/**
 * Rejects a pending transfer request with reason and notification workflow.
 * Allows authorized issuers to reject transfer requests that don't meet requirements.
 * Provides comprehensive rejection workflow with stakeholder notifications.
 * Maintains audit trail for compliance and regulatory reporting.
 *
 * @route PUT /reject
 * @middleware jwtAuthenticated.isIssuerCompleted - Ensures issuer has completed verification process
 * @middleware rejectTransferRequestsValidationReq - Validates rejection request data
 * @handler TransferRequestController.rejectTransferRequest - Processes transfer rejection
 * @requires Authentication - Valid JWT token required
 * @requires Authorization - Completed issuer verification required
 *
 * @description
 * Transfer rejection features:
 * - Validate issuer authority for transfer rejection
 * - Require detailed rejection reason for audit trail
 * - Send automatic notifications to requesting user
 * - Update transfer status and workflow state
 * - Maintain comprehensive compliance documentation
 * - Log rejection event for regulatory reporting
 *
 * Rejection reasons supported:
 * - Insufficient Documentation: Missing required compliance documents
 * - Address Not Verified: Destination address not properly verified
 * - Compliance Issues: AML or regulatory compliance concerns
 * - Technical Issues: Blockchain or technical processing problems
 * - Policy Violation: Transfer violates offering or platform policies
 *
 * @body {Object} rejectionRequest - Transfer rejection data and reason
 * @body {string} rejectionRequest.transferId - ID of transfer request to reject
 * @body {string} rejectionRequest.reason - Detailed reason for rejection
 * @body {string} rejectionRequest.category - Rejection category for classification
 * @body {string} [rejectionRequest.notes] - Additional administrative notes
 * @body {boolean} [rejectionRequest.notifyUser] - Whether to send user notification
 *
 * @returns {Object} 200 - Transfer request rejected successfully
 * @returns {Object} 400 - Invalid request data or validation errors
 * @returns {Object} 401 - Invalid or expired authentication token
 * @returns {Object} 403 - Insufficient permissions or incomplete issuer setup
 * @returns {Object} 404 - Transfer request not found
 * @returns {Object} 409 - Transfer request cannot be rejected (wrong status)
 *
 * {
 *   "transferId": "507f1f77bcf86cd799439014",
 *   "reason": "Destination address not verified",
 *   "category": "Address Not Verified",
 *   "notes": "Destination address failed KYC verification process",
 *   "notifyUser": true
 * }
 *
 * // Response:
 * {
 *   "status": 200,
 *   "message": "Transfer request rejected successfully",
 *   "data": {
 *     "transferId": "507f1f77bcf86cd799439014",
 *     "status": "REJECTED",
 *     "rejectedAt": "2024-01-16T14:30:00.000Z",
 *     "userNotified": true
 *   }
 * }
 * ```
 */
router.put('/reject', jwtAuthenticated.isIssuerCompleted, rejectTransferRequestsValidationReq, TransferRequestController.rejectTransferRequest);

/**
 * Retrieves all transfer requests for a specific tokenized offering.
 * Provides offering-specific transfer management for issuers and administrators.
 * Includes comprehensive transfer analytics and compliance reporting.
 * Supports detailed filtering and search capabilities within offering scope.
 *
 * @route GET /:offeringId
 * @middleware getTransferRequestsByofferingValidationReq - Validates offering-specific query parameters
 * @middleware jwtAuthenticated.isIssuerCompleted - Ensures issuer has completed verification process
 * @handler TransferRequestController.getAllTransferRequestsByOffering - Retrieves offering transfer requests
 * @requires Authentication - Valid JWT token required
 * @requires Authorization - Completed issuer verification with offering access
 *
 * @description
 * Offering transfer features:
 * - Complete transfer history for specific offering
 * - Token holder transfer pattern analysis
 * - Compliance reporting for regulatory requirements
 * - Transfer velocity and frequency analytics
 * - Address verification status tracking
 * - Approval workflow metrics and timing
 *
 * @param {string} offeringId - Unique identifier of the tokenized offering
 * @query {number} [page=1] - Page number for pagination
 * @query {number} [limit=10] - Number of results per page
 * @query {string} [status] - Filter by transfer status
 * @query {string} [transferType] - Filter by transfer type
 * @query {string} [search] - Search term for user or address information
 * @query {string} [sortBy] - Sort field (createdAt, quantity, status)
 * @query {string} [sortOrder] - Sort direction (asc, desc)
 *
 * @returns {Object} 200 - Offering-specific transfer requests with analytics
 * @returns {Object} 400 - Invalid offering ID or query parameters
 * @returns {Object} 401 - Invalid or expired authentication token
 * @returns {Object} 403 - Insufficient permissions or offering access denied
 * @returns {Object} 404 - Offering not found
 *
 * // Response includes offering-specific transfer data:
 * {
 *   "status": 200,
 *   "data": {
 *     "offeringId": "507f1f77bcf86cd799439011",
 *     "offeringName": "Downtown Office Complex",
 *     "transfers": [
 *       {
 *         "transferId": "507f1f77bcf86cd799439014",
 *         "fromAddress": "0x742d35Cc6634C0532925a3b8D7389c5e8dc07C",
 *         "toAddress": "******************************************",
 *         "quantity": "500",
 *         "status": "APPROVED",
 *         "transferType": "regular",
 *         "approvedAt": "2024-01-16T15:00:00.000Z"
 *       }
 *     ],
 *     "analytics": {
 *       "totalTransfers": 25,
 *       "pendingTransfers": 3,
 *       "approvedTransfers": 20,
 *       "rejectedTransfers": 2,
 *       "averageProcessingTime": "2.5 days"
 *     }
 *   }
 * }
 * ```
 */
router.get('/:offeringId', getTransferRequestsByofferingValidationReq, jwtAuthenticated.isIssuerCompleted, TransferRequestController.getAllTransferRequestsByOffering);

/**
 * Export the configured transfer router for use in the main application.
 * This router handles all token transfer operations and should be mounted
 * at the appropriate base path for transfer management APIs.
 *
 * @exports {Router} router - Configured Express router with all transfer routes
 */
export default router;
