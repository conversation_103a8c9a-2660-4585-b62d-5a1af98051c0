import { Router } from 'express';
import * as jwtAuthenticated from '../config/middleware/jwtAuthenticated';
import * as file from '../middleware/googleCloud.middleware';
import { investorCapValidationReq, ProposalValidationReq, ProposalVotingReq, getProposal, getProposalDetails, getInvestorProposal } from '../middleware/proposal.middleware';
import { proposals } from '../component/index';

/**
 * Express router for governance proposal management and voting system.
 * Handles proposal creation, voting workflows, investor participation caps,
 * and comprehensive governance analytics for tokenized asset decision-making.
 * Provides democratic governance features for token holders and issuers.
 *
 * @constant {Router} router - Express router instance for governance management
 * @description Complete governance and voting API for tokenized asset proposals
 *
 * @middleware Global authentication required for all routes via jwtAuthenticated.isAuthenticated
 *
 * // Create governance proposal (issuer)
 * POST /v1/proposal
 * Content-Type: multipart/form-data
 *
 * // Vote on proposal (investor)
 * POST /v1/proposal/investor/vote
 * Content-Type: application/json
 *
 * // Get proposal details
 * GET /v1/proposal/details?proposalId=123
 * ```
 */
const router: Router = Router();

/**
 * Global authentication middleware applied to all governance routes.
 * Ensures all governance operations require valid JWT authentication.
 *
 * @middleware jwtAuthenticated.isAuthenticated - Validates JWT token and user session
 */
router.use(jwtAuthenticated.isAuthenticated);

/**
 * Creates a new governance proposal for token holder voting.
 * Allows issuers to submit proposals for major decisions affecting tokenized offerings.
 * Supports document upload for proposal documentation and detailed voting information.
 * Validates proposal parameters and initiates democratic voting workflow.
 *
 * @route POST /
 * @middleware file.validateImageFiles - Validates uploaded proposal documentation files
 * @middleware ProposalValidationReq - Validates proposal creation data and parameters
 * @handler proposals.createProposal - Processes proposal creation and validation
 * @requires Authentication - Valid JWT token required
 * @requires Authorization - Issuer role required for proposal creation
 *
 * @description
 * Proposal creation features:
 * - Comprehensive proposal documentation and context
 * - File upload support for detailed proposal materials
 * - Voting timeline configuration (start date, end date)
 * - Quorum requirements and voting threshold settings
 * - Token holder eligibility and voting weight calculations
 * - Automated notification to eligible voters
 *
 * Proposal types supported:
 * - Asset Management: Property improvements, refinancing, strategic changes
 * - Financial Decisions: Dividend distributions, capital allocation, fee changes
 * - Governance Changes: Voting rules, management updates, policy modifications
 * - Strategic Initiatives: New investments, partnerships, major decisions
 *
 * @body {Object} proposalData - Governance proposal information
 * @body {string} proposalData.offeringId - ID of offering for which proposal is created
 * @body {string} proposalData.title - Clear and descriptive proposal title
 * @body {string} proposalData.description - Detailed proposal description and rationale
 * @body {string} proposalData.proposalType - Type of proposal (ASSET_MANAGEMENT, FINANCIAL, GOVERNANCE)
 * @body {string} proposalData.startDate - Voting start date and time (ISO format)
 * @body {string} proposalData.endDate - Voting end date and time (ISO format)
 * @body {number} proposalData.quorumPercentage - Required participation percentage for validity
 * @body {number} proposalData.approvalThreshold - Required approval percentage for passage
 * @body {File[]} [files] - Supporting documents and proposal materials
 *
 * @returns {Object} 201 - Governance proposal created successfully
 * @returns {Object} 400 - Invalid proposal data or validation errors
 * @returns {Object} 401 - Invalid or expired authentication token
 * @returns {Object} 403 - Insufficient permissions (not an issuer)
 * @returns {Object} 404 - Offering not found or access denied
 *
 * {
 *   "offeringId": "507f1f77bcf86cd799439011",
 *   "title": "Property Renovation and Upgrade Project",
 *   "description": "Proposal to invest $2M in modernizing building systems and common areas to increase property value and rental income",
 *   "proposalType": "ASSET_MANAGEMENT",
 *   "startDate": "2024-02-01T09:00:00.000Z",
 *   "endDate": "2024-02-15T17:00:00.000Z",
 *   "quorumPercentage": 25,
 *   "approvalThreshold": 66.7
 * }
 *
 * // Response:
 * {
 *   "status": 201,
 *   "message": "Governance proposal created successfully",
 *   "data": {
 *     "proposalId": "507f1f77bcf86cd799439015",
 *     "eligibleVoters": 145,
 *     "totalVotingPower": "10000",
 *     "votingStatus": "PENDING",
 *     "notificationsSent": true
 *   }
 * }
 * ```
 */
router.post('/', file.validateImageFiles, ProposalValidationReq, proposals.createProposal);

/**
 * Retrieves all governance proposals created by the issuer with status tracking.
 * Provides comprehensive proposal management dashboard for issuers including
 * voting progress, participation rates, and outcome analytics.
 *
 * @route GET /issuer
 * @middleware getProposal - Validates proposal query parameters
 * @handler proposals.getIssuerProposal - Retrieves issuer's governance proposals
 * @requires Authentication - Valid JWT token required
 * @requires Authorization - Issuer role implied for accessing own proposals
 *
 * @description
 * Issuer proposal features:
 * - Complete list of proposals created by authenticated issuer
 * - Real-time voting progress and participation tracking
 * - Detailed analytics including voter turnout and approval rates
 * - Proposal status management and timeline tracking
 * - Export capabilities for governance reporting
 *
 * @query {number} [page=1] - Page number for pagination
 * @query {number} [limit=10] - Number of proposals per page
 * @query {string} [status] - Filter by proposal status (PENDING, ACTIVE, COMPLETED, CANCELLED)
 * @query {string} [offeringId] - Filter by specific offering
 * @query {string} [proposalType] - Filter by proposal type
 *
 * @returns {Object} 200 - Issuer proposals with voting analytics
 * @returns {Object} 401 - Invalid or expired authentication token
 * @returns {Object} 403 - Insufficient permissions (not an issuer)
 *
 */
router.get('/issuer', getProposal, proposals.getIssuerProposal);

/**
 * Retrieves detailed information for a specific governance proposal.
 * Provides comprehensive proposal details including voting statistics,
 * participant information, and real-time results for transparency.
 *
 * @route GET /details
 * @middleware getProposalDetails - Validates proposal detail request parameters
 * @handler proposals.getProposalDetails - Retrieves detailed proposal information
 * @requires Authentication - Valid JWT token required
 *
 * @description
 * Proposal detail features:
 * - Complete proposal information and documentation
 * - Real-time voting statistics and participation data
 * - Individual vote breakdown and voter information
 * - Timeline tracking and voting period management
 * - Result calculations and outcome determination
 *
 * @query {string} proposalId - ID of proposal to retrieve details for
 *
 * @returns {Object} 200 - Detailed proposal information with voting data
 * @returns {Object} 400 - Invalid proposal ID
 * @returns {Object} 401 - Invalid or expired authentication token
 * @returns {Object} 404 - Proposal not found
 *
 */
router.get('/details', getProposalDetails, proposals.getProposalDetails);

/**
 * Sets investor participation caps for governance proposals.
 * Allows issuers to configure maximum voting participation limits
 * for regulatory compliance and governance balance requirements.
 *
 * @route POST /set-proposal-cap
 * @middleware investorCapValidationReq - Validates investor cap configuration data
 * @handler proposals.setInvestorProposalCap - Configures investor participation limits
 * @requires Authentication - Valid JWT token required
 * @requires Authorization - Issuer role required for cap configuration
 *
 * @description
 * Investor cap features:
 * - Configure maximum voting participation percentages
 * - Set individual investor voting weight limits
 * - Implement regulatory compliance requirements
 * - Balance governance power distribution
 * - Ensure democratic participation principles
 *
 * @body {Object} capConfiguration - Investor participation cap settings
 * @body {string} capConfiguration.offeringId - ID of offering to configure caps for
 * @body {number} capConfiguration.maxIndividualParticipation - Maximum percentage single investor can vote
 * @body {number} capConfiguration.maxInstitutionalParticipation - Maximum percentage institutional investors can vote
 * @body {boolean} capConfiguration.enableWeightingCaps - Whether to enable voting weight limitations
 *
 * @returns {Object} 200 - Investor participation caps configured successfully
 * @returns {Object} 400 - Invalid cap configuration data
 * @returns {Object} 401 - Invalid or expired authentication token
 * @returns {Object} 403 - Insufficient permissions (not an issuer)
 *
 * {
 *   "offeringId": "507f1f77bcf86cd799439011",
 *   "maxIndividualParticipation": 15.0,
 *   "maxInstitutionalParticipation": 35.0,
 *   "enableWeightingCaps": true
 * }
 * ```
 */
router.post('/set-proposal-cap', investorCapValidationReq, proposals.setInvestorProposalCap);

/**
 * Retrieves governance proposals available for investor participation.
 * Provides investor-focused view of active proposals with voting eligibility
 * and participation information for democratic decision-making.
 *
 * @route GET /investor
 * @middleware jwtAuthenticated.isInvestorOrInstitution - Validates investor/institution role
 * @middleware getInvestorProposal - Validates investor proposal query parameters
 * @handler proposals.getInvestorProposal - Retrieves investor-accessible proposals
 * @requires Authentication - Valid JWT token required
 * @requires Authorization - Investor or institution role required
 *
 * @description
 * Investor proposal features:
 * - Active proposals available for investor participation
 * - Voting eligibility and token weight information
 * - Proposal details and supporting documentation
 * - Current voting status and time remaining
 * - Previous voting history and participation records
 *
 * @query {number} [page=1] - Page number for pagination
 * @query {number} [limit=10] - Number of proposals per page
 * @query {string} [status] - Filter by proposal status (ACTIVE, COMPLETED)
 * @query {string} [offeringId] - Filter by specific offering investments
 *
 * @returns {Object} 200 - Investor-accessible proposals with voting information
 * @returns {Object} 401 - Invalid or expired authentication token
 * @returns {Object} 403 - Insufficient permissions (not investor/institution)
 *
 */
router.get('/investor', jwtAuthenticated.isInvestorOrInstitution, getInvestorProposal, proposals.getInvestorProposal);

/**
 * Submits a vote on a governance proposal with comprehensive validation.
 * Allows eligible token holders to participate in democratic decision-making
 * for tokenized asset governance with secure vote recording and verification.
 *
 * @route POST /investor/vote
 * @middleware jwtAuthenticated.isInvestorOrInstitution - Validates investor/institution role
 * @middleware ProposalVotingReq - Validates voting request data and eligibility
 * @handler proposals.voteProposal - Processes vote submission and recording
 * @requires Authentication - Valid JWT token required
 * @requires Authorization - Investor or institution role required
 *
 * @description
 * Voting process features:
 * - Secure vote recording with cryptographic verification
 * - Token-weighted voting based on ownership percentage
 * - Voting eligibility validation and fraud prevention
 * - Real-time vote tallying and result updates
 * - Comprehensive audit trail for transparency
 * - Anti-manipulation safeguards and validation
 *
 * Voting validation:
 * - Verify voter eligibility and token ownership
 * - Ensure proposal is in active voting period
 * - Prevent duplicate voting and manipulation
 * - Validate voting weight calculations
 * - Record vote with immutable audit trail
 *
 * @body {Object} voteData - Governance vote information
 * @body {string} voteData.proposalId - ID of proposal to vote on
 * @body {string} voteData.voteDecision - Vote choice (APPROVE, REJECT, ABSTAIN)
 * @body {string} [voteData.comments] - Optional voting comments or rationale
 * @body {string} [voteData.delegateAddress] - Optional delegate voter address
 *
 * @returns {Object} 200 - Vote submitted successfully
 * @returns {Object} 400 - Invalid vote data or voting period expired
 * @returns {Object} 401 - Invalid or expired authentication token
 * @returns {Object} 403 - Insufficient permissions or voting eligibility
 * @returns {Object} 404 - Proposal not found
 * @returns {Object} 409 - Vote already submitted or duplicate vote
 *
 * {
 *   "proposalId": "507f1f77bcf86cd799439015",
 *   "voteDecision": "APPROVE",
 *   "comments": "Support this renovation project as it will increase property value and rental income"
 * }
 *
 * // Response:
 * {
 *   "status": 200,
 *   "message": "Vote submitted successfully",
 * }
 * ```
 */
router.post('/investor/vote', jwtAuthenticated.isInvestorOrInstitution, ProposalVotingReq, proposals.voteProposal);

/**
 * Retrieves whitelisted offerings available for investor participation.
 * Provides list of tokenized offerings where investor is eligible to participate
 * in governance decisions and voting processes based on token ownership.
 *
 * @route GET /whitelisted-offerings
 * @middleware jwtAuthenticated.isInvestorOrInstitution - Validates investor/institution role
 * @handler proposals.getWhitelistOffering - Retrieves investor's whitelisted offerings
 * @requires Authentication - Valid JWT token required
 * @requires Authorization - Investor or institution role required
 *
 * @description
 * Whitelisted offering features:
 * - Complete list of offerings where investor has governance rights
 * - Token holding information and voting weight details
 * - Active and historical governance participation
 * - Offering performance and investment summary
 * - Governance activity and proposal participation rates
 *
 * @returns {Object} 200 - Whitelisted offerings with governance information
 * @returns {Object} 401 - Invalid or expired authentication token
 * @returns {Object} 403 - Insufficient permissions (not investor/institution)
 * @returns {Object} 404 - No whitelisted offerings found
 *
 * // Response includes governance-eligible offerings:
 * {
 *   "status": 200,
 *   "data": {
 *     "offerings": [
 *       {
 *         "offeringId": "507f1f77bcf86cd799439011",
 *         "offeringName": "Downtown Office Complex",
 *         "tokenHoldings": "1000",
 *         "votingWeight": "10.5%",
 *         "activeProposals": 2,
 *         "totalProposals": 8,
 *         "participationRate": "87.5%"
 *       }
 *     ]
 *   }
 * }
 * ```
 */
router.get('/whitelisted-offerings', jwtAuthenticated.isInvestorOrInstitution, proposals.getWhitelistOffering);

/**
 * Export the configured governance proposal router for use in the main application.
 * This router handles all governance and voting operations and should be mounted
 * at the appropriate base path for proposal management APIs.
 *
 * @exports {Router} router - Configured Express router with all governance routes
 */
export default router;
