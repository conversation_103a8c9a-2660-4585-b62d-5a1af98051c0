/* eslint-disable no-restricted-syntax */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { Request, Response, NextFunction } from 'express';
import { Jo<PERSON><PERSON>alidationResult, PromiseResolve, UserTypeEnum } from '../utils/common.interface';
import { RES_MSG, RESPONSES } from '../utils/responseUtils';
import UserValidation from '../component/userAuthentications/validation';
import { ResponseHandler } from '../helpers/response.helper';
import logger from '../helpers/logging/logger.helper';
import CustomError from '../helpers/customError.helper';
import CommonHelper from '../helpers/common.helper';

/**
 * Validates user registration request data for account creation.
 * Applies comprehensive validation rules for email format, password strength,
 * required fields, and data format consistency. Ensures data integrity before processing.
 *
 * @async
 * @function validateSignUpReq
 * @param {Request} req - Express request object containing registration data
 * @param {Response} res - Express response object for error handling
 * @param {NextFunction} next - Express next function to continue middleware chain
 * @returns {Promise<PromiseResolve | void>} Promise resolving to validation result or void on success
 * @throws {CustomError} Throws validation errors with specific messages and status codes
 * @exports
 *
 * @description
 * Registration validation includes:
 * - Email format and uniqueness validation
 * - Password strength requirements
 * - Required field presence checking
 * - Mobile number format validation
 * - Country code validation
 * - User type validation
 * - OTP method validation
 * - Data sanitization and normalization
 *
 * Validation rules:
 * - Email must be valid format and unique
 * - Password must meet security requirements
 * - Name, mobile, country code are required
 * - User type must be valid enum value
 * - OTP methods must be supported
 * - Data length and format constraints
 *
 * // Validation process:
 * // 1. Validates request body against signup schema
 * // 2. Sanitizes and normalizes data
 * // 3. Adds validated data to req.body
 * // 4. Calls next() on success or sends error response
 * ```
 */
export async function validateSignUpReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.singUpValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'validateSignupReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Validates user login request credentials and security requirements.
 * Enforces login validation rules including username format, password presence,
 * and additional security measures like rate limiting preparation.
 *
 * @async
 * @function validateLoginReq
 * @param {Request} req - Express request object containing login credentials
 * @param {Response} res - Express response object for error handling
 * @param {NextFunction} next - Express next function to continue middleware chain
 * @returns {Promise<PromiseResolve | void>} Promise resolving to validation result or void on success
 * @throws {CustomError} Throws validation errors with specific messages and status codes
 * @exports
 *
 * @description
 * Login validation includes:
 * - Username format validation (email or mobile)
 * - Password presence and format checking
 * - Request structure validation
 * - Data sanitization for security
 * - Preparation for rate limiting checks
 *
 * Security features:
 * - Prevents SQL injection through sanitization
 * - Validates input formats to prevent exploits
 * - Prepares data for authentication process
 * - Ensures consistent data structure
 *
 * // Validation covers:
 * // - Email/mobile format validation
 * // - Password presence checking
 * // - Request data sanitization
 * // - Security constraint enforcement
 * ```
 */
export async function validateLoginReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.loginValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'validateLoginReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Validates social media login request data for OAuth authentication.
 * Handles validation of social provider tokens, profile data, and integration requirements.
 * Ensures secure social authentication flow with proper data validation.
 *
 * @async
 * @function socialLoginValidationReq
 * @param {Request} req - Express request object containing social login data
 * @param {Response} res - Express response object for error handling
 * @param {NextFunction} next - Express next function to continue middleware chain
 * @returns {Promise<PromiseResolve | void>} Promise resolving to validation result or void on success
 * @throws {CustomError} Throws validation errors with specific messages and status codes
 * @exports
 *
 * @description
 * Social login validation includes:
 * - OAuth provider validation
 * - Access token format verification
 * - Profile data structure validation
 * - Provider-specific data requirements
 * - Security token verification preparation
 *
 * Supported providers:
 * - Google OAuth authentication
 * - Facebook social login
 * - LinkedIn professional authentication
 * - Other configured OAuth providers
 *
 * // Validates social login data:
 * // - Provider identification
 * // - OAuth token validation
 * // - Profile data verification
 * // - Security constraint checking
 * ```
 */
export async function socialLoginValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.socialLoginValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'socialLoginValidationReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Validates account verification request with OTP code verification.
 * Ensures proper verification data format and security constraints for account activation.
 * Handles multiple verification types including email, SMS, and two-factor authentication.
 *
 * @async
 * @function validateVerificationReq
 * @param {Request} req - Express request object containing verification data
 * @param {Response} res - Express response object for error handling
 * @param {NextFunction} next - Express next function to continue middleware chain
 * @returns {Promise<PromiseResolve | void>} Promise resolving to validation result or void on success
 * @throws {CustomError} Throws validation errors with specific messages and status codes
 * @exports
 *
 * @description
 * Verification validation includes:
 * - OTP code format and length validation
 * - User identification validation
 * - Verification type validation
 * - Security constraint enforcement
 * - Rate limiting preparation
 *
 * Verification types:
 * - Email verification for account activation
 * - SMS verification for mobile confirmation
 * - Login verification for additional security
 * - Password reset verification
 * - Two-factor authentication verification
 *
 * // Validates verification data:
 * // - OTP code format (6 digits)
 * // - User email identification
 * // - Verification type specification
 * // - Security and rate limiting constraints
 * ```
 */
export async function validateVerificationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.verificationValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);

    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'validateLoginReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Validates email address format and requirements for password reset and notification operations.
 * Ensures email format compliance and prepares data for email-based operations.
 * Handles email validation for various authentication flows.
 *
 * @async
 * @function validateEmailReq
 * @param {Request} req - Express request object containing email data
 * @param {Response} res - Express response object for error handling
 * @param {NextFunction} next - Express next function to continue middleware chain
 * @returns {Promise<PromiseResolve | void>} Promise resolving to validation result or void on success
 * @throws {CustomError} Throws validation errors with specific messages and status codes
 * @exports
 *
 * @description
 * Email validation includes:
 * - RFC-compliant email format validation
 * - Domain validation and format checking
 * - Disposable email detection
 * - Length and character constraints
 * - Security sanitization
 *
 * Use cases:
 * - Password reset email requests
 * - Account recovery operations
 * - Notification subscription management
 * - Email change verification
 * - Administrative email operations
 *
 * // Validates email data:
 * // - RFC-compliant format
 * // - Domain existence checking
 * // - Disposable email prevention
 * // - Security sanitization
 * ```
 */
export async function validateEmailReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.emailValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'validatePasswordReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function changePasswordReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.changePasswordValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    const { password, oldPassword } = validateRequest.value;
    req.body = { oldPassword, password, email: req.body.email };
    next();
  } catch (error: any) {
    logger.error(error, 'changePassReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function validateResetPasswordReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.resetPasswordValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'changePassReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}
/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function validateUpdateProfileReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.updateProfileValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'validateUpdateProfileReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function validateUpdateKycReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const { userType } = req.userInfo;

    const validateRequest: JoiValidationResult = userType === UserTypeEnum.Institution ? await UserValidation.updateKybValidation(req.body) : await UserValidation.updateKycValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);

    // kyc all date formats
    if (validateRequest.value?.mainInformation?.dob) {
      const formattedDate = await CommonHelper.formatDates(validateRequest.value.mainInformation?.dob);
      validateRequest.value.mainInformation.dob = formattedDate;
    }
    if (validateRequest.value?.mainInformation?.documentExpiration) {
      const formattedDate = await CommonHelper.formatDates(validateRequest.value.mainInformation?.documentExpiration);
      validateRequest.value.mainInformation.documentExpiration = formattedDate;
    }

    // kyb all date formats

    if (validateRequest.value.primaryContactInfo?.personalInformation?.dob) {
      const formattedDate = await CommonHelper.formatDates(validateRequest.value.primaryContactInfo?.personalInformation?.dob);
      validateRequest.value.primaryContactInfo.personalInformation.dob = formattedDate;
    }

    // Format dob for each beneficial owner in the array
    if (validateRequest.value?.beneficialOwners && Array.isArray(validateRequest.value.beneficialOwners)) {
      for (const owner of validateRequest.value.beneficialOwners) {
        if (owner?.personalInformation?.dob) {
          // eslint-disable-next-line no-await-in-loop
          const formattedDate = await CommonHelper.formatDates(owner.personalInformation.dob);
          owner.personalInformation.dob = formattedDate;
        }
      }
    }

    // Format dob for each managementInfo owner in the array
    if (validateRequest.value?.managementInfo && Array.isArray(validateRequest.value.managementInfo)) {
      for (const owner of validateRequest.value.managementInfo) {
        if (owner?.personalInformation?.dob) {
          // eslint-disable-next-line no-await-in-loop
          const formattedDate = await CommonHelper.formatDates(owner.personalInformation.dob);
          owner.personalInformation.dob = formattedDate;
        }
      }
    }

    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'validateUpdateKycReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function tokenValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.verifyTokenValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'tokenValidationReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}
/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function docsValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.docsValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'tokenValidationReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function verify2FAReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.verify2FAValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'tokenValidationReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function resendOtpValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.resendOtpValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'resendOtpValidationReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function getUserPortfolio(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const { page, limit, searchQuery } = req.query;
    const isCsv = req.query.isCsv === 'true';
    // Convert the query params to numbers and validate
    const validateRequest: JoiValidationResult = await UserValidation.getUserPortfolio({
      page: page ? Number(page) : undefined,
      limit: limit ? Number(limit) : undefined,
      searchQuery: searchQuery ? String(searchQuery as string) : undefined,
      isCsv: isCsv ? Boolean(isCsv) : false,
    });
    console.log('validateRequest', validateRequest);
    if (validateRequest.error) {
      throw new CustomError(validateRequest.message, validateRequest.status);
    }

    // Replace req.query with the validated and possibly defaulted values
    req.query = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'getPortfolio Error');

    return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function getProfileReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.getProfileValidation({ ...req.query });
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'changePassReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}
/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function getUserProfileReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.getUserProfileValidation({ ...req.query, id: req.params.id });
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'getUserProfileReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function transferAgentListValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.transferAgentListValidation(req.query as Record<string, any>);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.query = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'transferAgentListValidationReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}
