import { Request, Response, NextFunction } from 'express';
import logger from '../helpers/logging/logger.helper';
import { RES_MSG, RESPONSES } from '../utils/responseUtils';
import { Jo<PERSON><PERSON>alidationResult, PromiseResolve } from '../utils/common.interface';
import { TransferRequestValidation } from '../component/transfer/validation';
import CustomError from '../helpers/customError.helper';
import { ResponseHandler } from '../helpers/response.helper';

/**
 * Validates token transfer request creation for comprehensive transfer management.
 * Ensures proper transfer data validation, authorization, and compliance checking.
 * Provides security validation and business rule enforcement for token transfer operations.
 *
 * @async
 * @function createTransferRequestValidationReq
 * @param {Request} req - Express request object containing transfer request data
 * @param {Response} res - Express response object for error handling
 * @param {NextFunction} next - Express next function to continue middleware chain
 * @returns {Promise<PromiseResolve | void>} Promise resolving to validation result or void
 * @throws {CustomError} Throws custom error for validation failures
 *
 * @description
 * Transfer request validation features:
 * - Token balance verification and transfer amount validation
 * - Wallet address validation and whitelist checking
 * - Transfer type determination and compliance verification
 * - Lock-up period validation and transfer restriction checking
 * - Security validation and fraud prevention measures
 * - Regulatory compliance and AML requirement verification
 *
 * Validated transfer data:
 * - offeringId: Valid offering with transferable tokens
 * - fromAddress: Source wallet address validation and ownership verification
 * - toAddress: Destination wallet address validation and whitelist checking
 * - quantity: Transfer amount validation against available balance
 * - transferType: Transfer type validation (REGULAR, FORCE, EMERGENCY)
 * - reason: Transfer reason validation and categorization
 * - notes: Additional transfer notes and documentation
 *
 * Business rule validations:
 * - Sufficient token balance for transfer amount
 * - Destination address whitelist verification and compliance
 * - Lock-up period compliance and transfer window validation
 * - Transfer limits and frequency restrictions
 * - Regulatory compliance including AML and sanctions checking
 * - Platform transfer policies and fee calculations
 *
 * // Middleware usage
 * router.post('/transfer', createTransferRequestValidationReq, transferController.createTransfer);
 * ```
 */
export async function createTransferRequestValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const transferData = req.body;

    // Validate transfer request data using the TransferRequestValidation schema
    const validateRequest: JoiValidationResult = await TransferRequestValidation.createTransferRequestValidation(transferData);

    if (validateRequest.error) {
      throw new CustomError(validateRequest.message, validateRequest.status);
    }

    // Replace req.body with the validated data
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'createTransferRequestValidationReq Error');

    return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Validates transfer request retrieval for comprehensive transfer management dashboards.
 * Ensures proper query parameter validation and authorization for transfer data access.
 * Provides secure access control and performance optimization for transfer queries.
 *
 * @async
 * @function getTransferRequestsValidationReq
 * @param {Request} req - Express request object containing transfer query parameters
 * @param {Response} res - Express response object for error handling
 * @param {NextFunction} next - Express next function to continue middleware chain
 * @returns {Promise<PromiseResolve | void>} Promise resolving to validation result or void
 * @throws {CustomError} Throws custom error for validation failures
 *
 * @description
 * Transfer request retrieval validation features:
 * - Pagination parameter validation and performance optimization
 * - Filter parameter validation for status, type, and date ranges
 * - Search query sanitization and format validation
 * - Authorization verification for transfer data access
 * - Performance optimization through parameter limits
 * - Audit logging for transfer access patterns
 *
 * Validated query parameters:
 * - page: Page number for pagination (positive integer, reasonable limits)
 * - limit: Results per page (1-100 range for performance)
 * - searchQuery: Search term sanitization and length validation
 * - status: Transfer status filter (PENDING, APPROVED, REJECTED, COMPLETED)
 * - transferType: Transfer type filter (REGULAR, FORCE, EMERGENCY)
 * - offeringId: Specific offering filter with access verification
 * - startDate: Date range filter start (ISO format validation)
 * - endDate: Date range filter end (ISO format validation)
 *
 * Security and performance features:
 * - User authorization verification for transfer data access
 * - Role-based access control for different user types
 * - Rate limiting integration for API abuse prevention
 * - Parameter sanitization to prevent injection attacks
 * - Query optimization through validated parameters
 * - Data privacy protection for sensitive transfer information
 *
 * // Transfer filtering by offering
 * GET /api/transfer?offeringId=507f1f77bcf86cd799439011&transferType=REGULAR
 *
 * // Middleware usage
 * router.get('/transfer', getTransferRequestsValidationReq, transferController.getTransfers);
 * ```
 */
export async function getTransferRequestsValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const { page, limit, searchQuery } = req.query;

    // Convert query params to numbers and validate
    const validateRequest: JoiValidationResult = await TransferRequestValidation.getTransferRequestsValidation({
      page: page ? Number(page) : undefined,
      limit: limit ? Number(limit) : undefined,
      searchQuery: searchQuery ? String(searchQuery as string) : undefined,
    });

    if (validateRequest.error) {
      throw new CustomError(validateRequest.message, validateRequest.status);
    }

    // Replace req.query with the validated and possibly defaulted values
    req.query = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'getTransferRequestsValidationReq Error');

    return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Validates transfer request rejection for comprehensive transfer management and workflow control.
 * Ensures proper authorization and parameter validation for transfer rejection operations.
 * Provides secure access control and business rule enforcement for transfer status management.
 *
 * @async
 * @function rejectTransferRequestsValidationReq
 * @param {Request} req - Express request object containing transfer rejection data
 * @param {Response} res - Express response object for error handling
 * @param {NextFunction} next - Express next function to continue middleware chain
 * @returns {Promise<PromiseResolve | void>} Promise resolving to validation result or void
 * @throws {CustomError} Throws custom error for validation failures
 *
 * @description
 * Transfer rejection validation features:
 * - Transfer ID validation and existence verification
 * - Rejection reason validation and categorization
 * - Authorization verification for rejection permissions
 * - Transfer status validation to ensure rejection is allowed
 * - Business rule enforcement for rejection workflows
 * - Audit trail maintenance for rejection activities
 *
 * Validated rejection data:
 * - transferId: Valid transfer identifier and existence verification
 * - rejectionReason: Required reason classification and detailed explanation
 * - rejectionCategory: Standardized category for reporting and analytics
 * - Authorization level: Permission verification for rejection operations
 * - Transfer status: Current status validation to allow rejection
 * - Notification settings: Automatic notification configuration
 *
 * Business rule validations:
 * - Transfer must be in rejectable status (PENDING, APPROVED)
 * - User must have appropriate permissions for rejection
 * - Rejection reason must be provided and categorized
 * - Compliance requirements for rejection documentation
 * - Timeline restrictions and business hour validation
 * - Impact assessment for related transactions
 *
 * // Middleware usage
 * router.put('/transfer/reject', rejectTransferRequestsValidationReq, transferController.rejectTransfer);
 * ```
 */
export async function rejectTransferRequestsValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await TransferRequestValidation.rejectTransferRequestsValidation(req.body);

    if (validateRequest.error) {
      throw new CustomError(validateRequest.message, validateRequest.status);
    }

    // Replace req.body with the validated values
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'rejectTransferRequestsValidationReq Error');

    return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Validates transfer request retrieval by specific offering for offering-focused transfer management.
 * Ensures proper offering-specific parameter validation and authorization for transfer data access.
 * Provides secure access control and comprehensive filtering for offering-based transfer analytics.
 *
 * @async
 * @function getTransferRequestsByofferingValidationReq
 * @param {Request} req - Express request object containing offering-specific transfer query parameters
 * @param {Response} res - Express response object for error handling
 * @param {NextFunction} next - Express next function to continue middleware chain
 * @returns {Promise<PromiseResolve | void>} Promise resolving to validation result or void
 * @throws {CustomError} Throws custom error for validation failures
 *
 * @description
 * Offering-specific transfer validation features:
 * - Offering ID validation and access permission verification
 * - Offering-specific transfer filtering and analytics
 * - Token holder transfer pattern analysis for the offering
 * - Transfer velocity and frequency analytics
 * - Compliance reporting for offering-specific transfers
 * - Performance optimization for offering-based queries
 *
 * Validated offering transfer parameters:
 * - offeringId: Valid offering identifier with access verification
 * - page: Pagination optimized for offering-specific data
 * - limit: Results per page appropriate for offering analytics
 * - status: Transfer status filter within offering context
 * - transferType: Transfer type filter for offering analysis
 * - dateRange: Time-based filtering for offering transfer trends
 * - searchQuery: Search within offering-specific transfers
 *
 * Offering analytics features:
 * - Transfer volume and frequency analysis for the offering
 * - Token distribution patterns and holder behavior
 * - Compliance metrics and regulatory reporting
 * - Transfer approval rates and processing times
 * - Geographic distribution and transfer patterns
 * - Liquidity analysis and market activity indicators
 *
 * // Offering transfer analytics
 * GET /api/transfer/507f1f77bcf86cd799439011?transferType=REGULAR&startDate=2024-01-01
 *
 * // Middleware usage
 * router.get('/transfer/:offeringId', getTransferRequestsByofferingValidationReq, transferController.getOfferingTransfers);
 * ```
 */
export async function getTransferRequestsByofferingValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const { page, limit, searchQuery, isCsv } = req.query;
    const { offeringId } = req.params;
    const isCsvBool = isCsv === 'true'; // Convert isCsv to boolean (true if 'true', otherwise false)
    // Convert query params to numbers and validate
    const validateRequest: JoiValidationResult = await TransferRequestValidation.getTransferRequestsFromOfferingIdValidation({
      page: page ? Number(page) : undefined,
      limit: limit ? Number(limit) : undefined,
      searchQuery: searchQuery ? String(searchQuery as string) : undefined,
      offeringId: String(offeringId),
      isCsv: isCsvBool, // Pass the boolean value
    });

    if (validateRequest.error) {
      throw new CustomError(validateRequest.message, validateRequest.status);
    }
    req.params = { offeringId: validateRequest.value.offeringId };
    req.query = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'getTransferRequestsFromOfferingIdValidation Error');

    return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}
