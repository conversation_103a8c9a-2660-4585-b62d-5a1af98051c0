import { NextFunction, Request, Response } from 'express';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PromiseResolve } from '../utils/common.interface';
import ProposalValidation from '../component/proposals/validation';
import CustomError from '../helpers/customError.helper';
import { <PERSON><PERSON>and<PERSON> } from '../helpers/response.helper';
import { RES_MSG, RESPONSES } from '../utils/responseUtils';

/**
 * Validates governance proposal creation requests for comprehensive proposal management.
 * Ensures proper proposal data validation, authorization, and governance rule enforcement.
 * Provides security validation and compliance checking for proposal submission and workflow.
 *
 * @async
 * @function ProposalValidationReq
 * @param {Request} req - Express request object containing proposal creation data
 * @param {Response} res - Express response object for error handling
 * @param {NextFunction} next - Express next function to continue middleware chain
 * @returns {Promise<PromiseResolve | void>} Promise resolving to validation result or void
 * @throws {CustomError} Throws custom error for validation failures
 *
 * @description
 * Proposal creation validation features:
 * - Proposal content validation and format checking
 * - Voting timeline validation with proper date sequencing
 * - Offering validation and token holder eligibility verification
 * - Quorum and threshold validation for governance requirements
 * - Authorization verification for proposal submission rights
 * - Business rule enforcement for governance policies
 *
 * Validated proposal data:
 * - Offering ID: Valid offering with active governance capabilities
 * - Proposal title: Clear and descriptive proposal identification
 * - Proposal description: Detailed proposal content and rationale
 * - Voting start date: Future date validation with advance notice
 * - Voting end date: Logical timeline with sufficient voting period
 * - Quorum percentage: Required participation threshold validation
 * - Approval threshold: Required approval percentage for passage
 *
 * Business rule validations:
 * - Minimum advance notice period for proposal submissions
 * - Maximum voting period duration and practical limits
 * - Quorum requirements based on offering size and type
 * - Approval thresholds meeting regulatory and governance standards
 * - Proposal content guidelines and appropriateness checking
 * - Conflict of interest validation and disclosure requirements
 *
 * // Middleware usage
 * router.post('/proposal', ProposalValidationReq, proposalController.createProposal);
 * ```
 */
export async function ProposalValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await ProposalValidation.createProposal(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Validates proposal voting requests for comprehensive governance participation.
 * Ensures proper vote data validation, voter eligibility, and voting rule enforcement.
 * Provides security validation and fraud prevention for democratic voting processes.
 *
 * @async
 * @function ProposalVotingReq
 * @param {Request} req - Express request object containing voting data
 * @param {Response} res - Express response object for error handling
 * @param {NextFunction} next - Express next function to continue middleware chain
 * @returns {Promise<PromiseResolve | void>} Promise resolving to validation result or void
 * @throws {CustomError} Throws custom error for validation failures
 *
 * @description
 * Proposal voting validation features:
 * - Voter eligibility verification and token holding validation
 * - Vote choice validation and option verification
 * - Voting period validation and timeline enforcement
 * - Duplicate vote prevention and fraud detection
 * - Token weight calculation and voting power verification
 * - Security validation and anti-manipulation measures
 *
 * Validated voting data:
 * - Proposal ID: Valid active proposal with ongoing voting
 * - Vote decision: Valid voting option (APPROVE, REJECT, ABSTAIN)
 * - Voter identification: Token holder verification and eligibility
 * - Token holdings: Voting weight calculation and verification
 * - Voting timestamp: Voting period compliance checking
 * - Optional comments: Vote reasoning and stakeholder communication
 *
 * Voting rule validations:
 * - Proposal must be in active voting period
 * - Voter must be eligible token holder at record date
 * - No duplicate voting from same wallet or user
 * - Vote must be within valid options and format
 * - Voting weight calculation based on token holdings
 * - Fraud prevention and manipulation detection
 *
 * // Middleware usage
 * router.post('/proposal/investor/vote', ProposalVotingReq, proposalController.voteProposal);
 * ```
 */
export async function ProposalVotingReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await ProposalValidation.voteProposal(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Validates proposal retrieval requests for comprehensive governance management dashboards.
 * Ensures proper query parameter validation and authorization for proposal data access.
 * Provides secure access control and performance optimization for proposal queries.
 *
 * @async
 * @function getProposal
 * @param {Request} req - Express request object containing proposal query parameters
 * @param {Response} res - Express response object for error handling
 * @param {NextFunction} next - Express next function to continue middleware chain
 * @returns {Promise<PromiseResolve | void>} Promise resolving to validation result or void
 * @throws {CustomError} Throws custom error for validation failures
 *
 * @description
 * Proposal retrieval validation features:
 * - Pagination parameter validation and performance optimization
 * - Filter parameter validation for status, offering, and date ranges
 * - Search query sanitization and format validation
 * - Authorization verification for proposal data access
 * - Performance optimization through parameter limits
 * - Audit logging for governance access patterns
 *
 * // Middleware usage
 * router.get('/proposal/issuer', getProposal, proposalController.getIssuerProposal);
 * ```
 */
export async function getProposal(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await ProposalValidation.getProposal(req.query);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Validates proposal detail retrieval requests for comprehensive proposal information access.
 * Ensures proper proposal ID validation and authorization for detailed proposal data.
 * Provides secure access control and parameter validation for proposal detail views.
 *
 * @async
 * @function getProposalDetails
 * @param {Request} req - Express request object containing proposal detail query parameters
 * @param {Response} res - Express response object for error handling
 * @param {NextFunction} next - Express next function to continue middleware chain
 * @returns {Promise<PromiseResolve | void>} Promise resolving to validation result or void
 * @throws {CustomError} Throws custom error for validation failures
 *
 * @description
 * Proposal detail validation features:
 * - Proposal ID format validation and existence checking
 * - Authorization verification for proposal detail access
 * - Voting statistics and participation data validation
 * - Real-time voting progress and timeline tracking
 * - Security validation to prevent unauthorized data access
 *
 * // Middleware usage
 * router.get('/proposal/details', getProposalDetails, proposalController.getProposalDetails);
 * ```
 */
export async function getProposalDetails(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await ProposalValidation.getProposalDetails(req.query);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Validates investor proposal retrieval requests for investor-specific governance participation.
 * Ensures proper investor authorization and parameter validation for proposal access.
 * Provides secure access control tailored for investor governance interfaces.
 *
 * @async
 * @function getInvestorProposal
 * @param {Request} req - Express request object containing investor proposal query parameters
 * @param {Response} res - Express response object for error handling
 * @param {NextFunction} next - Express next function to continue middleware chain
 * @returns {Promise<PromiseResolve | void>} Promise resolving to validation result or void
 * @throws {CustomError} Throws custom error for validation failures
 *
 * @description
 * Investor proposal validation features:
 * - Investor-specific authorization and access control
 * - Token holding verification for voting eligibility
 * - Portfolio-specific proposal filtering and analytics
 * - Voting history and participation tracking
 * - Privacy protection for investor governance data
 *
 * // Middleware usage
 * router.get('/proposal/investor', getInvestorProposal, proposalController.getInvestorProposal);
 * ```
 */
export async function getInvestorProposal(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await ProposalValidation.getInvestorProposal(req.query);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Validates investor participation cap configuration requests for governance balance.
 * Ensures proper authorization and parameter validation for investor cap settings.
 * Provides regulatory compliance and democratic governance balance enforcement.
 *
 * @async
 * @function investorCapValidationReq
 * @param {Request} req - Express request object containing investor cap configuration data
 * @param {Response} res - Express response object for error handling
 * @param {NextFunction} next - Express next function to continue middleware chain
 * @returns {Promise<PromiseResolve | void>} Promise resolving to validation result or void
 * @throws {CustomError} Throws custom error for validation failures
 *
 * @description
 * Investor cap validation features:
 * - Participation cap percentage validation and regulatory compliance
 * - Individual investor voting weight limitations
 * - Institutional investor cap configuration and enforcement
 * - Democratic governance balance and power distribution
 * - Regulatory compliance for voting concentration limits
 *
 * // Middleware usage
 * router.post('/proposal/set-proposal-cap', investorCapValidationReq, proposalController.setInvestorCap);
 * ```
 */
export async function investorCapValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await ProposalValidation.setInvestorCapProposal(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}
