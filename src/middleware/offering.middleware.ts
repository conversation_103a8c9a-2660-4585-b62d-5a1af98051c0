/* eslint-disable @typescript-eslint/no-explicit-any */
import { Request, Response, NextFunction } from 'express';
import { Jo<PERSON><PERSON>alidationResult, PromiseResolve } from '../utils/common.interface';
import { RES_MSG, RESPONSES } from '../utils/responseUtils';
import { <PERSON><PERSON>and<PERSON> } from '../helpers/response.helper';
import logger from '../helpers/logging/logger.helper';
import CustomError from '../helpers/customError.helper';
import offeringValidation from '../component/offerings/validation';

/**
 * Validates offering list retrieval request parameters for comprehensive offering browsing.
 * Ensures proper pagination, filtering, and search parameters for offering discovery.
 * Provides secure and efficient offering list access with validated query parameters.
 *
 * @async
 * @function offeringListValidationReq
 * @param {Request} req - Express request object containing query parameters
 * @param {Response} res - Express response object for error handling
 * @param {NextFunction} next - Express next function to continue middleware chain
 * @returns {Promise<PromiseResolve | void>} Promise resolving to validation result or void
 * @throws {CustomError} Throws custom error for validation failures
 *
 * @description
 * Offering list validation features:
 * - Pagination parameter validation (page, limit, offset)
 * - Search query sanitization and format validation
 * - Filter parameter validation (status, type, category)
 * - Sort parameter validation and security checking
 * - Access control and permission validation
 * - Performance optimization through parameter limits
 *
 * Validated parameters:
 * - page: Page number for pagination (positive integer)
 * - limit: Results per page (1-100 range)
 * - search: Search term sanitization and length limits
 * - status: Offering status filter validation
 * - category: Asset category filter validation
 * - sortBy: Allowed sort fields validation
 * - sortOrder: Sort direction validation (asc/desc)
 *
 * Security validations:
 * - SQL injection prevention through parameter sanitization
 * - XSS protection through input encoding
 * - Rate limiting integration for API abuse prevention
 * - Access control verification for sensitive data
 * - Audit logging for offering access patterns
 *
 * // Middleware usage
 * router.get('/offerings', offeringListValidationReq, offeringController.getOfferings);
 * ```
 */
export async function offeringListValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await offeringValidation.offeringListValidation(req.query as Record<string, any>);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.query = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'offeringListValidationReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Validates offering creation request data for comprehensive asset tokenization.
 * Ensures all required offering information is present and properly formatted.
 * Provides security validation and business rule enforcement for new offering creation.
 *
 * @async
 * @function createOfferingsValidationReq
 * @param {Request} req - Express request object containing offering data in body
 * @param {Response} res - Express response object for error handling
 * @param {NextFunction} next - Express next function to continue middleware chain
 * @returns {Promise<PromiseResolve | void>} Promise resolving to validation result or void
 * @throws {CustomError} Throws custom error for validation failures
 *
 * @description
 * Offering creation validation features:
 * - Complete offering data structure validation
 * - Financial parameter validation (pricing, fees, minimums)
 * - Legal requirement validation (compliance, documentation)
 * - Asset-specific validation based on offering type
 * - Security and fraud prevention measures
 * - Regulatory compliance checking
 *
 * Validated offering data:
 * - Project details: Name, description, location, specifications
 * - Financial structure: Token price, supply, minimum investment
 * - Legal documentation: Required documents and compliance forms
 * - Timeline: Offering period, fundraising deadlines
 * - Asset details: Property information, financial projections
 * - Regulatory compliance: KYC requirements, jurisdictional rules
 *
 * Business rule validations:
 * - Minimum and maximum investment amounts
 * - Token supply and pricing consistency
 * - Regulatory compliance requirements
 * - Asset valuation and financial projections
 * - Documentation completeness and authenticity
 * - Timeline feasibility and business logic
 *
 * // Middleware usage
 * router.post('/offerings', createOfferingsValidationReq, offeringController.createOffering);
 * ```
 */
export async function createOfferingsValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await offeringValidation.createOfferingsValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'createOfferingsValidationReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function updateOfferingsValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await offeringValidation.updateOfferingsValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'updateOfferingsValidationReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Validates offering details retrieval request for comprehensive offering information access.
 * Ensures proper offering ID validation and user authorization for accessing detailed offering data.
 * Provides secure access control and parameter validation for offering detail views.
 *
 * @async
 * @function offeringDetailsValidationReq
 * @param {Request} req - Express request object containing offering ID parameter
 * @param {Response} res - Express response object for error handling
 * @param {NextFunction} next - Express next function to continue middleware chain
 * @returns {Promise<PromiseResolve | void>} Promise resolving to validation result or void
 * @throws {CustomError} Throws custom error for validation failures
 *
 * @description
 * Offering details validation features:
 * - Offering ID format validation and existence checking
 * - User permission validation for offering access
 * - Security validation to prevent unauthorized data access
 * - Access control based on offering visibility settings
 * - Audit logging for offering detail access attempts
 * - Rate limiting integration for API abuse prevention
 *
 * Validated parameters:
 * - offeringId: MongoDB ObjectId format validation
 * - User authorization: Access permission verification
 * - Offering status: Active offering access control
 * - Visibility settings: Public vs private offering access
 * - Geographic restrictions: Location-based access control
 *
 * // Middleware usage
 * router.get('/offerings/:id', offeringDetailsValidationReq, offeringController.getOfferingDetails);
 * ```
 */
// export async function offeringDetailsValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
//   try {
//     const validateRequest: JoiValidationResult = await offeringValidation.offeringDetailsValidation(req.params);
//     if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
//     req.params = validateRequest.value;
//     next();
//   } catch (error: any) {
//     logger.error(error, 'offeringDetailsValidationReq Error');
//     ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
//   }
// }

/**
 * Validates offering NAV (Net Asset Value) update requests for accurate financial tracking.
 * Ensures proper financial data validation and authorization for NAV updates.
 * Provides comprehensive validation for asset valuation and financial reporting accuracy.
 *
 * @async
 * @function updateOfferingsNavValidationReq
 * @param {Request} req - Express request object containing NAV update data
 * @param {Response} res - Express response object for error handling
 * @param {NextFunction} next - Express next function to continue middleware chain
 * @returns {Promise<PromiseResolve | void>} Promise resolving to validation result or void
 * @throws {CustomError} Throws custom error for validation failures
 *
 * @description
 * NAV update validation features:
 * - Financial data accuracy validation and consistency checking
 * - Authorization verification for NAV update permissions
 * - Calculation validation and mathematical accuracy verification
 * - Historical data consistency and trend analysis
 * - Regulatory compliance for financial reporting requirements
 * - Audit trail maintenance for valuation changes
 *
 * Validated NAV data:
 * - Current asset value: Accurate valuation and calculation methods
 * - Valuation date: Proper timestamp and reporting period validation
 * - Calculation methodology: Consistent valuation approach verification
 * - Supporting documentation: Required valuation reports and justifications
 * - Regulatory compliance: Financial reporting standards adherence
 * - Change justification: Explanation for significant valuation changes
 *
 * Financial validations:
 * - Reasonable value ranges and change thresholds
 * - Market data consistency and external validation
 * - Professional valuation requirements and certifications
 * - Historical trend analysis and outlier detection
 * - Currency and exchange rate considerations
 * - Tax implications and accounting standard compliance
 *
 * // Middleware usage
 * router.put('/offerings/:id/nav', updateOfferingsNavValidationReq, offeringController.updateNAV);
 * ```
 */
export async function updateOfferingsNavValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await offeringValidation.updateOfferingsNavValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'updateOfferingsNavValidationReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Validates offering subscription requests for investor participation in tokenized offerings.
 * Ensures proper investor eligibility, investment amount validation, and compliance checking.
 * Provides comprehensive validation for investment subscription and participation requirements.
 *
 * @async
 * @function subscribeValidationReq
 * @param {Request} req - Express request object containing subscription data
 * @param {Response} res - Express response object for error handling
 * @param {NextFunction} next - Express next function to continue middleware chain
 * @returns {Promise<PromiseResolve | void>} Promise resolving to validation result or void
 * @throws {CustomError} Throws custom error for validation failures
 *
 * @description
 * Subscription validation features:
 * - Investor eligibility verification and compliance checking
 * - Investment amount validation against offering parameters
 * - KYC and accreditation status verification
 * - Geographic and regulatory compliance validation
 * - Offering capacity and availability checking
 * - Payment method and funding source validation
 *
 * Validated subscription data:
 * - Investment amount: Minimum/maximum limits and increments
 * - Investor information: Identity verification and eligibility
 * - Compliance status: KYC, accreditation, and regulatory requirements
 * - Payment details: Funding source verification and processing
 * - Legal agreements: Terms acceptance and documentation
 * - Risk acknowledgment: Investor understanding and acceptance
 *
 * Eligibility validations:
 * - Accredited investor status verification
 * - Geographic jurisdiction compliance
 * - Investment limits and portfolio restrictions
 * - Age and legal capacity verification
 * - Sanctions and watchlist screening
 * - Source of funds verification and AML compliance
 *
 * // Middleware usage
 * router.post('/offerings/:id/subscribe', subscribeValidationReq, offeringController.subscribe);
 * ```
 */
export async function subscribeValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await offeringValidation.subscribeValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'subscribeValidationReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Validates offering report generation requests for comprehensive analytics and compliance reporting.
 * Ensures proper authorization and parameter validation for offering performance and compliance reports.
 * Provides secure access control and data validation for sensitive offering analytics.
 *
 * @async
 * @function offeringReportValidationReq
 * @param {Request} req - Express request object containing report parameters
 * @param {Response} res - Express response object for error handling
 * @param {NextFunction} next - Express next function to continue middleware chain
 * @returns {Promise<PromiseResolve | void>} Promise resolving to validation result or void
 * @throws {CustomError} Throws custom error for validation failures
 *
 * @description
 * Offering report validation features:
 * - Report type and scope validation for appropriate data access
 * - Date range validation and historical data availability checking
 * - User authorization verification for sensitive financial data
 * - Export format validation and file size limit enforcement
 * - Performance optimization through parameter validation
 * - Audit logging for report generation and data access
 *
 * Validated report parameters:
 * - Report type: Performance, compliance, financial, or investor reports
 * - Date range: Valid start and end dates with reasonable limits
 * - Data scope: Specific metrics and information categories
 * - Export format: PDF, Excel, CSV, or other supported formats
 * - Granularity: Daily, weekly, monthly, or quarterly reporting periods
 * - Recipient information: Authorized parties for report distribution
 *
 * Authorization levels:
 * - Issuer reports: Complete offering performance and analytics
 * - Investor reports: Portfolio-specific performance and statements
 * - Regulatory reports: Compliance and audit documentation
 * - Public reports: Limited performance and offering information
 * - Administrative reports: Platform metrics and operational data
 *
 * // Middleware usage
 * router.post('/offerings/:id/reports', offeringReportValidationReq, offeringController.generateReport);
 * ```
 */
export async function offeringReportValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await offeringValidation.offeringReportValidation({ ...req.body, ...req.query });
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'offeringReportValidationReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function topOfferingValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await offeringValidation.topOfferingValidation({ ...req.body, ...req.query });
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'topOfferingValidationReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function getOrdersFromOfferingIdValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await offeringValidation.getOrdersfromOfferingIdValidation({
      offeringId: req.params.offeringId,
      page: req.query.page ? parseInt(req.query.page as string, 10) : undefined,
      limit: req.query.limit ? parseInt(req.query.limit as string, 10) : undefined,
      status: req.query.status ? String(req.query.status as string) : undefined,
      search: req.query.search ? String(req.query.search as string) : undefined,
      isCsv: req.query.isCsv ? req.query.isCsv : false,
    });

    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);

    req.query = validateRequest.value; // Assign the validated query parameters back to req.query
    next();
  } catch (error: any) {
    logger.error(error, 'getOrdersfromOfferingIdValidationReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}
export async function updateWhitelistedOffering(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await offeringValidation.updateWhitelistedOffering({ whitelistId: req.body.whitelistId, status: req.body.status });

    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);

    req.query = validateRequest.value; // Assign the validated query parameters back to req.query
    next();
  } catch (error: any) {
    logger.error(error, 'getOrdersfromOfferingIdValidationReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}
