/* eslint-disable @typescript-eslint/no-explicit-any */
import { Request, Response, NextFunction } from 'express';
import { Jo<PERSON><PERSON>alidationResult, PromiseResolve } from '../utils/common.interface';
import { RES_MSG, RESPONSES } from '../utils/responseUtils';
import { <PERSON><PERSON>and<PERSON> } from '../helpers/response.helper';
import logger from '../helpers/logging/logger.helper';
import CustomError from '../helpers/customError.helper';
import orderValidation from '../component/order/validation';

/**
 * Validates investment order creation requests for tokenized asset purchases.
 * Ensures comprehensive validation of order data, investment amounts, and investor eligibility.
 * Provides security validation and business rule enforcement for new investment orders.
 *
 * @async
 * @function createOrderValidationReq
 * @param {Request} req - Express request object containing order creation data
 * @param {Response} res - Express response object for error handling
 * @param {NextFunction} next - Express next function to continue middleware chain
 * @returns {Promise<PromiseResolve | void>} Promise resolving to validation result or void
 * @throws {CustomError} Throws custom error for validation failures
 *
 * @description
 * Order creation validation features:
 * - Investment amount validation against offering parameters
 * - Investor eligibility and compliance verification
 * - Payment method validation and processing capability
 * - Offering availability and capacity checking
 * - Security validation and fraud prevention measures
 * - Regulatory compliance and KYC requirement verification
 *
 * Validated order data:
 * - Investment amount: Minimum/maximum limits and increment compliance
 * - Offering ID: Valid offering existence and availability verification
 * - Payment method: Supported payment options and processing validation
 * - Investor information: KYC status and eligibility verification
 * - Order type: Valid order type and processing method selection
 * - Wallet address: Blockchain address validation and format checking
 *
 * Business rule validations:
 * - Minimum and maximum investment thresholds
 * - Offering capacity and token availability
 * - Investor accreditation and jurisdiction compliance
 * - Payment method support and processing fees
 * - Lock-up periods and investment restrictions
 * - Platform limits and portfolio diversification rules
 *
 * // Middleware usage
 * router.post('/orders/create', createOrderValidationReq, orderController.createOrder);
 * ```
 */
export async function createOrderValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await orderValidation.createOrderValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'createOrderValidationReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Validates user order retrieval requests for portfolio management and order history.
 * Ensures proper pagination, filtering, and search parameters for order listing.
 * Provides secure access control and performance optimization for order queries.
 *
 * @async
 * @function getOrdersByUserValidationReq
 * @param {Request} req - Express request object containing query parameters
 * @param {Response} res - Express response object for error handling
 * @param {NextFunction} next - Express next function to continue middleware chain
 * @returns {Promise<PromiseResolve | void>} Promise resolving to validation result or void
 * @throws {CustomError} Throws custom error for validation failures
 *
 * @description
 * Order retrieval validation features:
 * - Pagination parameter validation and limit enforcement
 * - Filter parameter validation for order status and types
 * - Search query sanitization and format validation
 * - CSV export parameter validation and authorization
 * - Performance optimization through parameter limits
 * - Security validation to prevent unauthorized data access
 *
 * Validated query parameters:
 * - page: Page number for pagination (positive integer, reasonable limits)
 * - limit: Results per page (1-100 range for performance)
 * - status: Order status filter (PENDING, APPROVED, MINTED, REJECTED)
 * - search: Search term sanitization and length validation
 * - isCsv: CSV export flag validation and authorization checking
 * - sortBy: Allowed sort fields and direction validation
 *
 * Security and performance features:
 * - User authorization verification for order access
 * - Rate limiting integration for API abuse prevention
 * - Parameter sanitization to prevent injection attacks
 * - Query optimization through validated parameters
 * - Audit logging for order access patterns
 * - Data privacy protection for sensitive information
 *
 * // CSV export request
 * GET /api/orders?isCsv=true&status=APPROVED
 *
 * // Middleware usage
 * router.get('/orders', getOrdersByUserValidationReq, orderController.getOrdersByUser);
 * ```
 */
export async function getOrdersByUserValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const { page, limit, status, search } = req.query;
    const isCsv = req.query.isCsv === 'true';
    // Convert the query params to numbers and validate
    const validateRequest: JoiValidationResult = await orderValidation.getOrdersByUserValidation({
      page: page ? Number(page) : undefined,
      limit: limit ? Number(limit) : undefined,
      status: status ? String(status as string) : undefined,
      search: search ? String(search as string) : undefined,
      isCsv: isCsv ? Boolean(isCsv) : false,
    });

    if (validateRequest.error) {
      throw new CustomError(validateRequest.message, validateRequest.status);
    }

    // Replace req.query with the validated and possibly defaulted values
    req.query = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'getOrdersByUserValidationReq Error');

    return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Validates user order export requests for comprehensive portfolio reporting and tax filing.
 * Ensures proper date range validation and authorization for order data export.
 * Provides secure access control and data validation for sensitive financial exports.
 *
 * @async
 * @function exportUserOrdersValidationReq
 * @param {Request} req - Express request object containing export parameters
 * @param {Response} res - Express response object for error handling
 * @param {NextFunction} next - Express next function to continue middleware chain
 * @returns {Promise<PromiseResolve | void>} Promise resolving to validation result or void
 * @throws {CustomError} Throws custom error for validation failures
 *
 * @description
 * Order export validation features:
 * - Date range validation with reasonable limits and format checking
 * - Export format validation and file size limit enforcement
 * - User authorization verification for data export permissions
 * - Data privacy protection and PII handling compliance
 * - Performance optimization through parameter validation
 * - Audit logging for data export and download activities
 *
 * Validated export parameters:
 * - startDate: Valid start date for export range (ISO format)
 * - endDate: Valid end date for export range (ISO format)
 * - Date range validation: Reasonable timeframes and logical ordering
 * - Format specification: CSV, Excel, PDF export format validation
 * - Data scope: Specific order information and field selection
 * - Authorization level: User permission verification for data access
 *
 * Security and compliance features:
 * - User authentication and authorization verification
 * - Data privacy protection for sensitive financial information
 * - Export activity logging and audit trail maintenance
 * - Rate limiting for export operations to prevent abuse
 * - File size limits and performance optimization
 * - Secure file generation and temporary storage management
 *
 * // Tax year export
 * GET /api/orders/csv?startDate=2024-01-01T00:00:00.000Z&endDate=2024-12-31T23:59:59.000Z
 *
 * // Middleware usage
 * router.get('/orders/csv', exportUserOrdersValidationReq, orderController.exportOrders);
 * ```
 */
export async function exportUserOrdersValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const { startDate, endDate } = req.query;

    // Convert and validate the query parameters
    const validateRequest: JoiValidationResult = await orderValidation.exportUserOrdersValidation({ startDate: startDate ? new Date(startDate as string) : undefined, endDate: endDate ? new Date(endDate as string) : undefined });

    if (validateRequest.error) {
      throw new CustomError(validateRequest.message, validateRequest.status);
    }
    req.query = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'getOrdersByUserValidationReq Error');

    return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Validates portfolio performance analysis requests for comprehensive investment analytics.
 * Ensures proper parameter validation and authorization for portfolio performance data.
 * Provides secure access control and performance optimization for analytics queries.
 *
 * @async
 * @function getPortfolioPerformanceValidationReq
 * @param {Request} req - Express request object containing performance query parameters
 * @param {Response} res - Express response object for error handling
 * @param {NextFunction} next - Express next function to continue middleware chain
 * @returns {Promise<PromiseResolve | void>} Promise resolving to validation result or void
 * @throws {CustomError} Throws custom error for validation failures
 *
 * @description
 * Portfolio performance validation features:
 * - Time period validation for performance analysis (1M, 3M, 6M, 1Y, ALL)
 * - Calculation method validation and consistency checking
 * - User authorization verification for performance data access
 * - Performance metric selection and validation
 * - Benchmark comparison parameter validation
 * - Data aggregation period validation and optimization
 *
 * Validated performance parameters:
 * - period: Time period for analysis (1MONTH, 3MONTHS, 6MONTHS, 1YEAR, ALL)
 * - metrics: Performance metrics selection (returns, volatility, Sharpe ratio)
 * - benchmark: Benchmark comparison selection and validation
 * - currency: Currency for performance calculation (USD, EUR, etc.)
 * - includeUnrealized: Include unrealized gains/losses in calculations
 * - aggregationLevel: Daily, weekly, monthly aggregation for analysis
 *
 * Performance calculation features:
 * - Total return calculation with dividends and distributions
 * - Risk-adjusted performance metrics and ratios
 * - Benchmark comparison and relative performance analysis
 * - Portfolio allocation and diversification metrics
 * - Historical performance trending and volatility analysis
 * - Tax-adjusted performance calculations for reporting
 *
 * // Comprehensive performance analysis
 * GET /api/orders/portfolio-report?period=1YEAR&includeUnrealized=true
 *
 * // Middleware usage
 * router.get('/orders/portfolio-report', getPortfolioPerformanceValidationReq, orderController.getPerformance);
 * ```
 */
export async function getPortfolioPerformanceValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const { period } = req.query;

    // Convert the query params to numbers and validate
    const validateRequest: JoiValidationResult = await orderValidation.getPorfolioPerformanceValidation({ period: period ? String(period as string) : undefined });

    if (validateRequest.error) {
      throw new CustomError(validateRequest.message, validateRequest.status);
    }

    // Replace req.query with the validated and possibly defaulted values
    req.query = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'getPortfolioPerformanceValidationReq Error');

    return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Validates specific order retrieval requests by order ID for detailed order information.
 * Ensures proper order ID validation and user authorization for accessing individual orders.
 * Provides secure access control and parameter validation for order detail views.
 *
 * @async
 * @function getOrderByIdValidationReq
 * @param {Request} req - Express request object containing order ID parameter
 * @param {Response} res - Express response object for error handling
 * @param {NextFunction} next - Express next function to continue middleware chain
 * @returns {Promise<PromiseResolve | void>} Promise resolving to validation result or void
 * @throws {CustomError} Throws custom error for validation failures
 *
 * @description
 * Order ID validation features:
 * - Order ID format validation (MongoDB ObjectId format)
 * - Order existence verification and accessibility checking
 * - User authorization verification for order access
 * - Security validation to prevent unauthorized data access
 * - Access control based on order ownership and permissions
 * - Audit logging for order detail access attempts
 *
 * Validated parameters:
 * - orderId: MongoDB ObjectId format validation and existence checking
 * - User ownership: Verification that user owns or has access to the order
 * - Order status: Active order access control and visibility rules
 * - Permission level: Access permission verification based on user role
 * - Data privacy: Sensitive information protection and PII handling
 *
 * Security validations:
 * - Order ownership verification to prevent unauthorized access
 * - Role-based access control for different user types
 * - Data privacy protection for sensitive financial information
 * - Rate limiting integration for API abuse prevention
 * - Audit trail maintenance for order access patterns
 * - Parameter sanitization to prevent injection attacks
 *
 * // Middleware usage
 * router.get('/orders/:orderId', getOrderByIdValidationReq, orderController.getOrderById);
 * ```
 */
export async function getOrderByIdValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const { orderId } = req.params;

    // Validate order ID parameter
    const validateRequest: JoiValidationResult = await orderValidation.getOrderByIdValidation(orderId);

    if (validateRequest.error) {
      throw new CustomError(validateRequest.message, validateRequest.status);
    }

    // Replace req.params with the validated values
    req.params = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'getOrderByIdValidationReq Error');

    return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Validates order rejection requests for comprehensive order management and workflow control.
 * Ensures proper authorization and parameter validation for order rejection operations.
 * Provides secure access control and business rule enforcement for order status management.
 *
 * @async
 * @function rejectOrderValidationReq
 * @param {Request} req - Express request object containing rejection data
 * @param {Response} res - Express response object for error handling
 * @param {NextFunction} next - Express next function to continue middleware chain
 * @returns {Promise<PromiseResolve | void>} Promise resolving to validation result or void
 * @throws {CustomError} Throws custom error for validation failures
 *
 * @description
 * Order rejection validation features:
 * - Order ID validation and existence verification
 * - Rejection reason validation and categorization
 * - Authorization verification for rejection permissions
 * - Order status validation to ensure rejection is allowed
 * - Business rule enforcement for rejection workflows
 * - Audit trail maintenance for rejection activities
 *
 * Validated rejection data:
 * - orderId: Valid order identifier and existence verification
 * - rejectionReason: Required reason classification and detailed explanation
 * - rejectionCategory: Standardized category for reporting and analytics
 * - Authorization level: Permission verification for rejection operations
 * - Order status: Current status validation to allow rejection
 * - Notification settings: Automatic notification configuration
 *
 * Business rule validations:
 * - Order must be in rejectable status (PENDING, APPROVED)
 * - User must have appropriate permissions for rejection
 * - Rejection reason must be provided and categorized
 * - Refund processing rules and payment method validation
 * - Compliance requirements for rejection documentation
 * - Timeline restrictions and business hour validation
 *
 * Rejection workflow features:
 * - Automatic investor notification and communication
 * - Refund processing initiation and payment reversal
 * - Order status update and workflow state management
 * - Document generation for rejection records
 * - Audit logging and compliance documentation
 * - Analytics tracking for rejection patterns and trends
 *
 * // Middleware usage
 * router.put('/orders/reject', rejectOrderValidationReq, orderController.rejectOrder);
 * ```
 */
export async function rejectOrderValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await orderValidation.rejectOrderValidation(req.body);

    if (validateRequest.error) {
      throw new CustomError(validateRequest.message, validateRequest.status);
    }

    // Replace req.body with the validated values
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'rejectOrderValidationReq Error');

    return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}
