import { NextFunction, Request, Response } from 'express';
import DocusignValidation from '../component/docuSign/validation';
import CustomError from '../helpers/customError.helper';
import { ResponseHandler } from '../helpers/response.helper';
import { Jo<PERSON><PERSON>alidationR<PERSON>ult, PromiseResolve } from '../utils/common.interface';
import { RES_MSG, RESPONSES } from '../utils/responseUtils';

/**
 * Middleware to validate the request body for DocuSign subscription.
 * @param req - The request object containing the body to be validated.
 * @param res - The response object for sending error responses.
 * @param next - The next middleware function in the stack.
 * @returns A promise that resolves if the request is valid or sends an error response if not.
 */
export async function DocuSignValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await DocusignValidation.subscribeValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}
/**
 * Middleware to validate the request body for DocuSign webhook events.
 * @param req - The request object containing the webhook event body to be validated.
 * @param res - The response object for sending error responses.
 * @param next - The next middleware function in the stack.
 * @returns A promise that resolves if the request is valid or sends an error response if not.
 */
export async function DocuSignWebhookValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await DocusignValidation.DocusignWebhookValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}
