import { Request, Response, NextFunction } from 'express';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>R<PERSON>ult, PromiseResolve } from '../utils/common.interface';
import logger from '../helpers/logging/logger.helper';
import CustomError from '../helpers/customError.helper';
import { <PERSON><PERSON>and<PERSON> } from '../helpers/response.helper';
import { RES_MSG, RESPONSES } from '../utils/responseUtils';
import { RepresentativeRequestValidation } from '../component/representative/validation';
import { MultisigRequestValidation } from '../component/multisig/validation';

/**
 * Validates representative invitation requests for comprehensive delegation management.
 * Ensures proper invitation data validation, authorization, and role assignment verification.
 * Provides security validation and business rule enforcement for representative delegation.
 *
 * @async
 * @function inviteRequestValidationReq
 * @param {Request} req - Express request object containing representative invitation data
 * @param {Response} res - Express response object for error handling
 * @param {NextFunction} next - Express next function to continue middleware chain
 * @returns {Promise<PromiseResolve | void>} Promise resolving to validation result or void
 * @throws {CustomError} Throws custom error for validation failures
 *
 * @description
 * Representative invitation validation features:
 * - Representative information validation and format checking
 * - Role and permission assignment validation
 * - Delegation scope definition and authorization verification
 * - Email validation and invitation workflow configuration
 * - Security validation and conflict of interest checking
 * - Business rule enforcement for delegation policies
 *
 * Validated invitation data:
 * - Representative email: Valid email format and uniqueness verification
 * - Representative name: Name format validation and appropriateness checking
 * - Role assignment: Valid role type and permission scope verification
 * - Offering access: Specific offering delegation and access control
 * - Permission levels: Granular permission assignment and validation
 * - Expiration settings: Delegation timeline and automatic expiration
 *
 * Business rule validations:
 * - Maximum number of representatives per offering
 * - Role hierarchy and permission compatibility checking
 * - Conflict of interest detection and prevention
 * - Delegation approval workflow and authorization requirements
 * - Compliance requirements and regulatory validation
 * - Platform delegation policies and security standards
 *
 * Security features:
 * - Representative identity verification and background checking
 * - Permission scope limitation and access control
 * - Audit trail maintenance for delegation activities
 * - Real-time monitoring and alert systems
 * - Fraud prevention and authorization validation
 * - Secure invitation and onboarding workflow
 *
 * // Middleware usage
 * router.post('/represent/invite', inviteRequestValidationReq, representativeController.inviteRepresentative);
 * ```
 */
export async function inviteRequestValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const inviteData = req.body;

    // Validate representative request data using the RepresentativeRequestValidation schema
    const validateRequest: JoiValidationResult = await RepresentativeRequestValidation.inviteRequestValidation(inviteData);

    if (validateRequest.error) {
      throw new CustomError(validateRequest.message, validateRequest.status);
    }

    // Replace req.body with the validated data
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'inviteRequestValidationReq Error');

    return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Validates multi-signature wallet configuration requests for enhanced transaction security.
 * Ensures proper multisig setup validation, signer authorization, and security policy enforcement.
 * Provides comprehensive validation for multi-signature wallet management and operations.
 *
 * @async
 * @function addUpdateRequestValidation
 * @param {Request} req - Express request object containing multisig configuration data
 * @param {Response} res - Express response object for error handling
 * @param {NextFunction} next - Express next function to continue middleware chain
 * @returns {Promise<PromiseResolve | void>} Promise resolving to validation result or void
 * @throws {CustomError} Throws custom error for validation failures
 *
 * @description
 * Multi-signature validation features:
 * - Multisig configuration validation and setup verification
 * - Signer authorization and permission validation
 * - Signature threshold validation and security requirements
 * - Wallet address validation and ownership verification
 * - Security policy enforcement and compliance checking
 * - Business rule enforcement for multisig operations
 *
 * Validated multisig data:
 * - Wallet address: Primary wallet address validation and format checking
 * - Required signatures: Signature threshold validation (M-of-N scheme)
 * - Authorized signers: Signer address validation and authorization verification
 * - Transaction types: Valid transaction types requiring multisig approval
 * - Value thresholds: Minimum transaction amounts requiring multisig
 * - Emergency procedures: Backup access and recovery configuration
 *
 * Business rule validations:
 * - Minimum and maximum signature threshold requirements
 * - Signer diversity and independence requirements
 * - Value threshold appropriateness and risk management
 * - Emergency access configuration and security controls
 * - Compliance requirements and regulatory validation
 * - Platform multisig policies and security standards
 *
 * Security features:
 * - Signer identity verification and authorization checking
 * - Signature threshold security and best practice enforcement
 * - Anti-fraud measures and manipulation prevention
 * - Audit trail maintenance for multisig activities
 * - Real-time monitoring and security alerts
 * - Secure key management and backup procedures
 *
 * // Middleware usage
 * router.post('/represent/multisig', addUpdateRequestValidation, multisigController.configureMultisig);
 * ```
 */
export async function addUpdateRequestValidation(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const inviteData = req.body;

    // Validate multisig request data using the multisigRequestValidation schema
    const validateRequest: JoiValidationResult = await MultisigRequestValidation.addUpdateRequestValidation(inviteData);

    if (validateRequest.error) {
      throw new CustomError(validateRequest.message, validateRequest.status);
    }

    // Replace req.body with the validated data
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'inviteRequestValidationReq Error');

    return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}
