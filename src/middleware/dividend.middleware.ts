import { NextFunction, Request, Response } from 'express';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PromiseResolve } from '../utils/common.interface';
import DividentsValidation from '../component/dividends/validation';
import CustomError from '../helpers/customError.helper';
import { <PERSON><PERSON>and<PERSON> } from '../helpers/response.helper';
import { RES_MSG, RESPONSES } from '../utils/responseUtils';

/**
 * Validates dividend creation requests for comprehensive dividend distribution management.
 * Ensures proper dividend data validation, authorization, and business rule enforcement.
 * Provides security validation and compliance checking for dividend announcement and distribution.
 *
 * @async
 * @function DividendValidationReq
 * @param {Request} req - Express request object containing dividend creation data
 * @param {Response} res - Express response object for error handling
 * @param {NextFunction} next - Express next function to continue middleware chain
 * @returns {Promise<PromiseResolve | void>} Promise resolving to validation result or void
 * @throws {CustomError} Throws custom error for validation failures
 *
 * @description
 * Dividend creation validation features:
 * - Dividend amount validation and calculation verification
 * - Record date and payment date validation with timeline checking
 * - Offering validation and token holder eligibility verification
 * - Financial calculation validation and fee structure checking
 * - Regulatory compliance and tax implications validation
 * - Business rule enforcement for dividend policies
 *
 * Validated dividend data:
 * - Offering ID: Valid offering existence and dividend eligibility
 * - Dividend amount: Total distribution amount and per-share calculations
 * - Record date: Holder eligibility cutoff date validation
 * - Payment date: Distribution timeline and processing date validation
 * - Currency type: Supported currency validation and exchange rates
 * - Distribution method: Payment method and processing validation
 *
 * Business rule validations:
 * - Minimum dividend amount thresholds and distribution viability
 * - Record date timeline requirements and advance notice periods
 * - Token holder eligibility and ownership verification
 * - Dividend frequency restrictions and policy compliance
 * - Tax withholding requirements and regulatory compliance
 * - Platform fee calculations and distribution cost analysis
 *
 * // Middleware usage
 * router.post('/dividends', DividendValidationReq, dividendController.createDividend);
 * ```
 */
export async function DividendValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await DividentsValidation.createDividend(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Validates dividend retrieval requests for comprehensive dividend management dashboards.
 * Ensures proper query parameter validation and authorization for dividend data access.
 * Provides secure access control and performance optimization for dividend queries.
 *
 * @async
 * @function DividendFetchValidationReq
 * @param {Request} req - Express request object containing dividend query parameters
 * @param {Response} res - Express response object for error handling
 * @param {NextFunction} next - Express next function to continue middleware chain
 * @returns {Promise<PromiseResolve | void>} Promise resolving to validation result or void
 * @throws {CustomError} Throws custom error for validation failures
 *
 * @description
 * Dividend fetch validation features:
 * - Pagination parameter validation and performance optimization
 * - Filter parameter validation for status, offering, and date ranges
 * - Search query sanitization and format validation
 * - Authorization verification for dividend data access
 * - Performance optimization through parameter limits
 * - Audit logging for dividend access patterns
 *
 * Validated query parameters:
 * - page: Page number for pagination (positive integer, reasonable limits)
 * - limit: Results per page (1-100 range for performance)
 * - status: Dividend status filter (ANNOUNCED, DISTRIBUTED, CANCELLED)
 * - offeringId: Specific offering filter with access verification
 * - startDate: Date range filter start (ISO format validation)
 * - endDate: Date range filter end (ISO format validation)
 * - sortBy: Allowed sort fields (date, amount, status)
 * - sortOrder: Sort direction validation (asc, desc)
 *
 * Authorization and security features:
 * - User permission verification for dividend data access
 * - Role-based access control (issuer vs investor permissions)
 * - Data privacy protection for sensitive financial information
 * - Rate limiting integration for API abuse prevention
 * - Parameter sanitization to prevent injection attacks
 * - Audit trail maintenance for dividend access monitoring
 *
 * // Date range dividend query
 * GET /api/dividends?startDate=2024-01-01&endDate=2024-12-31&sortBy=paymentDate
 *
 * // Middleware usage
 * router.get('/dividends', DividendFetchValidationReq, dividendController.getDividends);
 * ```
 */
export async function DividendFetchValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await DividentsValidation.getDividendsValidation(req.query);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.query = validateRequest.value;
    next();
  } catch (error: any) {
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Validates dividend retrieval requests specifically for investor stakeholder access.
 * Ensures proper investor authorization and parameter validation for dividend information.
 * Provides secure access control tailored for investor dividend data and analytics.
 *
 * @async
 * @function DividendInvestorFetchValidationReq
 * @param {Request} req - Express request object containing investor dividend query parameters
 * @param {Response} res - Express response object for error handling
 * @param {NextFunction} next - Express next function to continue middleware chain
 * @returns {Promise<PromiseResolve | void>} Promise resolving to validation result or void
 * @throws {CustomError} Throws custom error for validation failures
 *
 * @description
 * Investor dividend validation features:
 * - Investor-specific authorization and access control
 * - Token holding verification and dividend eligibility checking
 * - Portfolio-specific dividend filtering and analytics
 * - Tax reporting parameter validation and compliance
 * - Privacy protection for investor-specific financial data
 * - Performance optimization for investor dashboard queries
 *
 * Validated investor parameters:
 * - investorId: Investor identification and authorization verification
 * - offeringId: Offering access verification and token holding validation
 * - page: Pagination with investor-appropriate limits
 * - limit: Results per page optimized for investor interfaces
 * - year: Tax year filtering for investor reporting
 * - currency: Currency preference for dividend display
 * - includeProjected: Include projected dividend calculations
 *
 * Investor-specific features:
 * - Token holding verification for dividend eligibility
 * - Per-share dividend calculation and display
 * - Historical dividend performance tracking
 * - Tax reporting information and documentation
 * - Projected dividend estimates and forecasting
 * - Portfolio allocation and diversification metrics
 *
 * // Investor portfolio dividend summary
 * GET /api/dividends/investors?includeProjected=true&currency=USD
 *
 * // Middleware usage
 * router.get('/dividends/investors', DividendInvestorFetchValidationReq, dividendController.getInvestorDividends);
 * ```
 */
export async function DividendInvestorFetchValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await DividentsValidation.getDividendsInvestorValidation(req.query);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.query = validateRequest.value;
    next();
  } catch (error: any) {
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Validates dividend calculation requests for precise financial distribution analysis.
 * Ensures proper parameter validation and authorization for dividend calculation operations.
 * Provides comprehensive validation for dividend amount calculations and distribution feasibility.
 *
 * @async
 * @function DividendCalculateValidationReq
 * @param {Request} req - Express request object containing dividend calculation parameters
 * @param {Response} res - Express response object for error handling
 * @param {NextFunction} next - Express next function to continue middleware chain
 * @returns {Promise<PromiseResolve | void>} Promise resolving to validation result or void
 * @throws {CustomError} Throws custom error for validation failures
 *
 * @description
 * Dividend calculation validation features:
 * - Total dividend amount validation and feasibility checking
 * - Token holder count verification and distribution viability
 * - Per-share calculation validation and precision requirements
 * - Fee calculation validation and cost analysis
 * - Gas fee estimation validation for blockchain distributions
 * - Liquidity assessment and funding source verification
 *
 * Validated calculation parameters:
 * - offeringId: Valid offering with active token holders
 * - totalAmount: Total dividend pool amount and validation
 * - recordDate: Holder eligibility date for calculations
 * - includeFees: Fee inclusion flag for accurate calculations
 * - currency: Distribution currency and exchange rate validation
 * - distributionMethod: Payment method and processing validation
 *
 * Financial calculation features:
 * - Precise per-share dividend amount calculations
 * - Platform fee and processing cost calculations
 * - Gas fee estimation for blockchain transactions
 * - Tax withholding calculations and compliance
 * - Minimum distribution threshold validation
 * - Rounding and precision handling for financial accuracy
 *
 * // Middleware usage
 * router.post('/dividends/calculate', DividendCalculateValidationReq, dividendController.calculateDividend);
 * ```
 */
export async function DividendCalculateValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await DividentsValidation.getDividendCalculateValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Validate the request query parameters for fetching dividend history.
 * @param req - The request object containing the query parameters.
 * @param res - The response object for sending error responses.
 * @param next - The next middleware function in the stack.
 * @returns A promise that resolves if the request is valid or sends an error response if not.
 */
export async function DividendHistoryValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await DividentsValidation.history(req.query);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Validates investor dividend history retrieval requests for comprehensive portfolio tracking.
 * Ensures proper investor authorization and parameter validation for dividend history access.
 * Provides secure access control and data validation for investor dividend analytics and reporting.
 *
 * @async
 * @function investorDividendHistoryValidationReq
 * @param {Request} req - Express request object containing dividend history query parameters
 * @param {Response} res - Express response object for error handling
 * @param {NextFunction} next - Express next function to continue middleware chain
 * @returns {Promise<PromiseResolve | void>} Promise resolving to validation result or void
 * @throws {CustomError} Throws custom error for validation failures
 *
 * @description
 * Investor dividend history validation features:
 * - Investor-specific authorization and data access control
 * - Historical dividend data filtering and date range validation
 * - Portfolio-specific dividend performance analytics
 * - Tax reporting parameter validation and compliance
 * - Export format validation for investor record keeping
 * - Privacy protection for sensitive financial information
 *
 * Validated history parameters:
 * - investorId: Investor identification and authorization verification
 * - offeringId: Optional offering filter with access verification
 * - startDate: Historical data range start (ISO format validation)
 * - endDate: Historical data range end (ISO format validation)
 * - page: Pagination for large dividend histories
 * - limit: Results per page optimized for investor interfaces
 * - status: Dividend status filter (DISTRIBUTED, PENDING, CANCELLED)
 * - year: Tax year filtering for annual reporting
 *
 * Investor analytics features:
 * - Total dividend income calculations and summaries
 * - Yield performance tracking and trend analysis
 * - Tax reporting information and documentation
 * - Portfolio allocation impact from dividend distributions
 * - Reinvestment tracking and compounding analysis
 * - Performance comparison across multiple offerings
 *
 * // Portfolio dividend history with date range
 * GET /api/dividends/investor-history?startDate=2024-01-01&endDate=2024-12-31&offeringId=507f1f77bcf86cd799439011
 *
 * // Middleware usage
 * router.get('/dividends/investor-history', investorDividendHistoryValidationReq, dividendController.getInvestorHistory);
 * ```
 */
export async function investorDividendHistoryValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await DividentsValidation.getInvestorDividendHistoryValidation(req.query);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.query = validateRequest.value;
    next();
  } catch (error: any) {
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}
