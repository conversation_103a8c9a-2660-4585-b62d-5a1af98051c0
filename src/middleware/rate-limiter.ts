import * as express from 'express';
import { rateLimit } from 'express-rate-limit';
import { ResponseHandler } from '../helpers/response.helper';
import { RESPONSES } from '../utils/responseUtils';

/**
 * Creates a configurable rate limiting middleware for API endpoint protection.
 * Implements intelligent rate limiting based on user authentication status with
 * fallback to IP-based limiting. Provides comprehensive protection against
 * abuse, DDoS attacks, and API misuse with customizable thresholds and responses.
 *
 * @function createRateLimiter
 * @param {Object} options - Rate limiter configuration options
 * @param {number} [options.windowMs=900000] - Time window in milliseconds (default: 15 minutes)
 * @param {number} [options.max=200] - Maximum requests per window (default: 200)
 * @param {string} [options.message] - Custom error message for rate limit exceeded
 * @returns {Function} Express rate limiting middleware function
 *
 * @description
 * Rate limiting features:
 * - User-based rate limiting for authenticated requests
 * - IP-based fallback for anonymous requests
 * - Configurable time windows and request limits
 * - Standard HTTP headers for rate limit information
 * - Custom error responses with platform consistency
 * - Health check endpoint exemptions
 * - X-Forwarded-For header support for proxy environments
 *
 * Security benefits:
 * - Prevents brute force attacks on authentication endpoints
 * - Protects against DDoS and API abuse
 * - Ensures fair usage across all platform users
 * - Maintains service availability under high load
 * - Provides audit trail for suspicious activity
 * - Supports distributed deployment scenarios
 *
 * Configuration strategies:
 * - Stricter limits for sensitive endpoints (auth, payment)
 * - Generous limits for public information endpoints
 * - User-specific limits based on subscription tiers
 * - Geographic or network-based rate limiting
 * - Burst protection with sliding window algorithms
 *
 * // Apply to authentication routes
 * app.use('/api/auth/login', authLimiter);
 *
 * // Create general API rate limiter
 * const apiLimiter = createRateLimiter({
 *   windowMs: 60 * 1000, // 1 minute
 *   max: 100, // 100 requests per minute
 *   message: 'API rate limit exceeded'
 * });
 *
 * // Apply to all API routes
 * app.use('/api', apiLimiter);
 *
 * // Create permissive limiter for public endpoints
 * const publicLimiter = createRateLimiter({
 *   windowMs: 15 * 60 * 1000, // 15 minutes
 *   max: 1000, // 1000 requests per window
 *   message: 'Public API rate limit exceeded'
 * });
 * ```
 */
export const createRateLimiter = (options: {
  windowMs?: number; // Time window in milliseconds
  max?: number; // Maximum requests per window
  message?: string; // Custom error message
}) => {
  const {
    windowMs = 15 * 60 * 1000, // Default: 15 minutes
    max = 200, // Default: 200 requests per windowMs
    message = 'Too many requests, please try again later',
  } = options;

  return rateLimit({
    windowMs,
    max,
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers

    // Use user ID as the key if available, fallback to IP
    keyGenerator: (req: any) => {
      // If user is authenticated, use their ID
      if (req.user && req.user.id) {
        return req.user.id.toString();
      }

      // Fallback to IP address
      return req.ip || (req.headers['x-forwarded-for'] as string) || '127.0.0.1';
    },

    // Custom handler for when rate limit is exceeded
    handler: (req: any, res: express.Response<any, Record<string, any>>) => {
      ResponseHandler.error(res, {
        error: true,
        message,
        status: RESPONSES.TOO_MANY_REQUESTS,
      });
    },

    // Skip rate limiting for certain paths if needed
    skip: (req: { originalUrl: string | string[] }) => {
      // Don't rate limit health check endpoint
      return req.originalUrl.includes('/health-check');
    },
  });
};

/**
 * Configures comprehensive rate limiting strategy for the entire application.
 * Implements layered rate limiting approach with different thresholds for
 * various endpoint categories. Provides baseline protection while maintaining
 * optimal user experience for legitimate platform usage.
 *
 * @function configureRateLimiting
 * @param {express.Application} app - Express application instance to configure
 * @returns {void}
 *
 * @description
 * Application-wide rate limiting strategy:
 * - Default rate limiter applied to all routes as baseline protection
 * - Configurable limits based on endpoint sensitivity and usage patterns
 * - Automatic scaling based on deployment environment
 * - Integration with monitoring and alerting systems
 * - Support for whitelist and blacklist management
 *
 * Rate limiting tiers:
 * - Public endpoints: Generous limits for information access
 * - Authentication: Strict limits to prevent brute force attacks
 * - Investment operations: Moderate limits for legitimate trading
 * - Administrative functions: Restrictive limits for security
 * - File uploads: Special limits based on file size and type
 *
 * Monitoring and analytics:
 * - Rate limit hit tracking and alerting
 * - User behavior analysis and anomaly detection
 * - Geographic and temporal usage patterns
 * - Automatic adjustment based on traffic patterns
 * - Integration with security incident response
 *
 * const app = express();
 *
 * // Configure comprehensive rate limiting
 * configureRateLimiting(app);
 *
 * // Additional endpoint-specific rate limiters can be added after
 * const strictAuthLimiter = createRateLimiter({
 *   windowMs: 15 * 60 * 1000,
 *   max: 5,
 *   message: 'Too many authentication attempts'
 * });
 *
 * app.use('/api/auth', strictAuthLimiter);
 *
 * // Rate limiting is now active across the application
 * app.listen(3000, () => {
 *   console.log('Server running with comprehensive rate limiting');
 * });
 * ```
 */
export function configureRateLimiting(app: express.Application): void {
  // Default rate limiter for all routes
  const defaultLimiter = createRateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // 100 requests per 15 minutes
  });

  // Default limiter for all other routes
  app.use(defaultLimiter);
}
