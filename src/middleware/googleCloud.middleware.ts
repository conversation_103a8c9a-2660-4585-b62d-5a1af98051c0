/* eslint-disable no-undef */
import * as multer from 'multer';
import { Request, Response, NextFunction } from 'express';
import CONF<PERSON> from '../config/env';
import { ResponseHandler } from '../helpers/response.helper';
import { RESPONSES } from '../utils/responseUtils';

/**
 * Multer memory storage configuration for file upload handling.
 * Stores uploaded files in memory for immediate processing and cloud upload.
 * Provides temporary storage before files are transferred to permanent storage.
 *
 * @constant {multer.StorageEngine} storage - Memory storage engine for file uploads
 */
const storage = multer.memoryStorage();

/**
 * Type definition for multer file filter callback function.
 * Defines the callback signature for file validation and acceptance decisions.
 *
 * @typedef {Function} FileFilterCallback
 * @param {Error | null} error - Error object if validation fails, null otherwise
 * @param {boolean} acceptFile - Whether to accept the uploaded file
 */
type FileFilterCallback = (error: Error | null, acceptFile: boolean) => void;

/**
 * File filter for validating image and PDF file uploads.
 * Accepts JPG, JPEG, PNG, and PDF file formats for document and image uploads.
 * Provides comprehensive file type validation for offering documentation and user uploads.
 *
 * @function imageFilter
 * @param {Request} req - Express request object
 * @param {Express.Multer.File} file - Uploaded file object with metadata
 * @param {FileFilterCallback} cb - Callback function to accept or reject file
 * @returns {void}
 *
 * @description
 * File validation features:
 * - Supports JPG, JPEG, PNG image formats for visual content
 * - Accepts PDF documents for legal and compliance documentation
 * - Case-insensitive file extension matching
 * - Secure file type validation based on file extensions
 * - Prevents upload of potentially dangerous file types
 *
 * Accepted file types:
 * - .jpg, .JPG - JPEG image format
 * - .jpeg, .JPEG - JPEG image format (alternative extension)
 * - .png, .PNG - PNG image format with transparency support
 * - .pdf, .PDF - PDF document format for legal documents
 *
 * Security considerations:
 * - File extension validation prevents executable uploads
 * - Memory storage limits exposure time of uploaded content
 * - Integration with virus scanning and content validation
 * - Audit logging for all file upload attempts
 *
 * // File will be rejected
 * const invalidFiles = [
 *   'script.js', 'executable.exe', 'document.docx', 'archive.zip'
 * ];
 * ```
 */
const imageFilter = (req: Request, file: Express.Multer.File, cb: FileFilterCallback) => {
  if (!file.originalname.match(/\.(JPG|jpg|jpeg|JPEG|png|PNG|PDF|pdf)$/)) {
    return cb(null, false); // Reject the file
  }
  cb(null, true);
};

/**
 * Strict file filter for image-only uploads (JPG and PNG only).
 * Restricts uploads to image formats only, excluding PDF documents.
 * Used for profile pictures, logos, and other visual content where only images are appropriate.
 *
 * @function imageOnlyFilter
 * @param {Request} req - Express request object
 * @param {Express.Multer.File} file - Uploaded file object with metadata
 * @param {FileFilterCallback} cb - Callback function to accept or reject file
 * @returns {void}
 *
 * @description
 * Image-only validation features:
 * - Strictly limited to visual image formats
 * - Excludes document formats for security and use case clarity
 * - Case-insensitive extension matching for user convenience
 * - Optimized for profile pictures and visual content uploads
 * - Prevents document upload in image-only contexts
 *
 * Accepted image formats:
 * - .jpg, .JPG - JPEG image format for photographs
 * - .jpeg, .JPEG - JPEG alternative extension
 * - .png, .PNG - PNG format with transparency support
 *
 * Use cases:
 * - User profile pictures and avatars
 * - Company logos and branding images
 * - Property and asset photographs
 * - Marketing and promotional images
 * - UI elements and graphics
 *
 * // Files rejected by imageOnlyFilter
 * const rejectedFiles = [
 *   'document.pdf', 'contract.PDF', 'script.js', 'data.json'
 * ];
 * ```
 */
const imageOnlyFilter = (req: Request, file: Express.Multer.File, cb: FileFilterCallback) => {
  if (!file.originalname.match(/\.(JPG|jpg|jpeg|JPEG|png|PNG)$/)) {
    return cb(null, false); // Reject the file
  }
  cb(null, true);
};

/**
 * Maximum file size configuration from environment settings.
 * Defines upload size limits to prevent abuse and ensure system performance.
 * Configurable through environment variables for different deployment scenarios.
 *
 * @constant {Object} maxSize - File size limit configuration
 * @property {number} fileSize - Maximum file size in bytes (converted from MB)
 */
const maxSize = { fileSize: Number(CONFIG.GOOGLE.MAX_SIZE) * 1024 * 1024 };

/**
 * Creates a comprehensive file upload handler with validation and error management.
 * Provides secure file upload processing with configurable file type filtering,
 * size limits, and comprehensive error handling for production environments.
 *
 * @function uploadHandler
 * @param {Function} fileFilter - File filter function for type validation
 * @returns {Function} Express middleware function for file upload handling
 *
 * @description
 * Upload handler features:
 * - Configurable file type filtering with custom validation rules
 * - Memory storage for immediate processing and cloud transfer
 * - Comprehensive error handling with user-friendly messages
 * - File size limit enforcement with configurable thresholds
 * - Integration with cloud storage and processing workflows
 * - Security validations and audit logging
 *
 * Error handling:
 * - File size exceeded: Clear message with size limit information
 * - Invalid file type: Rejection with supported format guidance
 * - Upload failures: Graceful handling with error recovery
 * - Network issues: Timeout and retry logic integration
 * - Security violations: Immediate rejection and logging
 *
 * Performance optimizations:
 * - Memory-based storage for fast processing
 * - Streaming uploads for large files
 * - Parallel processing capabilities
 * - Resource cleanup and garbage collection
 * - Monitoring and metrics collection
 *
 * // Create handler for image-only uploads
 * const imageUploadHandler = uploadHandler(imageOnlyFilter);
 *
 * // Custom file filter for specific use case
 * const documentFilter = (req, file, cb) => {
 *   if (file.originalname.match(/\.(pdf|doc|docx)$/i)) {
 *     cb(null, true);
 *   } else {
 *     cb(null, false);
 *   }
 * };
 *
 * const documentHandler = uploadHandler(documentFilter);
 *
 * // Use in routes
 * app.post('/upload/image', imageUploadHandler, (req, res) => {
 *   // Handle uploaded image
 * });
 * ```
 */
const uploadHandler = (fileFilter: (req: Request, file: Express.Multer.File, cb: FileFilterCallback) => void) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const upload = multer({ storage, fileFilter, limits: maxSize }).single('file');

    upload(req, res, (err) => {
      // Handle multer-specific errors
      if (err instanceof multer.MulterError) {
        if (err.code === 'LIMIT_FILE_SIZE') {
          return ResponseHandler.error(res, { message: `File size should not exceed ${CONFIG.GOOGLE.MAX_SIZE} MB.`, status: RESPONSES.BAD_REQUEST, error: true });
        }
      } else if (err) {
        return ResponseHandler.error(res, { message: err.message || 'An unknown error occurred during file upload.', status: RESPONSES.BAD_REQUEST, error: true });
      }
      next();
    });
  };
};

/**
 * General file upload validation middleware for images and PDF documents.
 * Supports comprehensive file uploads including legal documents, images, and forms.
 * Provides flexible file type validation for various platform upload requirements.
 *
 * @constant {Function} validateFiles - Express middleware for general file validation
 *
 * @description
 * General file validation features:
 * - Accepts both image and document formats
 * - Suitable for offering documentation and user uploads
 * - Comprehensive file type support for business requirements
 * - Integration with document management workflows
 * - Support for legal and compliance documentation
 *
 * Supported file types:
 * - Images: JPG, JPEG, PNG for visual content
 * - Documents: PDF for legal and business documentation
 * - Case-insensitive validation for user convenience
 * - Configurable size limits through environment settings
 *
 * Use cases:
 * - Offering documentation upload
 * - Legal document submission
 * - Compliance form uploads
 * - Business document management
 * - Mixed content upload workflows
 *
 * const app = express();
 *
 * // Apply to document upload routes
 * app.post('/api/documents/upload', validateFiles, (req, res) => {
 *   // Handle uploaded document or image
 *   if (req.file) {
 *     console.log('File uploaded:', req.file.originalname);
 *     console.log('File size:', req.file.size);
 *     console.log('MIME type:', req.file.mimetype);
 *   }
 * });
 *
 * // Use for offering document uploads
 * app.post('/api/offerings/:id/documents', validateFiles, offeringController.uploadDocument);
 * ```
 */
export const validateFiles = uploadHandler(imageFilter);

/**
 * Image-only file upload validation middleware for visual content.
 * Restricts uploads to image formats only, ideal for profile pictures, logos, and visual assets.
 * Provides strict image validation for UI elements and user-generated visual content.
 *
 * @constant {Function} validateImageFiles - Express middleware for image-only validation
 *
 * @description
 * Image-only validation features:
 * - Strict image format validation (JPG, JPEG, PNG only)
 * - Optimized for visual content and user interface elements
 * - Enhanced security through format restriction
 * - Perfect for profile pictures and branding assets
 * - Integration with image processing and optimization workflows
 *
 * Security benefits:
 * - Prevents document upload in image contexts
 * - Reduces attack surface through format limitation
 * - Enables specialized image processing pipelines
 * - Supports automated image optimization and resizing
 * - Integration with content delivery networks (CDN)
 *
 * Recommended usage:
 * - User profile picture uploads
 * - Company logo and branding uploads
 * - Property and asset photography
 * - Marketing material uploads
 * - UI component image uploads
 *
 * const app = express();
 *
 * // Profile picture upload
 * app.post('/api/users/profile/picture', validateImageFiles, (req, res) => {
 *   // Handle profile picture upload
 *   if (req.file) {
 *     // Process image upload
 *     console.log('Profile picture uploaded:', req.file.originalname);
 *   }
 * });
 *
 * // Company logo upload
 * app.post('/api/company/logo', validateImageFiles, (req, res) => {
 *   // Handle company logo upload
 * });
 *
 * // Property image upload
 * app.post('/api/properties/:id/images', validateImageFiles, propertyController.uploadImage);
 * ```
 */
export const validateImageFiles = uploadHandler(imageOnlyFilter);
