/* eslint-disable @typescript-eslint/no-explicit-any */
import { Request, Response, NextFunction } from 'express';
import { Jo<PERSON>ValidationResult, PromiseResolve } from '../utils/common.interface';
import { RES_MSG, RESPONSES } from '../utils/responseUtils';
import { <PERSON>Hand<PERSON> } from '../helpers/response.helper';
import logger from '../helpers/logging/logger.helper';
import CustomError from '../helpers/customError.helper';
import RedeemValidation from '../component/redeem/validation';

/**
 * Validates token redemption request creation for comprehensive redemption management.
 * Ensures proper redemption data validation, authorization, and liquidity compliance checking.
 * Provides security validation and business rule enforcement for token redemption operations.
 *
 * @async
 * @function createRedeemReq
 * @param {Request} req - Express request object containing redemption request data
 * @param {Response} res - Express response object for error handling
 * @param {NextFunction} next - Express next function to continue middleware chain
 * @returns {Promise<PromiseResolve | void>} Promise resolving to validation result or void
 * @throws {CustomError} Throws custom error for validation failures
 *
 * @description
 * Redemption creation validation features:
 * - Token balance verification and redemption amount validation
 * - Redemption eligibility checking and lock-up period compliance
 * - Liquidity pool validation and funding source verification
 * - Redemption pricing validation and fee calculation
 * - Authorization verification for redemption permissions
 * - Business rule enforcement for redemption policies
 *
 * Validated redemption data:
 * - Offering ID: Valid offering with redeemable tokens
 * - Redemption quantity: Token amount validation against available balance
 * - Redemption type: Type validation (PARTIAL, FULL, EMERGENCY)
 * - Wallet address: Source wallet validation and ownership verification
 * - Payment method: Preferred payment method validation for proceeds
 * - Reason: Redemption reason validation and categorization
 *
 * Business rule validations:
 * - Minimum and maximum redemption amount thresholds
 * - Lock-up period compliance and transfer restrictions
 * - Liquidity availability and funding source verification
 * - Redemption fees and processing cost calculations
 * - Market timing restrictions and trading window compliance
 * - Platform redemption policies and rate limiting
 *
 * Security features:
 * - Token ownership verification and balance checking
 * - Anti-fraud measures and suspicious activity detection
 * - Rate limiting for redemption operations
 * - Audit trail maintenance for regulatory compliance
 * - Real-time monitoring and alert systems
 * - Risk assessment and compliance screening
 *
 * // Middleware usage
 * router.post('/redeem', createRedeemReq, redeemController.createRedemption);
 * ```
 */
export async function createRedeemReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await RedeemValidation.validateCreateRedeem(req.body as Record<string, any>);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.query = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'createRedeemReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Validates redemption request retrieval for comprehensive redemption management dashboards.
 * Ensures proper query parameter validation and authorization for redemption data access.
 * Provides secure access control and performance optimization for redemption analytics.
 *
 * @async
 * @function validateGetRedeemReq
 * @param {Request} req - Express request object containing redemption query parameters
 * @param {Response} res - Express response object for error handling
 * @param {NextFunction} next - Express next function to continue middleware chain
 * @returns {Promise<PromiseResolve | void>} Promise resolving to validation result or void
 * @throws {CustomError} Throws custom error for validation failures
 *
 * @description
 * Redemption retrieval validation features:
 * - Pagination parameter validation and performance optimization
 * - Filter parameter validation for status, type, and date ranges
 * - Search query sanitization and format validation
 * - Authorization verification for redemption data access
 * - Performance optimization through parameter limits
 * - Audit logging for redemption access patterns
 *
 * Validated query parameters:
 * - userId/offeringId: Valid identifier for redemption filtering
 * - page: Page number for pagination (positive integer, reasonable limits)
 * - limit: Results per page (1-100 range for performance)
 * - status: Redemption status filter (PENDING, APPROVED, REJECTED, COMPLETED)
 * - redemptionType: Redemption type filter (PARTIAL, FULL, EMERGENCY)
 * - startDate: Date range filter start (ISO format validation)
 * - endDate: Date range filter end (ISO format validation)
 * - search: Search term sanitization for user or redemption data
 *
 * Security and performance features:
 * - User authorization verification for redemption data access
 * - Role-based access control (investor vs issuer permissions)
 * - Rate limiting integration for API abuse prevention
 * - Parameter sanitization to prevent injection attacks
 * - Query optimization through validated parameters
 * - Data privacy protection for sensitive redemption information
 *
 * Redemption analytics capabilities:
 * - Redemption volume and frequency analysis
 * - Processing time tracking and efficiency metrics
 * - Success rate monitoring and approval analytics
 * - Liquidity impact assessment and planning
 * - Compliance reporting and audit trail maintenance
 * - Real-time redemption monitoring and alerting
 *
 * // Issuer redemption dashboard
 * GET /api/redeem/orders/507f1f77bcf86cd799439011?page=1&limit=20&redemptionType=PARTIAL
 *
 * // Middleware usage
 * router.get('/redeem/:id', validateGetRedeemReq, redeemController.getRedemptions);
 * router.get('/redeem/orders/:id', validateGetRedeemReq, redeemController.getRedemptionOrders);
 * ```
 */
export async function validateGetRedeemReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await RedeemValidation.validateGetRedeem(req.query as Record<string, any>);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.query = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'validateGetRedeemReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}
