import { Types } from 'mongoose';
import { KafkaMessage, Consumer } from 'kafkajs';
import { kafkaHelperService } from '../helpers/kafka.helper';
import logger from '../helpers/logging/logger.helper';
import OfferingService from '../component/offerings/service';
import UserService from '../component/userAuthentications/service';
import OrderService from '../component/order/service';
import TransferRequestService from '../component/transfer/service';
import DividendsService from '../component/dividends/service';

/**
 * Kafka service class for real-time message processing and inter-service communication.
 * Handles asynchronous message consumption from Kafka topics and routes messages to appropriate services.
 * Provides reliable event-driven communication between microservices and external systems.
 * Supports various message types including offerings, orders, transfers, dividends, and user operations.
 *
 * @class KafkaService
 * @description Enterprise-grade message processing service with Kafka integration
 *
 * // Service automatically:
 * // 1. Connects to Kafka cluster
 * // 2. Subscribes to configured topics
 * // 3. Processes incoming messages
 * // 4. Routes to appropriate business services
 * // 5. Handles errors and retries
 *
 * // Message processing flow:
 * // Kafka Topic -> KafkaService -> Business Service -> Database Update
 * ```
 */
class KafkaService {
  /**
   * Kafka consumer instance for message consumption.
   * Handles connection to Kafka cluster and message processing.
   *
   * @private
   * @type {Consumer | null}
   * @memberof KafkaService
   */
  private consumer: Consumer | null = null;

  /**
   * Initializes the Kafka service with automatic consumer setup.
   * Establishes connections to Kafka topics and prepares message handlers.
   *
   * @constructor
   * @description
   * Initialization process:
   * 1. Creates Kafka consumer instances
   * 2. Subscribes to required topics
   * 3. Sets up message handlers
   * 4. Starts listening for messages
   * 5. Handles connection errors gracefully
   */
  constructor() {
    this.initialize();
  }

  /**
   * Initializes Kafka consumer connections and topic subscriptions.
   * Sets up message processing pipeline for real-time event handling.
   * Configures error handling and retry mechanisms for reliable message processing.
   *
   * @private
   * @async
   * @method initialize
   * @memberof KafkaService
   * @throws {Error} Throws error if Kafka consumer initialization fails
   *
   * @description
   * Consumer setup includes:
   * - Topic subscription configuration
   * - Message handler registration
   * - Error handling and retry logic
   * - Connection health monitoring
   * - Graceful shutdown handling
   *
   * Configured topics:
   * - cron-to-user: Scheduled task results and updates
   * - admin-to-user: Administrative actions and notifications
   * - system-events: System-wide event notifications
   *
   */
  private async initialize() {
    try {
      this.consumer = await this.setupConsumer('cron-to-user', this.handleCronMessage.bind(this));
      // this.consumer = await this.setupConsumer('admin-to-user', this.handleCronMessage.bind(this));
    } catch (error) {
      logger.error(error, 'Error initializing Kafka consumer for user:');
      throw error;
    }
  }

  /**
   * Sets up a Kafka consumer for specific topic with message handler configuration.
   * Creates consumer groups, configures message processing, and establishes error handling.
   * Provides reliable message consumption with automatic offset management.
   *
   * @private
   * @async
   * @method setupConsumer
   * @param {string} topic - Kafka topic name to subscribe to
   * @param {Function} messageHandler - Function to handle incoming messages
   * @param {KafkaMessage} messageHandler.message - The Kafka message received
   * @param {string} messageHandler.topic - Topic name of the message
   * @param {number} messageHandler.partition - Partition number of the message
   * @param {string} messageHandler.offset - Message offset for tracking
   * @returns {Promise<Consumer>} Promise resolving to configured Kafka consumer instance
   * @throws {Error} Throws error if consumer setup fails
   * @memberof KafkaService
   *
   * @description
   * Consumer configuration:
   * - Consumer group management for load balancing
   * - Automatic offset commits for message tracking
   * - Error handling and retry mechanisms
   * - Message acknowledgment and processing confirmation
   * - Connection health monitoring and recovery
   *
   * Message processing features:
   * - Automatic deserialization of JSON messages
   * - Error isolation and handling per message
   * - Dead letter queue for failed messages
   * - Monitoring and logging of processing metrics
   *
   * // Consumer handles:
   * // - Message consumption from topic
   * // - Error handling and retries
   * // - Offset management
   * // - Processing confirmation
   * ```
   */
  private async setupConsumer(
    topic: string,
    messageHandler: (
      message: KafkaMessage,
      // eslint-disable-next-line no-shadow
      topic: string,
      partition: number,
      offset: string,
    ) => void,
  ): Promise<Consumer> {
    try {
      const consumer = await kafkaHelperService.createConsumer('cron-user-group', topic, messageHandler);
      logger.info(`User consumer set up and listening on topic: ${topic}`);

      return consumer;
    } catch (error) {
      logger.error(error, `Error setting up consumer for topic ${topic}:`);
      throw error;
    }
  }

  /**
   * Handles incoming Kafka messages from cron and administrative topics.
   * Routes messages to appropriate business services based on message type and content.
   * Provides comprehensive message processing with error handling and service integration.
   *
   * @private
   * @async
   * @method handleCronMessage
   * @param {KafkaMessage} message - Kafka message containing event data and metadata
   * @param {string} topic - Topic name where the message originated
   * @param {number} partition - Partition number for message ordering
   * @memberof KafkaService
   *
   * @description
   * Message processing workflow:
   * 1. Message deserialization and validation
   * 2. Message type identification and routing
   * 3. Business service method invocation
   * 4. Error handling and logging
   * 5. Processing confirmation and metrics
   *
   * Supported message types:
   * - offering: Asset offering updates and status changes
   * - order: Trade order processing and status updates
   * - whitelist: Investor whitelist management operations
   * - user: User account and profile updates
   * - transfer: Token transfer operations and approvals
   * - dividend: Dividend distribution and payment processing
   * - ForceTransferred: Forced token transfer operations
   * - DividendDistributed: Dividend payment confirmations
   * - SaveTransfer: Transfer record persistence
   * - issuerAction: Issuer-initiated operations
   *
   * Error handling:
   * - Graceful error recovery for transient failures
   * - Dead letter queue for permanently failed messages
   * - Comprehensive error logging and monitoring
   * - Service-specific error handling and retries
   *
   * // Processing flow:
   * // 1. Extract message type: "offering"
   * // 2. Route to: OfferingService.updateOfferingDetails
   * // 3. Update database with new status
   * // 4. Send confirmation response
   * ```
   */
  private async handleCronMessage(message: KafkaMessage, topic: string, partition: number) {
    try {
      let response: any;
      const body = JSON.parse(message.value.toString());
      const value = typeof body.value === 'string' ? JSON.parse(body?.value) : body?.value;
      const filter = { _id: value?._id };
      delete value?._id;
      console.log('\n\n\n\n', value?.type, '======', value?.event, 'data =====>>>>\n\n\n', value);
      switch (value?.type) {
        case 'offering':
          response = await OfferingService.updateOfferingDetails(value, filter);
          break;
        case 'ForceTransferred':
          response = await TransferRequestService.updateForceTransferDetails(value, filter);
          break;
        case 'whitelist':
          response = await OfferingService.updateWhitelistDetails(value, filter);
          break;
        case 'DividendDistributed':
          response = await DividendsService.createDividendHistory(value);
          break;
        case 'SaveTransfer':
          response = await TransferRequestService.updateForceTransferDetails({ ...value, ...filter }, filter);
          break;
        case 'order':
          response = await OrderService.updateOrderDetails(value, filter);
          break;
        case 'issuerAction':
          response = await OrderService.processOrder(value, filter);
          break;
        case 'user':
          response = await UserService.updateUserDetails(value, filter);
          break;
        case 'countryAllow':
          response = await OfferingService.countryAllow(value);
          break;
        case 'countryRestrict':
          response = await OfferingService.countryAllow(value);
          break;
        case 'offeringNav': {
          const offeringId = filter._id ? new Types.ObjectId(filter._id) : null;
          response = await OfferingService.tokenValuation(offeringId, value);
          break;
        }
        default:
          logger.warn('Unrecognized or missing type');

          return;
      }
      if (!response.error && this.consumer) {
        await this.consumer.commitOffsets([{ topic, partition, offset: (parseInt(message.offset, 10) + 1).toString() }]);
      } else {
        logger.warn('Consumer is not initialized or operation failed.', value?.type);
      }
    } catch (error) {
      logger.error(error, 'handleUserMessage');
      throw error;
    }
  }

  /**
   * Sends a message to the 'user-to-admin' Kafka topic.
   * @param message - The message to be sent.
   */
  public async sendMessageToAdmin(message: any) {
    try {
      await kafkaHelperService.sendMessage('user-to-admin', [{ value: JSON.stringify(message) }]);
    } catch (error) {
      logger.error(error, 'Error sending message from user to admin:');
      throw error;
    }
  }

  public async sendMessageToNotification(message: any) {
    try {
      await kafkaHelperService.sendMessage('notifications', [{ value: JSON.stringify(message) }]);
    } catch (error) {
      logger.error(error, 'Error sending message from user to admin:');
      throw error;
    }
  }
}

export default new KafkaService();
