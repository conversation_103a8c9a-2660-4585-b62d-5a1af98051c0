import express from 'express';
import { ResponsePayLoad, PromiseResolve } from '../utils/common.interface';
import { capitalizeString } from './messageHelper';

/**
 * Utility class for standardizing HTTP response handling across the application.
 * Provides consistent response formatting, status code management, and message capitalization.
 * Ensures uniform API response structure for both success and error scenarios.
 *
 * @class ResponseHandler
 * @description Static utility class for HTTP response standardization and formatting
 *
 * // Error response
 * ResponseHandler.error(res, {
 *   message: 'User not found',
 *   status: 404,
 *   error: true
 * });
 * ```
 */
export class ResponseHandler {
  /**
   * Sends a standardized success response with consistent formatting.
   * Handles message capitalization, status code defaults, and response structure.
   *
   * @static
   * @method success
   * @template T - Type of the response data payload
   * @param {express.Response} response - Express response object to send the response through
   * @param {ResponsePayLoad<T>} responseData - Response data containing message, status, data, and error flag
   * @param {string} responseData.message - Success message to display to client
   * @param {number} [responseData.status] - HTTP status code (defaults to 200 if not provided)
   * @param {T} [responseData.data] - Optional data payload to include in response
   * @param {boolean} responseData.error - Error flag indicating success (should be false)
   * @returns {PromiseResolve} Standardized response object with message, status, data, and error properties
   * @memberof ResponseHandler
   *
   * @description
   * Success response features:
   * - Automatic message capitalization for consistency
   * - Default status code of 200 if not specified
   * - Consistent JSON response structure
   * - Data payload inclusion when provided
   * - Error flag set to false for success responses
   *
   * // Success response with default status
   * ResponseHandler.success(res, {
   *   message: 'data retrieved',
   *   data: users,
   *   error: false
   * });
   * ```
   */
  static success<T>(response: express.Response, responseData: ResponsePayLoad<T>): PromiseResolve {
    let { message } = responseData;
    const { status, data = null, error } = responseData;
    message = capitalizeString(message);
    response.status(status || 200).json({ message, status, data, error });

    return { message, status: status || 200, data, error };
  }

  /**
   * Sends a standardized error response with consistent formatting.
   * Handles error message capitalization, status code defaults, and error structure.
   *
   * @static
   * @method error
   * @template T - Type of the response data payload (typically null for errors)
   * @param {express.Response} response - Express response object to send the error response through
   * @param {ResponsePayLoad<T>} responseData - Error response data containing message, status, data, and error flag
   * @param {string} responseData.message - Error message to display to client
   * @param {number} [responseData.status] - HTTP error status code (defaults to 500 if not provided)
   * @param {T} [responseData.data] - Optional error data payload (typically null)
   * @param {boolean} responseData.error - Error flag indicating failure (should be true)
   * @returns {PromiseResolve} Standardized error response object with message, status, data, and error properties
   * @memberof ResponseHandler
   *
   * @description
   * Error response features:
   * - Automatic error message capitalization for consistency
   * - Default status code of 500 if not specified
   * - Consistent JSON error response structure
   * - Optional error data inclusion for debugging
   * - Error flag set to true for error responses
   *
   * // Error response with default status
   * ResponseHandler.error(res, {
   *   message: 'internal server error occurred',
   *   error: true
   * });
   *
   * // Error response with debug data
   * ResponseHandler.error(res, {
   *   message: 'validation failed',
   *   status: 400,
   *   data: validationErrors,
   *   error: true
   * });
   * ```
   */
  static error<T>(response: express.Response, responseData: ResponsePayLoad<T>): PromiseResolve {
    let { message } = responseData;
    const { status, data = null, error } = responseData;
    message = capitalizeString(message);
    response.status(status || 500).json({ message, status, data, error });

    return { message, status: status || 500, data, error };
  }
}
