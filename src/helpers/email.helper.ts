import * as sgMail from '@sendgrid/mail';
import * as ejs from 'ejs';
import * as path from 'path';
import config from '../config/env';
import { RES_MSG } from '../utils/responseUtils';
import logger from './logging/logger.helper';

/**
 * Email service class providing comprehensive email delivery functionality using SendGrid.
 * Handles template-based emails, direct email sending, and various notification types.
 * Supports HTML templating, authentication emails, transaction notifications, and system alerts.
 *
 * @class EmailHelper
 * @description Complete email management service with SendGrid integration and template support
 *
 * // Send direct email
 * await emailHelper.sendMail(
 *
 * // Send template-based email
 * await emailHelper.sendEmailTemplate(
 */
class EmailHelper {
  /**
   * SendGrid mail service instance for email delivery.
   * Configured with API key and sender information from environment variables.
   *
   * @private
   * @type {any}
   * @memberof EmailHelper
   */
  private sgMail: any;

  /**
   * Initializes the EmailHelper with SendGrid configuration.
   * Sets up the SendGrid service with API key from application configuration.
   *
   * @constructor
   * @description
   * Initialization includes:
   * - SendGrid API key configuration
   * - Service instance setup
   * - Error handling configuration
   */
  constructor() {
    // Set the SendGrid API key
    this.sgMail = sgMail.setApiKey(config.SENDGRID.API_KEY);
  }

  /**
   * Sends an email using SendGrid with custom content and formatting.
   * Provides direct email sending capability with both plain text and HTML content.
   * Includes comprehensive error handling and delivery confirmation.
   *
   * @async
   * @method sendMail
   * @param {string} to - Recipient email address for message delivery
   * @param {string} subject - Email subject line displayed to recipient
   * @param {string} text - Plain text content for email clients that don't support HTML
   * @param {string} html - HTML content for rich email formatting and styling
   * @returns {Promise<boolean>} Promise resolving to true if email sent successfully, false if failed
   * @memberof EmailHelper
   *
   * @description
   * Email sending features:
   * - SendGrid integration for reliable delivery
   * - Custom sender name and email configuration
   * - Both plain text and HTML content support
   * - Comprehensive error logging and handling
   * - Delivery status tracking and reporting
   *
   * Error handling:
   * - Logs detailed error information for debugging
   * - Returns boolean status for easy integration
   * - Graceful failure handling without throwing exceptions
   *
   * if (success) {
   *   console.log('Email sent successfully');
   * } else {
   *   console.log('Email delivery failed');
   * }
   * ```
   */
  public async sendMail(to: string, subject: string, text: string, html: string): Promise<boolean> {
    try {
      // Create the email message object
      const msg = {
        to,
        from: {
          email: config.SENDGRID.SENDER,
          name: 'Valuit', // Set the display name here
        },
        subject,
        text,
        html,
      };

      // Send the email using SendGrid
      return this.sgMail
        .send(msg)
        .then(() => {
          return true;
        })
        .catch((error: any) => {
          logger.error(error?.message, 'error in SendGrid');

          return false;
        });
    } catch (error) {
      logger.error(error, 'Error of sendMail');

      return false;
    }
  }

  /**
   * Sends template-based emails using predefined EJS templates with dynamic data injection.
   * Supports various email types including authentication, notifications, and transaction updates.
   * Automatically handles template selection, subject line assignment, and content rendering.
   *
   * @async
   * @method sendEmailTemplate
   * @param {string} to - Recipient email address for template-based email
   * @param {string} templateName - Name of the email template to use (without .ejs extension)
   * @param {any} details - Dynamic data object to inject into the email template
   * @returns {Promise<boolean>} Promise resolving to true if template email sent successfully, false if failed
   * @memberof EmailHelper
   *
   * @description
   * Supported template types:
   * - Authentication: reset-password, verify-otp, login notifications
   * - KYC/Compliance: kycreceived, kybreceived status updates
   * - Transaction: orderMint, orderCreate, orderreject notifications
   * - Transfer: forceTransfer, fromforceTransfer, transferapproval, transferrejected
   * - Whitelist: walletWhiteListRequest, walletWhitelist, whiterequested
   * - Dividend: dividendAnnouncement, dividendDistibuted notifications
   * - Offering: offeringDeployed, offeringDeployedRejected status
   * - Redeem: redemRequested, redemApproved process updates
   * - Token: userTokens, userTokensFrozen, userTokensUnFrozen status
   * - Governance: VotingProposalSubmitted, VoteSuccessfully notifications
   * - User Management: signup, multipleLogin, accountBlocked alerts
   *
   * Template processing:
   * - EJS template engine for dynamic content rendering
   * - Automatic subject line and text content assignment
   * - Template file path resolution and loading
   * - Error handling for missing templates or rendering issues
   *
   * // Send dividend announcement
   * await emailHelper.sendEmailTemplate(
   *
   * // Send order confirmation
   * await emailHelper.sendEmailTemplate(
   */
  public async sendEmailTemplate(to: string, templateName: string, details: any): Promise<boolean> {
    try {
      let templatePath;
      let subject;
      let text;
      if (templateName === 'reset-password') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.FORGOT_PASSWORD_SUBJECT;
        text = RES_MSG.EMAIL.FORGOT_PASSWORD_TEXT;
      } else if (templateName === 'verify-otp') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = 'Your Email OTP for Valuit';
        text = RES_MSG.EMAIL.OTP_TEXT;
      } else if (templateName === 'kycreceived') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.KYC_RECEIVED;
        text = RES_MSG.EMAIL.KYC_RECEIVED;
      } else if (templateName === 'fromforceTransfer') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.FORCE_TRANSFER;
        text = RES_MSG.EMAIL.FORCE_TRANSFER;
      } else if (templateName === 'orderMint') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.ORDER_MINTED;
        text = RES_MSG.EMAIL.ORDER_MINTED;
      } else if (templateName === 'walletWhiteListRequest') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.WHITELIST_REQUEST;
        text = RES_MSG.EMAIL.WHITELIST_REQUEST;
      } else if (templateName === 'VoteSuccessfully') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.VOTE_SUCESSFULLY;
        text = RES_MSG.EMAIL.VOTE_SUCESSFULLY;
      } else if (templateName === 'VotingProposalSubmitted') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.PROPOSAL_SUBMITTED;
        text = RES_MSG.EMAIL.PROPOSAL_SUBMITTED;
      } else if (templateName === 'redemRequested') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.REDEEMPTION_REQUESTED;
        text = RES_MSG.EMAIL.REDEEMPTION_REQUESTED;
      } else if (templateName === 'orderreject') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.ORDER_REJECTED;
        text = RES_MSG.EMAIL.ORDER_REJECTED;
      } else if (templateName === 'redemApproved') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.REDEMPTION_APPROVED;
        text = RES_MSG.EMAIL.REDEMPTION_APPROVED;
      } else if (templateName === 'dividendDistibuted') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.DIVIDENT_SUCESS;
        text = RES_MSG.EMAIL.DIVIDENT_SUCESS;
      } else if (templateName === 'dividendAnnouncement') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.DIVIDENT_ANNOUNCEMENT;
        text = RES_MSG.EMAIL.DIVIDENT_ANNOUNCEMENT;
      } else if (templateName === 'divident') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.DIVIDENT_SUCESS;
        text = RES_MSG.EMAIL.DIVIDENT_SUCESS;
      } else if (templateName === 'offeringDeployed') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.OFFERING_DEPLOYED;
        text = RES_MSG.EMAIL.OFFERING_DEPLOYED;
      } else if (templateName === 'offeringDeployedRejected') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.OFFERING_DEPLOYED_REJECTED;
        text = RES_MSG.EMAIL.OFFERING_DEPLOYED_REJECTED;
      } else if (templateName === 'whiterequested') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.WHITELIST_REJECT;
        text = RES_MSG.EMAIL.WHITELIST_REJECT;
      } else if (templateName === 'walletWhitelist') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.WHITELIST_APPROVE;
        text = RES_MSG.EMAIL.WHITELIST_APPROVE;
      } else if (templateName === 'assignToAdmin') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.OFFERING_REQUEST_ADMIN;
        text = RES_MSG.EMAIL.OFFERING_REQUEST_ADMIN;
      } else if (templateName === 'userTokensFrozen') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.USERTOKENSFROZEN;
        text = RES_MSG.EMAIL.USERTOKENSFROZEN;
      } else if (templateName === 'userTokensUnFrozen') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.USERTOKENSUNFROZEN;
        text = RES_MSG.EMAIL.USERTOKENSUNFROZEN;
      } else if (templateName === 'userTokens') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.WHITELIST_REJECT;
        text = RES_MSG.EMAIL.WHITELIST_REJECT;
      } else if (templateName === 'transferrejected') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.TRANSFER_RJECTED;
        text = RES_MSG.EMAIL.TRANSFER_RJECTED;
      } else if (templateName === 'transferapproval') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.TRANSFER_APPROVED;
        text = RES_MSG.EMAIL.TRANSFER_APPROVED;
      } else if (templateName === 'forceTransfer') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.FORCE_TRANSFER;
        text = RES_MSG.EMAIL.FORCE_TRANSFER;
      } else if (templateName === 'orderCreate') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.ORDER_CREATE;
        text = RES_MSG.EMAIL.ORDER_CREATE;
      } else if (templateName === 'kybreceived') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.KYB_RECEIVED;
        text = RES_MSG.EMAIL.KYB_RECEIVED;
      } else if (templateName === 'accountBlocked') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.USER_BLOCKED;
        text = RES_MSG.EMAIL.USER_BLOCKED;
      } else if (templateName === 'signup') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.SIGN_UP_SUBJECT;
        text = RES_MSG.EMAIL.OTP_TEXT;
      } else if (templateName === 'ConvertedToERC-20') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.SIGN_IN_SUBJECT;
        text = RES_MSG.EMAIL.SIGN_IN_SUBJECT;
      } else if (templateName === 'ConvertedToERC-3643') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.SIGN_IN_SUBJECT;
        text = RES_MSG.EMAIL.SIGN_IN_SUBJECT;
      } else if (templateName === 'login') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.SIGN_IN_SUBJECT;
        text = RES_MSG.EMAIL.SIGN_IN_SUBJECT;
      } else if (templateName === 'multipleLogin') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.MULTIPLE_LOGIN;
        text = RES_MSG.EMAIL.OTP_TEXT;
      } else if (templateName === 'invitingRepresentative') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.INVITE_REPRESENTATIVE;
        text = RES_MSG.EMAIL.INVITE_REPRESENTATIVE;
      } else {
        logger.error(templateName, 'Error: no matching template found');

        return false;
      }
      const htmlDetails = await ejs.renderFile(templatePath, { detail: { ...details, baseUrl: process.env.baseUrl } }, { async: true }); // render the template
      return await this.sendMail(
        to,
        subject,
        text,
        // htmldata
        htmlDetails,
      );
    } catch (error: any) {
      logger.error(error, 'Error in sendEmailTemplate');

      return false;
    }
  }
}

// Export an instance of the EmailHelper class
export default new EmailHelper();
