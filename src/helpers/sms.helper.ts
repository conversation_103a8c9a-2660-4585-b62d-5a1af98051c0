import * as twilio from 'twilio';
import CONF<PERSON> from '../config/env';
import logger from './logging/logger.helper';
import { capitalizeString } from './messageHelper';

/**
 * SMSHelper class to handle sending SMS messages using Twilio.
 */
class SMSHelper {
  private client: twilio.Twilio;

  /**
   * Constructor to initialize the SMSHelper.
   * Sets up <PERSON><PERSON><PERSON> with the account SID and auth token from the configuration.
   */
  constructor() {
    // Initialize Twilio client with account SID and auth token
    this.client = twilio(CONFIG.TWILIO.ACCOUNT_SID, CONFIG.TWILIO.AUTH_TOKEN);
  }

  /**
   * Sends an SMS message using Twilio.
   *
   * @param to - Recipient mobile number (in E.164 format, e.g., +**********)
   * @param body - Message body to send
   * @returns A promise that resolves to true if the message is sent or false if an error occurs
   */
  public async sendSMS(to: string, body: string): Promise<boolean> {
    try {
      // Create the SMS message object
      const message = await this.client.messages.create({
        body,
        from: CONFIG.TWILIO.PHONE_NUMBER, // Twilio phone number
        to, // Recipient phone number
      });

      logger.info(`SMS sent successfully to ${to}. Message SID: ${message.sid}`);

      return true;
    } catch (error: any) {
      logger.error(error, 'Error sending SMS');

      return false;
    }
  }

  /**
   * Sends an SMS message with a predefined template.
   *
   * @param to - Recipient mobile number (in E.164 format)
   * @param templateName - Template name to use for the message
   * @param details - Details to inject into the template
   * @returns A promise that resolves to true if the message is sent or false if an error occurs
   */
  public async sendSMSTemplates(to: string, templateName: string, details: any): Promise<boolean> {
    try {
      let messageBody;

      if (templateName === 'reset-password') {
        messageBody = `Dear ${details.name},\n\nYour OTP for verification is ${details.otp}.\nIt is valid for the next ${details.expireTime} minutes.\nPlease do not share this code.\nIf you didn't request this, contact support.\nThank you for ensuring the security of your account.\n\nBest Regards,\nValuit Team`;
      } else if (templateName === 'verify-otp') {
        messageBody = `Dear ${details.name},\n\nYour OTP for verification is ${details.otp}.\nIt is valid for the next ${details.expireTime} minutes.\nPlease do not share this code.\nIf you didn't request this, contact support.\nThank you for ensuring the security of your account.\n\nBest Regards,\nValuit Team`;
      } else {
        logger.warn(`No matching template found for: ${templateName}`);

        return false;
      }
      const message = await capitalizeString(messageBody);

      // Send the SMS using the Twilio client
      return await this.sendSMS(to, message);
    } catch (error: any) {
      logger.error(error, 'Error sending SMS template');

      return false;
    }
  }
}

export default new SMSHelper();
