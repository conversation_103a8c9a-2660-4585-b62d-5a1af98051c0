import { createLogger, format, transports } from 'winston';
import * as fs from 'fs';
import * as path from 'path';
import 'winston-daily-rotate-file';

import CONFIG from '../../config/env/index';

/**
 * Log directory path for storing application logs.
 * Automatically created if it doesn't exist for proper log management.
 *
 * @constant {string} logDir - Absolute path to the logs directory
 */
const logDir = path.resolve(__dirname, '../../../logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

/**
 * Daily rotating file transport for application logs.
 * Automatically rotates log files daily with compression and retention policies.
 *
 * @constant {transports.DailyRotateFile} dailyRotateFileTransport
 * @description Configured with:
 * - Daily rotation pattern (YYYY-MM-DD)
 * - Gzip compression for old files
 * - Maximum file size of 20MB
 * - 14-day retention period
 * - Environment-specific log level
 */
const dailyRotateFileTransport = new transports.DailyRotateFile({ filename: `${logDir}/%DATE%-app.log`, datePattern: 'YYYY-MM-DD', zippedArchive: true, maxSize: '20m', maxFiles: '14d', level: CONFIG.PROJECT.LOG_LEVEL });

/**
 * File path for unhandled exception logs.
 * Captures critical application crashes and unhandled exceptions.
 *
 * @constant {string} exceptionsFilePath - Path to exceptions log file
 */
const exceptionsFilePath = path.join(logDir, 'exceptions.log');

/**
 * File path for unhandled promise rejection logs.
 * Captures unhandled promise rejections for debugging async issues.
 *
 * @constant {string} rejectionsFilePath - Path to rejections log file
 */
const rejectionsFilePath = path.join(logDir, 'rejections.log');

/**
 * Production environment flag for conditional logging configuration.
 * Determines whether to enable console logging and debug features.
 *
 * @constant {boolean} isProduction - True if running in production environment
 */
const isProduction = CONFIG.ENVIRONMENT === 'production';

/**
 * Winston logger instance with comprehensive logging configuration.
 * Provides structured logging with multiple transports, error handling, and environment-specific settings.
 * Supports file rotation, exception handling, and conditional console output.
 *
 * @constant {Logger} logger - Configured Winston logger instance
 *
 * @description
 * Logger features:
 * - Structured JSON logging format
 * - Timestamp and error stack trace inclusion
 * - Multiple transport targets (file, console, daily rotation)
 * - Automatic exception and rejection handling
 * - Environment-specific configuration
 * - Service and environment metadata inclusion
 *
 * Log levels supported:
 * - error: Error conditions and exceptions
 * - warn: Warning conditions that need attention
 * - info: General information about application flow
 * - http: HTTP request/response logging
 * - verbose: Detailed information for debugging
 * - debug: Debug information for development
 * - silly: Very detailed trace information
 *
 * Transport configuration:
 * - Daily rotating files with compression and retention
 * - Static application log file
 * - Console output for non-production environments
 * - Separate files for exceptions and rejections
 *
 * // Log different levels
 * logger.error('Database connection failed', { error: dbError });
 * logger.warn('High memory usage detected', { usage: '90%' });
 * logger.info('User authenticated successfully', { userId: '123' });
 * logger.debug('Processing order details', { orderId: '456' });
 *
 * // Log with metadata
 * logger.info('API request processed', {
 *   method: 'POST',
 *   url: '/api/orders',
 *   responseTime: '150ms',
 *   statusCode: 201
 * });
 *
 * // Error logging with stack traces
 * try {
 *   // Some operation
 * } catch (error) {
 *   logger.error('Operation failed', error);
 * }
 * ```
 */
const logger = createLogger({
  level: CONFIG.PROJECT.LOG_LEVEL,
  format: format.combine(
    format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    format.errors({ stack: true }), // Ensures stack traces are included
    // format.metadata({ fillExcept: ['message', 'level', 'timestamp', 'stack'] }),
    format.json(),
  ),
  defaultMeta: { service: CONFIG.PROJECT.NAME, environment: CONFIG.ENVIRONMENT || 'development' },
  transports: [dailyRotateFileTransport, new transports.File({ filename: path.join(logDir, 'app.log'), handleExceptions: true, level: CONFIG.PROJECT.LOG_LEVEL })],
  exceptionHandlers: [new transports.File({ filename: exceptionsFilePath })],
  rejectionHandlers: [new transports.File({ filename: rejectionsFilePath })],
});

/**
 * Add console transport for non-production environments.
 * Provides colored, formatted console output for development and testing.
 * Includes timestamp, colorized levels, and stack trace formatting.
 */
// Add conditional logging for non-production environments
if (!isProduction) {
  logger.add(
    new transports.Console({
      format: format.combine(
        format.colorize(),
        format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
        format.printf(({ timestamp, level, message, stack }) => {
          let logOutput = `[${timestamp}] ${level}: ${message}`;
          if (stack) {
            logOutput += `\n${stack}`;
          }

          return logOutput;
        }),
      ),
      handleExceptions: true,
      level: 'debug',
    }),
  );
}

export default logger;
