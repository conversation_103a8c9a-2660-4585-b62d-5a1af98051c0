import * as jwt from 'jsonwebtoken';
import { Server, Socket } from 'socket.io';
import { DecodedToken } from '../utils/common.interface';
import CONFIG from '../config/env/index';
import logger from './logging/logger.helper';
import { RES_MSG, RESPONSES } from '../utils/responseUtils';
import CustomError from './customError.helper';

/**
 * Socket.IO helper class for real-time communication and live updates.
 * Manages WebSocket connections, authentication, room management, and message broadcasting.
 * Provides secure, real-time communication between the server and connected clients.
 * Supports user-specific messaging, room-based communication, and event broadcasting.
 *
 * @class SocketHelper
 * @description Comprehensive real-time communication service with authentication and room management
 *
 * // Initialize socket server
 * const io = new Server(httpServer);
 * initializeSocket(io);
 *
 * // Send messages to specific users
 * socketHelper.sendMessageToUser('userId123', 'order-update', orderData);
 *
 * // Broadcast to all connected clients
 * socketHelper.broadcast('system-announcement', announcementData);
 * ```
 */
class SocketHelper {
  /**
   * Socket.IO server instance for managing WebSocket connections.
   * Handles all real-time communication, connection management, and event routing.
   *
   * @private
   * @type {Server}
   * @memberof SocketHelper
   */
  private io: Server;

  /**
   * Initializes the Socket.IO helper with server instance and event handlers.
   * Sets up connection handling, authentication, and event routing for real-time communication.
   *
   * @constructor
   * @param {Server} io - Socket.IO server instance for WebSocket communication
   *
   * @description
   * Initialization includes:
   * - Socket.IO server instance assignment
   * - Connection event handler setup
   * - Authentication and authorization configuration
   * - Event routing and message handling
   * - Room management and user tracking
   */
  constructor(io: Server) {
    this.io = io;
    this.initialize();
  }

  /**
   * Initializes Socket.IO connection handling and event routing.
   * Sets up authentication, message handling, room management, and disconnect handling.
   * Establishes the foundation for real-time communication features.
   *
   * @private
   * @method initialize
   * @memberof SocketHelper
   *
   * @description
   * Connection setup includes:
   * - User authentication validation
   * - Event handler registration for various message types
   * - Room join/leave functionality
   * - Disconnect handling and cleanup
   * - Message routing and broadcasting setup
   *
   * Supported events:
   * - 'connection': New client connection establishment
   * - 'kyc': KYC status updates and notifications
   * - 'joinRoom': User joining specific communication rooms
   * - 'leaveRoom': User leaving communication rooms
   * - 'disconnect': Client disconnection cleanup
   */
  private initialize() {
    this.io.on('connection', (socket: Socket) => {
      this.authenticateUser(socket);

      // socket.on("message", (data: MessagePayload) => this.handleMessage(socket, data));
      socket.on('kyc', (data: any) => this.handleMessage(socket, data));
      socket.on('joinRoom', (room: string) => this.joinRoom(socket, room));
      socket.on('leaveRoom', (room: string) => this.leaveRoom(socket, room));
      socket.on('disconnect', () => this.handleDisconnect(socket));
    });
  }

  /**
   * Authenticates WebSocket connections using JWT tokens for secure communication.
   * Validates user identity and establishes authenticated session for real-time updates.
   * Disconnects clients with invalid or missing authentication credentials.
   *
   * @private
   * @method authenticateUser
   * @param {Socket} socket - Socket.IO connection instance to authenticate
   * @memberof SocketHelper
   *
   * @description
   * Authentication process:
   * 1. Extracts JWT token from connection handshake (auth or headers)
   * 2. Validates token signature and expiration using configured secret
   * 3. Decodes user information from valid token payload
   * 4. Stores user ID in socket data for session management
   * 5. Logs successful authentication or disconnects invalid clients
   *
   * Security features:
   * - JWT token validation with application secret
   * - Automatic disconnection for invalid tokens
   * - User ID extraction and session association
   * - Comprehensive error handling and logging
   * - Support for both auth and header token sources
   *
   * // Successful authentication logs:
   * // "✅ [SOCKET] User authenticated: 507f1f77bcf86cd799439011"
   *
   * // Failed authentication:
   * // "❌ [SOCKET] Authentication failed: Authentication token missing"
   * // Client disconnected automatically
   * ```
   */
  private authenticateUser(socket: Socket) {
    try {
      const token = (socket.handshake.auth.token as string) || (socket.handshake.headers.auth as string);
      if (!token) throw new Error('Authentication token missing');

      // this TOKEN is JWT_SECRET token to validate the authentication  of token
      if (socket.handshake.headers.auth) {
        console.log('token from postman->> ', token);
      }
      const { TOKEN } = CONFIG.JWT_AUTH;
      const decodedData = jwt.verify(token, TOKEN as string) as DecodedToken;
      if (!decodedData) {
        throw new CustomError(RES_MSG.COMMON.UNAUTHORIZED_ACCESS, RESPONSES.UN_AUTHORIZED);
      }
      socket.data.userId = decodedData.userId;

      console.log(`✅ [SOCKET] User authenticated: ${decodedData.userId}`);
    } catch (error) {
      console.error('❌ [SOCKET] Authentication failed:', error.message);
      socket.disconnect();
    }
  }

  /**
   * Emits events to specific Socket.IO connections with error handling.
   * Provides reliable message delivery to individual client connections.
   * Includes comprehensive error handling and logging for debugging.
   *
   * @public
   * @method emitToSocket
   * @template T - Type of the data payload being sent
   * @param {Socket} socket - Target Socket.IO connection for message delivery
   * @param {string} event - Event name for client-side event handling
   * @param {T} data - Data payload to send to the client
   * @memberof SocketHelper
   *
   * @description
   * Message emission features:
   * - Type-safe data payload support
   * - Comprehensive error handling and logging
   * - Event-based communication for client-side routing
   * - Debugging output for development and troubleshooting
   *
   * // Send KYC status change
   * const kycData = { status: 'APPROVED', message: 'KYC approved' };
   * socketHelper.emitToSocket(socket, 'kyc-update', kycData);
   *
   * // Send notification
   * const notification = { title: 'New Message', body: 'You have a new message' };
   * socketHelper.emitToSocket(socket, 'notification', notification);
   * ```
   */
  public emitToSocket<T>(socket: Socket, event: string, data: T) {
    try {
      console.log('\nhere before emit', event);
      socket.emit(event, data);
      console.log('emmited to => ', event);
    } catch (error) {
      console.error(`❌ [SOCKET] Error emitting event "${event}":`, error);
    }
  }

  /**
   * Broadcasts events to all connected Socket.IO clients simultaneously.
   * Provides platform-wide messaging for system announcements and global updates.
   * Includes error handling and logging for reliable message delivery.
   *
   * @public
   * @method broadcast
   * @template T - Type of the data payload being broadcast
   * @param {string} event - Event name for client-side event handling
   * @param {T} data - Data payload to broadcast to all connected clients
   * @memberof SocketHelper
   *
   * @description
   * Broadcasting features:
   * - Simultaneous message delivery to all connected clients
   * - Type-safe data payload support
   * - Error handling for failed broadcasts
   * - Suitable for system-wide announcements and updates
   *
   * // Market data update
   * const marketData = {
   *   symbol: 'VALUIT',
   *   price: '125.50',
   *   change: '+2.5%'
   * };
   * socketHelper.broadcast('market-update', marketData);
   * ```
   */
  public broadcast<T>(event: string, data: T) {
    try {
      this.io.emit(event, data);
    } catch (error) {
      console.error(`❌ [SOCKET] Error broadcasting event "${event}":`, error);
    }
  }

  /**
   * Sends messages to specific users without authentication validation.
   * Provides message delivery to individual users with fallback broadcasting.
   * Used for scenarios where user connection status is uncertain.
   *
   * @public
   * @method sendMessageToUserWithoutAuth
   * @param {string} userId - Target user ID for message delivery
   * @param {string} event - Event name for client-side event handling
   * @param {any} data - Data payload to send to the user
   * @memberof SocketHelper
   *
   * @description
   * Message delivery features:
   * - User-specific message targeting by user ID
   * - Fallback to broadcast if user not connected
   * - No authentication validation for flexible usage
   * - Comprehensive logging for connection status
   *
   * // If user not connected, message is broadcast to all clients
   * // Logs: "⚠️ [SOCKET] User userId123 not connected, broadcasting message"
   * ```
   */
  public sendMessageToUserWithoutAuth(userId: string, event: string, data: any) {
    const socket = this.findSocketByUserId(userId);
    console.log('\nsocker ID::: ===>>> ', socket);
    if (socket) {
      this.emitToSocket(socket, event, data);
    } else {
      console.warn(`⚠️ [SOCKET] User ${userId} not connected, broadcasting message`);
      this.broadcast(event, data); // Send message to all connected users if a specific user isn't found
    }
  }

  /**
   * Sends messages to specific users with both targeted and broadcast delivery.
   * Combines broadcasting with user-specific messaging for maximum reach.
   * Ensures message delivery even if user connection status is uncertain.
   *
   * @public
   * @method sendMessageToUser
   * @param {string} userId - Target user ID for specific message delivery
   * @param {string} event - Event name for client-side event handling
   * @param {any} data - Data payload to send to the user
   * @memberof SocketHelper
   *
   * @description
   * Delivery strategy:
   * - Always broadcasts message to all connected clients
   * - Additionally sends to specific user if connected
   * - Provides redundant delivery for critical messages
   * - Logs connection status for monitoring
   *
   * // Message is both broadcast and sent to specific user
   * // Ensures delivery regardless of connection status
   * ```
   */
  public sendMessageToUser(userId: string, event: string, data: any) {
    this.broadcast(event, data);
    const socket = this.findSocketByUserId(userId);
    if (socket) {
      console.log('here in send message to user');
      this.emitToSocket(socket, event, data);
    } else {
      console.warn(`⚠️ [SOCKET] User ${userId} not connected`);
    }
  }

  /**
   * Sends messages exclusively to specific users without broadcasting.
   * Provides private message delivery for user-specific notifications.
   * Does not fall back to broadcasting if user is not connected.
   *
   * @public
   * @method sendMessageToSingleUser
   * @param {string} userId - Target user ID for exclusive message delivery
   * @param {string} event - Event name for client-side event handling
   * @param {any} data - Data payload to send exclusively to the target user
   * @memberof SocketHelper
   *
   * @description
   * Private messaging features:
   * - Exclusive delivery to specific user only
   * - No broadcasting fallback for privacy
   * - Suitable for sensitive or personal notifications
   * - Connection status logging for monitoring
   *
   * // Personal account notification
   * const accountAlert = {
   *   type: 'security',
   *   message: 'New login detected from unknown device',
   *   action: 'verify-login'
   * };
   * socketHelper.sendMessageToSingleUser('userId123', 'security-alert', accountAlert);
   * ```
   */
  public sendMessageToSingleUser(userId: string, event: string, data: any) {
    const socket = this.findSocketByUserId(userId);
    if (socket) {
      console.log('here in send message to user');
      this.emitToSocket(socket, event, data);
    } else {
      console.warn(`⚠️ [SOCKET] User ${userId} not connected`);
    }
  }

  /**
   * Handles incoming messages from clients with broadcasting and logging.
   * Processes client messages and broadcasts them to other connected clients.
   * Provides message relay functionality for peer-to-peer communication.
   *
   * @private
   * @method handleMessage
   * @param {Socket} socket - Source socket connection for the message
   * @param {any} data - Message data received from the client
   * @memberof SocketHelper
   *
   * @description
   * Message handling process:
   * - Logs incoming message details for debugging
   * - Broadcasts message to all connected clients
   * - Enables peer-to-peer communication through server relay
   * - Maintains message flow visibility for monitoring
   *
   * // Server logs and broadcasts:
   * // "📩 [SOCKET] Message received from abc123: { status: 'submitted', documentType: 'passport' }"
   * // Broadcasts 'ping' event with data to all clients
   * ```
   */
  private handleMessage(socket: Socket, data: any) {
    console.log(`📩 [SOCKET] Message received from ${socket.id}:`, data);
    this.broadcast('ping', data);
  }

  /**
   * Locates Socket.IO connections by user ID for targeted messaging.
   * Searches through all connected sockets to find specific user connections.
   * Enables user-specific message delivery and connection management.
   *
   * @private
   * @method findSocketByUserId
   * @param {string} userId - User ID to search for among connected sockets
   * @returns {Socket | undefined} Socket connection for the user, or undefined if not found
   * @memberof SocketHelper
   *
   * @description
   * Socket lookup process:
   * - Iterates through all connected socket instances
   * - Matches socket.data.userId with target user ID
   * - Returns first matching socket connection
   * - Returns undefined if user is not connected
   *
   */
  private findSocketByUserId(userId: string): Socket | undefined {
    return Array.from(this.io.sockets.sockets.values()).find((socket) => socket.data.userId === userId);
  }

  /**
   * Handles user joining communication rooms for group messaging.
   * Adds users to specific rooms and notifies other room members.
   * Enables organized group communication and topic-based messaging.
   *
   * @private
   * @method joinRoom
   * @param {Socket} socket - Socket connection joining the room
   * @param {string} room - Room name or identifier to join
   * @memberof SocketHelper
   *
   * @description
   * Room joining process:
   * - Adds socket to the specified room
   * - Logs room membership change
   * - Notifies other room members of new user
   * - Enables room-specific message broadcasting
   *
   * // Logs: "👥 [SOCKET] User abc123 joined room: offering-507f1f77bcf86cd799439011"
   * // Other room members receive 'userJoined' event
   * ```
   */
  private joinRoom(socket: Socket, room: string) {
    socket.join(room);
    console.log(`👥 [SOCKET] User ${socket.id} joined room: ${room}`);
    this.io.to(room).emit('userJoined', { userId: socket.data.userId, room });
  }

  /**
   * Handles user leaving communication rooms with cleanup and notifications.
   * Removes users from specific rooms and notifies remaining members.
   * Maintains clean room membership and proper disconnect handling.
   *
   * @private
   * @method leaveRoom
   * @param {Socket} socket - Socket connection leaving the room
   * @param {string} room - Room name or identifier to leave
   * @memberof SocketHelper
   *
   * @description
   * Room leaving process:
   * - Removes socket from the specified room
   * - Logs room membership change
   * - Notifies remaining room members of user departure
   * - Cleans up room-specific subscriptions
   *
   * // Logs: "🚪 [SOCKET] User abc123 left room: offering-507f1f77bcf86cd799439011"
   * // Remaining room members receive 'userLeft' event
   * ```
   */
  private leaveRoom(socket: Socket, room: string) {
    socket.leave(room);
    console.log(`🚪 [SOCKET] User ${socket.id} left room: ${room}`);
    this.io.to(room).emit('userLeft', { userId: socket.data.userId, room });
  }

  /**
   * Handles client disconnection with proper cleanup and logging.
   * Manages graceful disconnection and resource cleanup for departed clients.
   * Maintains connection monitoring and system health visibility.
   *
   * @private
   * @method handleDisconnect
   * @param {Socket} socket - Socket connection that has disconnected
   * @memberof SocketHelper
   *
   * @description
   * Disconnect handling:
   * - Logs user disconnection for monitoring
   * - Automatic cleanup of socket resources
   * - Room membership cleanup (handled by Socket.IO)
   * - Session termination and memory cleanup
   *
   */
  private handleDisconnect(socket: Socket) {
    console.log(`🔌 [SOCKET] User disconnected: ${socket.id}`);
  }
}

/**
 * Global SocketHelper instance for application-wide real-time communication.
 * Singleton pattern ensures consistent socket management across the application.
 *
 * @exports {SocketHelper} socketHelper - Global socket helper instance
 */
export let socketHelper: SocketHelper;

/**
 * Initializes the global Socket.IO helper with server instance.
 * Creates singleton SocketHelper instance for application-wide use.
 * Sets up real-time communication infrastructure with proper error handling.
 *
 * @function initializeSocket
 * @param {Server} io - Socket.IO server instance for WebSocket communication
 * @exports
 *
 * @description
 * Initialization process:
 * - Creates SocketHelper singleton if not already initialized
 * - Prevents duplicate initialization for system stability
 * - Logs successful initialization with port information
 * - Handles initialization errors gracefully
 *
 * const app = express();
 * const httpServer = createServer(app);
 * const io = new Server(httpServer, {
 *   cors: { origin: "*", methods: ["GET", "POST"] }
 * });
 *
 * // Initialize socket helper
 * initializeSocket(io);
 *
 * httpServer.listen(3000, () => {
 *   console.log('Server running with Socket.IO support');
 * });
 * ```
 */
export const initializeSocket = (io: Server) => {
  try {
    if (!socketHelper) {
      socketHelper = new SocketHelper(io);
    }
    logger.info(`🚀 [SOCKET] Server initialized on port ${CONFIG.SOCKET_PORT}`);
  } catch (error) {
    console.log('❌ [SOCKET] Error in socket initialization:\n', error);
  }
};
