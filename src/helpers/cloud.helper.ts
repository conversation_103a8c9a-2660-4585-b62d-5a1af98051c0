/* eslint-disable indent */
import { S3 } from 'aws-sdk';
import * as path from 'path';
import axios from 'axios';
import CONFIG from '../config/env';
import logger from './logging/logger.helper';
import { RES_MSG, RESPONSES } from '../utils/responseUtils';
import { DocumentTypesEnum, offeringDocumentTypesEnum, PromiseResolve } from '../utils/common.interface';
import CustomError from './customError.helper';

/**
 * Cloud storage helper class providing comprehensive file management for AWS S3.
 * Handles file uploads, deletions, organization, and URL generation with environment-specific folder structures.
 * Supports document type categorization, automatic file cleanup, and secure file operations.
 * Integrates with AWS S3 for scalable and reliable cloud storage solutions.
 *
 * @class CloudHelper
 * @description Complete cloud storage management service with AWS S3 integration
 *
 * // Upload a file
 * const uploadResult = await cloudHelper.uploadFiles(
 *   'userId123',
 *   fileObject,
 *   'PROFILE_PICTURE',
 *   'userDocuments',
 *   'offeringId456'
 * );
 *
 * // Delete a file
 * const deleteSuccess = await cloudHelper.deleteFile('bucket/folder/file.jpg');
 *
 * // Delete old images in a folder
 * await cloudHelper.deleteOldImages('environment/folder/userId/documentType/');
 * ```
 */
export class CloudHelper {
  /**
   * AWS S3 service instance for cloud storage operations.
   * Configured with region and credentials for secure file operations.
   *
   * @private
   * @type {S3}
   * @memberof CloudHelper
   */
  private s3: S3;

  /**
   * S3 bucket name for file storage.
   * Retrieved from environment configuration for deployment flexibility.
   *
   * @private
   * @type {string}
   * @memberof CloudHelper
   */
  private bucketName: string;

  /**
   * Initializes the CloudHelper with AWS S3 configuration.
   * Sets up S3 client connection and validates required configuration parameters.
   *
   * @constructor
   * @description
   * Initialization includes:
   * - AWS S3 client configuration
   * - Environment validation
   * - Bucket name assignment
   * - Error handling for missing credentials
   */
  constructor() {
    this.initClient();
  }

  /**
   * Initializes the AWS S3 client with environment configuration.
   * Validates required AWS configuration parameters and establishes connection.
   *
   * @private
   * @method initClient
   * @memberof CloudHelper
   * @throws {Error} Throws error if required AWS configuration is missing
   *
   * @description
   * Configuration validation:
   * - Bucket name presence
   * - AWS region specification
   * - Error logging for troubleshooting
   * - Graceful failure handling
   */
  private initClient() {
    try {
      if (
        // !CONFIG.AWS?.ACCESS_KEY_ID || !CONFIG.AWS?.SECRET_ACCESS_KEY
        !CONFIG.AWS?.BUCKET_NAME ||
        !CONFIG.AWS?.REGION
      ) {
        throw new Error('AWS S3 keys are not set correctly');
      }

      this.s3 = new S3({
        // accessKeyId: CONFIG.AWS.ACCESS_KEY_ID,
        // secretAccessKey: CONFIG.AWS.SECRET_ACCESS_KEY,
        region: CONFIG.AWS.REGION,
      });
      this.bucketName = CONFIG.AWS.BUCKET_NAME;
    } catch (error: any) {
      logger.error(error, 'S3 Init error');
    }
  }

  /**
   * Uploads files to AWS S3 with organized folder structure and automatic cleanup.
   * Supports various document types, environment-specific organization, and old file cleanup.
   * Generates secure URLs and maintains comprehensive file metadata.
   *
   * @async
   * @public
   * @method uploadFiles
   * @param {string} userId - ID of the user uploading the file
   * @param {Express.Multer.File} file - The file being uploaded (from multer middleware)
   * @param {string} [documentType='othersDocs'] - Type of document being uploaded for categorization
   * @param {string} folderName - Primary folder name for organizing files
   * @param {string} [offeringId] - Optional offering ID for offering-specific file organization
   * @returns {Promise<PromiseResolve | any>} Promise resolving to upload result with URL and metadata
   * @throws {Error} Throws error if upload fails or S3 operation errors occur
   * @memberof CloudHelper
   *
   * @description
   * Upload process:
   * 1. File name generation with timestamp and user ID
   * 2. Folder structure creation based on parameters
   * 3. Old file cleanup (if applicable for document type)
   * 4. S3 upload with proper content type and cache settings
   * 5. Public URL generation for file access
   * 6. Comprehensive error handling and logging
   *
   * Folder structure:
   * - With offering: `environment/folderName/userId/offeringId/documentType/fileName`
   * - Without offering: `environment/folderName/userId/documentType/fileName`
   *
   * File naming convention:
   * - `userId-timestamp-documentType.extension`
   * - Ensures uniqueness and traceability
   *
   * Cleanup behavior:
   * - Automatically removes old files for most document types
   * - Preserves multiple files for specific types (ID cards, custom images)
   * - Maintains clean storage and prevents clutter
   *
   * // Upload offering document with cleanup
   * const offeringUpload = await cloudHelper.uploadFiles(
   *   'issuer456',
   *   documentFile,
   *   'PROSPECTUS',
   *   'offeringDocs',
   *   'offering789'
   * );
   *
   * // Upload ID card (preserves multiple files)
   * const idUpload = await cloudHelper.uploadFiles(
   *   'user123',
   *   frontIdFile,
   *   'FRONT_ID_CARD',
   *   'kycDocuments'
   * );
   * ```
   */
  public async uploadFiles(userId: string, file: Express.Multer.File, documentType: string = 'othersDocs', folderName: string, offeringId?: string): Promise<PromiseResolve | any> {
    try {
      const envFolder = CONFIG.ENVIRONMENT;
      const fileExtension = path.extname(file.originalname);
      const currentTimestamp = Date.now();
      const newFileName = `${userId}-${currentTimestamp}-${documentType}${fileExtension}`;
      const keyName = offeringId ? `${envFolder}/${folderName}/${userId}/${offeringId}/${documentType}/${newFileName}` : `${envFolder}/${folderName}/${userId}/${documentType}/${newFileName}`;

      const oldFolderName = offeringId ? `${envFolder}/${folderName}/${userId}/${offeringId}/${documentType}/` : `${envFolder}/${folderName}/${userId}/${documentType}/`;

      const excludedDocumentTypes: Array<offeringDocumentTypesEnum | DocumentTypesEnum> = [offeringDocumentTypesEnum.url, DocumentTypesEnum.FRONT_ID_CARD, DocumentTypesEnum.BACK_ID_CARD, offeringDocumentTypesEnum.customImage];

      if (!excludedDocumentTypes.includes(documentType as offeringDocumentTypesEnum | DocumentTypesEnum)) {
        await this.deleteOldImages(oldFolderName);
      }

      const params = {
        Bucket: this.bucketName,
        Key: keyName,
        Body: file.buffer,
        ContentType: file.mimetype,
        CacheControl: 'max-age=0',
      };

      await this.s3.upload(params).promise();
      const endPoint = `https://${this.bucketName}.s3.${CONFIG.AWS.REGION}.amazonaws.com/${keyName}`;
      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.CREATE_SUCCESS,
        data: { url: endPoint, keyName: `${this.bucketName}/${keyName}` },
      };
    } catch (error: any) {
      logger.error(error, 'Failed to uploadFile');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }

  /**
   * Deletes a specific file from AWS S3 storage.
   * Provides clean file removal with error handling and logging.
   *
   * @async
   * @public
   * @method deleteFile
   * @param {string} keyName - The S3 key (path) of the file to delete
   * @returns {Promise<boolean>} Promise resolving to true if deletion successful, false if failed
   * @memberof CloudHelper
   *
   * @description
   * Deletion features:
   * - Direct S3 object deletion
   * - Error handling and logging
   * - Boolean return for operation status
   * - Graceful failure handling
   *
   * if (deleteSuccess) {
   *   console.log('File deleted successfully');
   * } else {
   *   console.log('File deletion failed');
   * }
   * ```
   */
  public async deleteFile(keyName: string): Promise<boolean> {
    try {
      await this.s3.deleteObject({ Bucket: this.bucketName, Key: keyName }).promise();
      return true;
    } catch (err: any) {
      logger.error(err, 'Failed to deleteFile');
      return false;
    }
  }

  /**
   * Delete old images in a folder on S3
   * @param oldFolderName - The folder name containing the images to delete
   */
  private async deleteOldImages(oldFolderName: string): Promise<void> {
    try {
      const listParams = {
        Bucket: this.bucketName,
        Prefix: oldFolderName,
      };
      const listedObjects = await this.s3.listObjectsV2(listParams).promise();
      if (!listedObjects.Contents || listedObjects.Contents.length === 0) {
        logger.info(`No files found in ${oldFolderName} to delete.`);
        return;
      }
      const deleteParams = {
        Bucket: this.bucketName,
        Delete: { Objects: listedObjects.Contents.map(({ Key }) => ({ Key })) },
      };
      await this.s3.deleteObjects(deleteParams).promise();
    } catch (error) {
      logger.error(error, `Failed to delete images in folder ${oldFolderName}:`);
    }
  }

  /**
   * Copy all files from one folder to another in S3
   * @param oldOfferingId - The ID of the source offering
   * @param newOfferingId - The ID of the destination offering
   * @param userId - ID of the user
   * @param folderName - Folder name for organizing files
   */
  public async copyFolder(oldOfferingId: string, newOfferingId: string, userId: string, folderName: string): Promise<any> {
    try {
      const envFolder = CONFIG.ENVIRONMENT;
      const sourceFolderName = `${envFolder}/${folderName}/${userId}/${oldOfferingId}`;
      const destinationFolderName = `${envFolder}/${folderName}/${userId}/${newOfferingId}`;
      const listParams = {
        Bucket: this.bucketName,
        Prefix: sourceFolderName,
      };
      const listedObjects = await this.s3.listObjectsV2(listParams).promise();
      if (!listedObjects.Contents || listedObjects.Contents.length === 0) {
        logger.info(`No files found in the source folder ${sourceFolderName}`);
        return { status: RESPONSES.NOTFOUND, error: true, message: RES_MSG.COMMON.NO_RECORD };
      }
      const copyPromises = listedObjects.Contents.map(async ({ Key }) => {
        if (!Key) return;
        const destinationKey = Key.replace(sourceFolderName, destinationFolderName);
        await this.s3
          .copyObject({
            Bucket: this.bucketName,
            CopySource: `${this.bucketName}/${Key}`,
            Key: destinationKey,
          })
          .promise();
      });
      await Promise.all(copyPromises);
      logger.info(`Successfully copied folder from ${sourceFolderName} to ${destinationFolderName}`);
      return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.CREATE_SUCCESS };
    } catch (error: any) {
      logger.error(error, 'Failed to copy folder');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }

  /**
   * Fetches data from the provided URL and converts it to base64 format.
   * @param url - The URL of the file to fetch
   * @returns A Promise that resolves to the base64 string of the file
   */
  public async getFileAsBase64(url: string): Promise<PromiseResolve | any> {
    try {
      const response = await axios.get(url, { responseType: 'arraybuffer' });
      const base64String = Buffer.from(response.data).toString('base64');
      return base64String;
    } catch (error: any) {
      logger.error(error, 'Failed to fetch and convert file to base64');
      throw new CustomError(error.message || 'Failed to fetch and convert file to base64', error.status);
    }
  }
}

export default new CloudHelper();
