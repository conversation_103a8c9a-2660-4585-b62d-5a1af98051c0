/* eslint-disable @typescript-eslint/no-unused-vars */
import Big from 'big.js';

/**
 * Mathematical operation types supported by the BigMath calculator.
 * Defines the available arithmetic operations for precise financial calculations.
 *
 * @type {Operation}
 * @description Supported arithmetic operations for high-precision calculations
 */
type Operation = 'add' | 'sub' | 'mul' | 'div';

/**
 * Performs high-precision arithmetic operations for financial calculations without floating-point errors.
 * Uses the Big.js library to ensure accurate decimal arithmetic for monetary values and token quantities.
 * Prevents JavaScript's native floating-point precision issues that can occur in financial applications.
 * Supports addition, subtraction, multiplication, and division with string output for precision preservation.
 *
 * @function calculate
 * @param {Operation} operation - The arithmetic operation to perform ('add', 'sub', 'mul', 'div')
 * @param {number | string} [num1='0'] - The first operand (supports both number and string input)
 * @param {number | string} [num2='0'] - The second operand (supports both number and string input)
 * @param {number} [precision] - Optional precision for future truncation implementation (currently unused)
 * @returns {string} The calculation result as a string to preserve full precision
 * @throws {Error} Throws error for invalid operations or mathematical errors (e.g., division by zero)
 *
 * @description
 * Financial calculation features:
 * - Eliminates floating-point precision errors common in JavaScript
 * - Preserves full decimal precision for monetary calculations
 * - Handles large numbers without precision loss
 * - Sanitizes input by removing 'n' suffix from BigInt strings
 * - Returns string format to maintain precision across systems
 * - Supports both string and number inputs for flexibility
 *
 * Supported operations:
 * - 'add': Addition of two numbers (num1 + num2)
 * - 'sub': Subtraction of two numbers (num1 - num2)
 * - 'mul': Multiplication of two numbers (num1 × num2)
 * - 'div': Division of two numbers (num1 ÷ num2)
 *
 * Input sanitization:
 * - Removes 'n' suffix from BigInt string representations
 * - Handles both string and numeric inputs
 * - Provides default values of '0' for missing operands
 * - Validates operation types for security
 *
 * Precision handling:
 * - Maintains full decimal precision throughout calculation
 * - Returns string representation to prevent precision loss
 * - Future support for precision truncation without rounding
 * - Suitable for financial and monetary calculations
 *
 * // Financial calculations
 * const tokenPrice = calculate('div', '1000000', '500000'); // '2'
 * const feeAmount = calculate('mul', '10000', '0.025');     // '250'
 * const netAmount = calculate('sub', '10000', '250');       // '9750'
 *
 * // Handle BigInt strings (removes 'n' suffix)
 * const bigIntCalc = calculate('add', '12345678901234567890n', '100');
 *
 * // Token quantity calculations
 * const tokens = calculate('div', '50000', '125.50');  // Token quantity from investment
 * const value = calculate('mul', '1000', '0.15');      // Current value calculation
 *
 * // Percentage calculations
 * const percentage = calculate('div', '75', '100');    // '0.75' (75%)
 * const percentageOf = calculate('mul', '10000', '0.75'); // '7500' (75% of 10000)
 * ```
 *
 *   calculateFees(amount: string, feePercentage: string): string {
 *     return calculate('mul', amount, feePercentage);
 *   }
 *
 *   calculateNetAmount(grossAmount: string, fees: string): string {
 *     return calculate('sub', grossAmount, fees);
 *   }
 * }
 *
 * // Example usage
 * const calculator = new TokenCalculator();
 * const investmentAmount = '10000.00';
 * const tokenPrice = '25.50';
 * const feePercentage = '0.025'; // 2.5%
 *
 * const tokens = calculator.calculateTokens(investmentAmount, tokenPrice);
 * const fees = calculator.calculateFees(investmentAmount, feePercentage);
 * const netAmount = calculator.calculateNetAmount(investmentAmount, fees);
 *
 * console.log(`Tokens: ${tokens}`);      // Tokens: 392.156862745098039215686274509804
 * console.log(`Fees: ${fees}`);          // Fees: 250
 * console.log(`Net Amount: ${netAmount}`); // Net Amount: 9750
 * ```
 */
export function calculate(operation: Operation, num1: number | string = '0', num2: number | string = '0', precision?: number): string {
  // Use regex to remove 'n' if it exists
  const sanitizedNum1 = num1.toString().replace(/n/g, '');
  const sanitizedNum2 = num2.toString().replace(/n/g, '');

  const bigNum1 = new Big(sanitizedNum1);
  const bigNum2 = new Big(sanitizedNum2);
  let result: Big;

  switch (operation) {
    case 'add':
      result = bigNum1.plus(bigNum2);
      break;
    case 'sub':
      result = bigNum1.minus(bigNum2);
      break;
    case 'mul':
      result = bigNum1.times(bigNum2);
      break;
    case 'div':
      result = bigNum1.div(bigNum2);
      break;
    default:
      throw new Error('Invalid operation. Use "add", "sub", "mul", or "div".');
  }
  // If precision is specified, truncate without rounding
  // if (precision !== undefined) {
  //   const factor = new Big(10).pow(precision); // 10^precision
  //   result = result.times(factor).round(0, Big.roundDown).div(factor); // Truncate without rounding
  // }
  return result.toString();
}
