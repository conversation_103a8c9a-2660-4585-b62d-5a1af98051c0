import axios from 'axios';
import UserService from '../component/userAuthentications/service';
import { sumSubKycStatusEnum, PromiseResolve, WebhookTypeEnum } from '../utils/common.interface';
import { RESPONSES, RES_MSG } from '../utils/responseUtils';
import CustomError from './customError.helper';
import CONFIG from '../config/env';
import logger from './logging/logger.helper';
import * as crypto from 'crypto';
// const crypto = require('crypto');
// const rp = require('request-promise');

const SUMSUB_BASE_URL = 'https://api.sumsub.com';
const configs: any = {};
configs.baseURL = SUMSUB_BASE_URL;
class SumSubHelper {
  [x: string]: any;
  public async getToken(userId: string, type: string, username: string, ttlInSecs = '600', levelName = type || 'basic-kyc-level'): Promise<any> {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    levelName = type; // Keeping the same logic

    const url = `/resources/accessTokens?userId=${userId}&ttlInSecs=${ttlInSecs}&levelName=${type}&name=${encodeURIComponent(username)}`;

    try {
      let options = {
        method: 'POST',
        url: `${configs.baseURL}${url}`,
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      };

      // Signing the request (assuming `this.sign` modifies options correctly)
      options = await this.sign(options, url);

      const response = await axios(options);
      return response.data; // Axios automatically parses JSON
    } catch (err) {
      logger.error(err, 'sign error');
      throw err;
    }
  }

  // set sumsub headers
  public async sign(config: any, url: any) {
    try {
      const time = Math.round(Date.now() / 1000);
      const secretKey = CONFIG.SUMSUB.SUMSUB_SECRET_KEY;
      const hmacSha256 = crypto
        .createHmac('sha256', secretKey)
        .update(time + config.method.toUpperCase() + url)
        .digest('hex');
      //    const hmacSha256 = crypto.createHmac('sha256', SUMSUB_SECRET_KEY).update(time + config.method.toUpperCase() + url).digest('hex');
      const token = CONFIG.SUMSUB.SUMSUB_TOKEN;
      config.headers['X-App-Token'] = token;
      config.headers['X-App-Access-Ts'] = time;
      config.headers['X-App-Access-Sig'] = hmacSha256;

      return config;
    } catch (err: any) {
      logger.error(err, 'getToken error');

      return err;
    }
  }

  public async getExistApplicant(userId: any) {
    try {
      const getExistApplicant: any = await this.kycRepo.findOne({ where: { userId }, raw: true });

      return getExistApplicant;
    } catch (error: any) {
      return error;
    }
  }

  public async kycWebhookService(userKycData: any, webhookType: WebhookTypeEnum, kycApplicationId: any, externalUserId: any, levelName: any) {
    try {
      let result;
      let kycData: any;
      switch (webhookType) {
        case WebhookTypeEnum.APPLICANT_CREATED:
          kycData = { kycApplicationId, webhookReceived: webhookType, userId: externalUserId, sumSubKycStatus: sumSubKycStatusEnum.PENDING, levelName };
          result = await this.createOrUpdateKycUserData(kycData);
          logger.info(webhookType, ': res  ===>>>>>>', result?.data);

          return result?.data;

        case WebhookTypeEnum.APPLICANT_PENDING:
          kycData = { kycApplicationId, webhookReceived: webhookType, userId: externalUserId, sumSubKycStatus: sumSubKycStatusEnum.PENDING, levelName };
          result = await this.createOrUpdateKycUserData(kycData);

          return result;

        case WebhookTypeEnum.APPLICANT_REVIEWED: {
          const { reviewAnswer } = userKycData.reviewResult; // GREEN/RED
          switch (reviewAnswer) {
            case 'GREEN':
              kycData = { reviewAnswer, kycApplicationId, webhookReceived: webhookType, userId: externalUserId, sumSubKycStatus: sumSubKycStatusEnum.APPROVED, levelName };
              result = await this.createOrUpdateKycUserData(kycData);
              logger.info(webhookType, ': res  ===>>>>>>', result?.data);

              return result?.data;

            case 'RED':
              kycData = { reviewAnswer, kycApplicationId, webhookReceived: webhookType, userId: externalUserId, sumSubKycStatus: sumSubKycStatusEnum.DECLINED, levelName };
              result = await this.createOrUpdateKycUserData(kycData);
              logger.info(webhookType, ': res  ===>>>>>>', result?.data);

              return result?.data;

            default:
              logger.info(' \n\nWebhook reviewed status:\n\n ');
          }
          break;
        }

        case WebhookTypeEnum.APPLICANT_ON_HOLD:
          kycData = { reviewAnswer: userKycData.reviewResult?.reviewAnswer, kycApplicationId, webhookReceived: webhookType, userId: externalUserId, sumSubKycStatus: sumSubKycStatusEnum.HOLD, levelName };
          result = await this.createOrUpdateKycUserData(kycData);
          logger.info(webhookType, ': res  ===>>>>>>', result?.data);

          return result?.data;

        case WebhookTypeEnum.APPLICANT_PRECHECKED:
          kycData = { reviewAnswer: userKycData.reviewResult?.reviewAnswer, kycApplicationId, webhookReceived: webhookType, userId: externalUserId, sumSubKycStatus: sumSubKycStatusEnum.PRECHECKRED, levelName };
          result = await this.createOrUpdateKycUserData(kycData);
          logger.info(webhookType, ': res  ===>>>>>>', result?.data);

          return result?.data;

        default:
          logger.info(' \n\nWebhook reviewed status:\n\n ', kycData);
          break;
      }
    } catch (error) {
      logger.error(error, 'kycWebhookService error-------');

      return { error: true, message: error.message };
    }
  }

  public async createOrUpdateKycUserData(data: any) {
    try {
      const res: PromiseResolve = await UserService.fetchUserDetails({ _id: data?.userId });
      if (res.error) {
        throw new CustomError(RES_MSG.USER.EVENT_RECIEVED, RESPONSES.SUCCESS);
      } else {
        const updatedUser = await UserService.updateUserDetails(data, { _id: data.userId });

        return { error: false, message: 'Success', data: updatedUser };
      }
    } catch (err: any) {
      logger.info('createOrUpdateKycUser :>>>>>> ', err);

      return { status: err.status, error: true, message: err.message };
    }
  }
}

export default new SumSubHelper();
