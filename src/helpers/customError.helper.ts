/**
 * Custom error class extending the native Error class with HTTP status code support.
 * Provides standardized error handling across the application with proper status codes.
 * Maintains stack traces and error metadata for debugging and logging purposes.
 *
 * @class CustomError
 * @extends {Error}
 * @description Enhanced error class with HTTP status code integration for API responses
 *
 * // Throw a validation error
 * throw new CustomError('Invalid email format', 400);
 *
 * // Throw an authorization error
 * throw new CustomError('Access denied', 403);
 *
 * // Catch and handle custom errors
 * try {
 *   // Some operation that might fail
 * } catch (error) {
 *   if (error instanceof CustomError) {
 *     res.status(error.status).json({ message: error.message });
 *   }
 * }
 * ```
 */
class CustomError extends Error {
  /**
   * HTTP status code associated with the error.
   * Used for API response status codes and error categorization.
   *
   * @type {number}
   * @memberof CustomError
   */
  status: number;

  /**
   * Creates a new CustomError instance with message and HTTP status code.
   * Automatically captures stack trace and sets appropriate error metadata.
   *
   * @constructor
   * @param {string} message - Error message describing what went wrong
   * @param {number} status - HTTP status code (400, 401, 403, 404, 500, etc.)
   *
   * @description
   * Constructor features:
   * - Calls parent Error constructor with message
   * - Sets HTTP status code for API responses
   * - Captures stack trace for debugging
   * - Sets error name to class name for identification
   * - Maintains proper prototype chain
   *
   * Common status codes:
   * - 400: Bad Request (validation errors, malformed data)
   * - 401: Unauthorized (authentication required)
   * - 403: Forbidden (insufficient permissions)
   * - 404: Not Found (resource doesn't exist)
   * - 409: Conflict (duplicate resources, business logic conflicts)
   * - 422: Unprocessable Entity (semantic errors)
   * - 500: Internal Server Error (unexpected errors)
   *
   * // Create authentication error
   * const authError = new CustomError('Invalid credentials', 401);
   *
   * // Create permission error
   * const permissionError = new CustomError('Admin access required', 403);
   *
   * // Create not found error
   * const notFoundError = new CustomError('User not found', 404);
   *
   * // Create conflict error
   * const conflictError = new CustomError('Email already exists', 409);
   * ```
   */
  constructor(message: string, status: number) {
    super(message);
    this.status = status;
    this.name = this.constructor.name;
    Error.captureStackTrace(this, this.constructor);
  }
}

export default CustomError;
