import { createClient } from 'redis';
import CONFIG from '../config/env';
import logger from './logging/logger.helper';
import { RedisSetOptions } from '../utils/common.interface';

const { REDIS } = CONFIG;

export class RedisHelper {
  public client: any;

  public clientInternal: any;

  private host: string = `${REDIS.HOST}`;

  public isConnected: boolean = false;

  /**
       * Purpose:
            Establishes a connection to Redis.
       * Usage:
            Checks if already connected (this.isConnected).
            Creates a Redis client (this.clientInternal) using createClient with TLS options.
            Sets up event listeners for errors ('error') and readiness ('ready').
            Selects the default database (database 0 by default).
            Sets this.isConnected to true upon successful connection.
    */
  public async connectRedis() {
    try {
      if (this.isConnected) {
        return false;
      }
      const tlsOptions = { url: this.host };
      this.clientInternal = createClient(tlsOptions);
      this.clientInternal.on('error', (err: any) => {
        logger.error(err, 'Redis Client Error');
        throw new Error(err);
      });
      this.clientInternal.on('ready', () => {
        logger.info('Redis Client Ready for use   ');
      });

      await this.clientInternal.connect();
      this.isConnected = true;
    } catch (err: any) {
      logger.error(err, 'Failed to create Redis client');
      this.isConnected = false;
      this.connectRedis();

      return null;
    }
  }

  /**
     * Purpose:
         Sets a string value in Redis for a given key.
     * Usage:
        Selects the specified database (database).
        Uses clientInternal.set(key, value, options) to set the key-value pair.
        Optional expiration (expires) in seconds can be set.
    */
  public async setString(key: string, value: string | boolean | object, expires: number = 0, database: number = 0): Promise<string | null> {
    try {
      await this.clientInternal.select(database);
      const options: RedisSetOptions = { NX: false };
      if (expires) {
        options.EX = expires;
      }
      const response: string | null = await this.clientInternal.set(key, value, options);

      return response;
    } catch (err: any) {
      logger.error(err, 'Redis Error during Set String');

      return null;
    }
  }

  /**
     * Purpose:
         Retrieves a string value from Redis for a given key.
     * Usage:
        Selects the specified database (database).
        Uses clientInternal.get(key) to fetch the value associated with key.
     */
  public async getString(key: string, database: number = 0): Promise<string | null> {
    try {
      await this.clientInternal.select(database);

      return await this.clientInternal.get(key);
    } catch (err: any) {
      logger.error(err, 'Redis Error during Get String');

      return null;
    }
  }

  /**
     * Purpose:
        Deletes a key from Redis cache.
     * Usage:
        Selects the specified database (database).
        Uses clientInternal.del(key) to delete the key.
    */

  public async deleteKey(key: string, database: number = 0): Promise<number | null> {
    try {
      await this.clientInternal.select(database);

      return await this.clientInternal.del(key);
    } catch (err: any) {
      logger.error(err, 'Redis Error during Del String');

      return null;
    }
  }

  /**
     * Purpose:
        increment key value from Redis cache.
     * Usage:
        Selects the specified database (database).
        Uses clientInternal.incr(key) to increment the key.
    */
  public async incrementKey(key: string, expireTimeInSeconds: string | number, database: number = 0): Promise<number | null> {
    try {
      await this.clientInternal.select(database);
      const incrementedValue = await this.clientInternal.incr(key);
      if (incrementedValue) {
        await this.clientInternal.expire(key, expireTimeInSeconds);
      }

      return incrementedValue;
    } catch (err: any) {
      logger.error(err, 'Redis Error during Increment Key');

      return null;
    }
  }

  /**
       * Purpose:
          check the expire time.
       * Usage:

      */
  public async getTTL(key: string) {
    try {
      const ttl = await this.clientInternal.ttl(key); // TTL in seconds

      return ttl;
    } catch (err) {
      logger.error(err, 'Error fetching TTL:');

      return CONFIG.REDIS.LOGIN_BLOCK_TIME;
    }
  }
}
export default new RedisHelper();
