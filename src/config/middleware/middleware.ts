import * as bodyParser from 'body-parser';
import * as compression from 'compression';
import * as cookieParser from 'cookie-parser';
import * as cors from 'cors';
import * as express from 'express';
import helmet from 'helmet';
import { HttpError } from '../error/index';
import { sendHttpErrorModule } from '../error/sendHttpError';
import { RESPONSES } from '../../utils/responseUtils';
import { UserInfo } from '../../utils/common.interface';
import logger from '../../helpers/logging/logger.helper';

/**
 * Extends the Express Request interface to include user information.
 * Adds userInfo property that contains authenticated user details.
 *
 * @interface Request
 * @extends {express.Request}
 */
declare module 'express' {
  export interface Request {
    /**
     * User information object containing authenticated user details.
     * Available on all authenticated requests after JWT middleware processing.
     *
     * @type {UserInfo}
     */
    userInfo: UserInfo;
  }
}

/**
 * Configures Express application middleware stack including security, parsing, and CORS.
 * Sets up comprehensive middleware chain for request processing, security headers,
 * and cross-origin resource sharing policies.
 *
 * @export
 * @function configure
 * @param {express.Application} app - The Express application instance to configure
 * @returns {void}
 *
 * @description
 * Middleware configuration includes:
 * - Trust proxy settings for load balancer support
 * - Body parsing with size limits for JSON and URL-encoded data
 * - Cookie parsing for session management
 * - Response compression for improved performance
 * - Security headers via Helmet (HSTS, CSP, referrer policy)
 * - CORS configuration for cross-origin requests
 * - Custom error handling integration
 * - Method validation and security headers
 *
 */
export function configure(app: express.Application): void {
  app.set('trust proxy', 1);
  // express middleware
  app.use(bodyParser.urlencoded({ extended: false }));
  // app.use(bodyParser.json());
  app.use(bodyParser.json({ limit: '10mb' }));
  // parse Cookie header and populate req.cookies with an object keyed by the cookie names.
  app.use(cookieParser());
  // returns the compression middleware
  app.use(compression());
  // helps you secure your Express apps by setting various HTTP headers
  app.use(
    helmet({
      contentSecurityPolicy: true, // Disable Helmet's CSP to use the manually configured one.
      hsts: {
        maxAge: 31536000, // One year in seconds
        includeSubDomains: true,
        preload: true,
      },
      referrerPolicy: { policy: 'same-origin' },
    }),
  );

  // providing a Connect/Express middleware that can be used to enable CORS with various options
  app.use(cors());

  // custom errors
  app.use(sendHttpErrorModule);

  // cors
  app.use((req, res, next) => {
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, PATCH, DELETE');
    // eslint-disable-next-line no-useless-concat
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With,' + ' Content-Type, Accept,' + ' Authorization,');
    res.header('Access-Control-Allow-Credentials', 'true');
    const allowedMethods = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'];
    if (!allowedMethods.includes(req.method)) {
      return res.status(RESPONSES.METHOD_NOT_ALLOWED).send('Method Not Allowed');
    }
    next();
  });
}

/**
 * Extended Express Response interface with custom error handling capabilities.
 * Adds sendHttpError method for consistent error response formatting.
 *
 * @interface CustomResponse
 * @extends {express.Response}
 */
interface CustomResponse extends express.Response {
  /**
   * Sends a formatted HTTP error response to the client.
   * Provides consistent error response structure across the application.
   *
   * @method sendHttpError
   * @param {HttpError | Error} error - The error object to send
   * @param {string} [message] - Optional custom error message
   * @returns {void}
   */
  sendHttpError: (error: HttpError | Error, message?: string) => void;
}

/**
 * Initializes global error handling middleware for the Express application.
 * Catches and processes all unhandled errors with appropriate response formatting.
 * Provides different error handling behavior for development and production environments.
 *
 * @export
 * @function initErrorHandler
 * @param {express.Application} app - The Express application instance to add error handling to
 * @returns {void}
 *
 * @description
 * Error handling logic:
 * - Converts numeric error codes to HttpError instances
 * - Handles HttpError instances with custom error responses
 * - In development: Returns detailed error messages
 * - In production: Returns generic error messages for security
 * - Logs all errors for debugging and monitoring
 *
 */
export function initErrorHandler(app: express.Application): void {
  app.use((error: Error, req: express.Request, res: CustomResponse) => {
    if (typeof error === 'number') {
      error = new HttpError(error); // next(404)
    }

    if (error instanceof HttpError) {
      res.sendHttpError(error);
    } else if (app.get('env') === 'development') {
      error = new HttpError(500, error.message);
      res.sendHttpError(error);
    } else {
      error = new HttpError(500);
      res.sendHttpError(error, error.message);
    }

    logger.error(error);
  });
}
