import mongoose, { Connection, Mongoose } from 'mongoose';
import CONFIG from '../env/index';
import logger from '../../helpers/logging/logger.helper';

/**
 * Database connection manager class for MongoDB operations.
 * Handles connection establishment, configuration, and cleanup for the application database.
 * Supports both authenticated and non-authenticated MongoDB connections based on configuration.
 *
 * @class Db
 * @description Singleton class that manages MongoDB connection lifecycle
 */
class Db {
  /**
   * The Mongoose instance used for database operations.
   * Provides access to MongoDB through the Mongoose ODM.
   *
   * @public
   * @type {Mongoose}
   * @memberof Db
   */
  public mongooseInstance: Mongoose = mongoose;

  /**
   * The complete MongoDB connection URI string.
   * Dynamically constructed based on authentication configuration.
   *
   * @public
   * @type {string}
   * @memberof Db
   */
  public dbUri: string;

  /**
   * Establishes connection to MongoDB database with proper configuration.
   * Handles both authenticated and non-authenticated connection scenarios,
   * sets up connection event listeners, and configures Mongoose settings.
   *
   * @async
   * @method dbConnection
   * @memberof Db
   * @returns {Promise<void>} Promise that resolves when database connection is established
   * @throws {Error} Throws error if database connection fails
   *
   * @description
   * Connection process:
   * 1. Constructs appropriate connection URI based on credentials
   * 2. Establishes connection with timeout configuration
   * 3. Sets up error and success event listeners
   * 4. Configures Mongoose strict query mode
   *
   */
  public dbConnection = async (): Promise<void> => {
    try {
      if (CONFIG.DATABASE.MONGODB_USER && CONFIG.DATABASE.MONGODB_PASSWORD) {
        this.dbUri = `mongodb://${CONFIG.DATABASE.MONGODB_USER}:${CONFIG.DATABASE.MONGODB_PASSWORD}@${CONFIG.DATABASE.MONGODB_HOST}:${CONFIG.DATABASE.MONGODB_PORT}/${CONFIG.DATABASE.MONGODB_DATABASE}?authSource=admin`;
      } else {
        this.dbUri = `mongodb://${CONFIG.DATABASE.MONGODB_HOST}:${CONFIG.DATABASE.MONGODB_PORT}/${CONFIG.DATABASE.MONGODB_DATABASE}?authSource=admin`;
      }
      await this.mongooseInstance.connect(this.dbUri, { socketTimeoutMS: 0, connectTimeoutMS: 30000 });

      const dbConnection: Connection = this.mongooseInstance.connection;
      dbConnection.on('error', logger.error.bind(console, 'Connection error:'));
      dbConnection.once('open', () => {
        logger.info('Database connected successfully');
      });

      this.mongooseInstance.set('strictQuery', false);
    } catch (error) {
      logger.error(error, 'Error connecting to the database:');
    }
  };

  /**
   * Safely disconnects from the MongoDB database.
   * Ensures proper cleanup of all database connections and resources.
   *
   * @async
   * @method dbDisconnect
   * @memberof Db
   * @returns {Promise<void>} Promise that resolves when database disconnection is complete
   * @throws {Error} Throws error if database disconnection fails
   *
   * @description
   * Used during application shutdown to ensure graceful termination
   * and prevent connection leaks.
   *
   */
  public dbDisconnect = async (): Promise<void> => {
    try {
      await this.mongooseInstance.disconnect();
      logger.info('Database disconnected successfully');
    } catch (error) {
      logger.error(error, 'Error disconnecting from the database:');
    }
  };
}

/**
 * Singleton instance of the database connection manager.
 * Provides global access to database operations throughout the application.
 *
 * @exports {Db} Default database connection instance
 */
export default new Db();
