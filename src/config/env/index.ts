import * as dotenv from 'dotenv';

dotenv.config();

/**
 * Comprehensive configuration interface for the Valuit tokenization platform.
 * Defines all environment-specific settings, API keys, database connections,
 * and external service configurations required for platform operation.
 * Supports multiple deployment environments with environment-specific overrides.
 *
 * @interface IConfig
 * @description Complete configuration structure for all platform services and integrations
 *
 * // All configurations are type-safe and validated
 * console.log(`Environment: ${CONFIG.ENVIRONMENT}`);
 * console.log(`Database: ${CONFIG.DATABASE.MONGODB_HOST}`);
 * ```
 */
interface IConfig {
  /**
   * Current deployment environment identifier.
   * Determines which configuration set to use and enables environment-specific features.
   *
   * @type {string}
   * @values "development", "stage", "qa", "uat", "production"
   */
  ENVIRONMENT: string;

  /**
   * Cap table service URL for equity management and token holder tracking.
   * Used for integration with external cap table management services.
   *
   * @type {string}
   */
  CAP_TABLE_URL: string;

  /**
   * Project-specific configuration settings.
   * Contains application metadata and operational parameters.
   */
  PROJECT: {
    /**
     * Application name for identification and logging.
     * @type {string}
     */
    NAME: string;
    /**
     * Logging level for application events and debugging.
     * Controls verbosity of log output.
     * @type {string}
     * @values "error", "warn", "info", "verbose", "debug", "silly"
     */
    LOG_LEVEL: string;
  };

  /**
   * MongoDB database configuration for data persistence.
   * Contains all settings required for database connectivity and authentication.
   */
  DATABASE: {
    /**
     * MongoDB server hostname or IP address.
     * @type {string}
     */
    MONGODB_HOST: string;
    /**
     * Database authentication username.
     * @type {string}
     */
    MONGODB_USER: string;
    /**
     * Database authentication password.
     * @type {string}
     */
    MONGODB_PASSWORD: string;
    /**
     * MongoDB server port number.
     * @type {number}
     * @default 27017
     */
    MONGODB_PORT: number;
    /**
     * Database name for application data storage.
     * @type {string}
     */
    MONGODB_DATABASE: string;
  };

  /**
   * Redis configuration for caching and session management.
   * Contains settings for Redis connectivity and operational limits.
   */
  REDIS: {
    /**
     * Redis server hostname or IP address.
     * @type {string}
     */
    HOST: string;
    /**
     * Maximum number of login attempts before account lockout.
     * @type {number}
     */
    LOGIN_MAX_ATTEMPT: number;
    /**
     * Duration in minutes for account lockout after failed login attempts.
     * @type {number}
     */
    LOGIN_BLOCK_TIME: number;
    /**
     * OTP code expiration time in seconds.
     * @type {number}
     */
    OTP_EXPIRY: number;
  };

  /**
   * JWT authentication configuration for secure token management.
   * Contains secrets and expiration settings for access and refresh tokens.
   */
  JWT_AUTH: {
    /**
     * Secret key for JWT access token signing and verification.
     * @type {string}
     */
    TOKEN: string;
    /**
     * Access token expiration time.
     * @type {string}
     * @format Time string (e.g., "1h", "30m", "7d")
     */
    AUTH_EXPIRE_TIME: string;
    /**
     * Secret key for JWT refresh token signing and verification.
     * @type {string}
     */
    REFRESH_TOKEN: string;
    /**
     * Refresh token expiration time.
     * @type {string}
     * @format Time string (e.g., "7d", "30d")
     */
    REFRESH_EXPIRE_TIME: string;
    /**
     * Password reset token expiration time.
     * @type {string}
     * @format Time string (e.g., "15m", "1h")
     */
    FORGOT_EXPIRE_TIME: string;
    /**
     * Two-factor authentication token expiration time.
     * @type {string}
     * @format Time string (e.g., "10m", "30m")
     */
    JWT_2FA_EXPIRE: string;
  };

  /**
   * SendGrid email service configuration for transactional emails.
   * Contains API credentials and sender information for email delivery.
   */
  SENDGRID: {
    /**
     * SendGrid API key for email service authentication.
     * @type {string}
     */
    API_KEY: string;
    /**
     * Default sender email address for outgoing emails.
     * @type {string}
     * @format Email address
     */
    SENDER: string;
  };

  /**
   * Twilio SMS service configuration for mobile communication.
   * Contains credentials and phone number for SMS delivery.
   */
  TWILIO: {
    /**
     * Twilio account SID for service authentication.
     * @type {string}
     */
    ACCOUNT_SID: string;
    /**
     * Twilio authentication token for API access.
     * @type {string}
     */
    AUTH_TOKEN: string;
    /**
     * Twilio phone number for sending SMS messages.
     * @type {string}
     * @format Phone number with country code
     */
    PHONE_NUMBER: string;
  };

  /**
   * Google Cloud Platform configuration for file storage and services.
   * Contains credentials and settings for Google Cloud integration.
   */
  GOOGLE: {
    /**
     * Google OAuth client ID for authentication.
     * @type {string}
     */
    CLIENT_ID: string;
    /**
     * Google Cloud project ID for resource identification.
     * @type {string}
     */
    PROJECT_ID: string;
    /**
     * Google Cloud Storage bucket name for file storage.
     * @type {string}
     */
    BUCKET_NAME: string;
    /**
     * Google Cloud Storage upload URL for file operations.
     * @type {string}
     * @format URL
     */
    UPLOAD_URL: string;
    /**
     * Maximum file size for uploads in megabytes.
     * @type {number}
     * @default 5
     */
    MAX_SIZE: number;
  };

  /**
   * Apache Kafka configuration for message streaming and event processing.
   * Contains broker information for distributed messaging.
   */
  KAFKA: {
    /**
     * Kafka broker connection string with host and port.
     * @type {string}
     * @format "host:port"
     */
    BROKERS: string;
  };

  /**
   * gRPC service configuration for inter-service communication.
   * Contains connection details for all microservices in the platform.
   */
  GRPC: {
    /**
     * User service gRPC server hostname.
     * @type {string}
     */
    USER_SERVICE_GRPC_CONTAINER_NAME: string;
    /**
     * User service gRPC server port.
     * @type {string}
     */
    USER_SERVICE_GRPC_PORT: string;
    /**
     * Admin service gRPC server hostname.
     * @type {string}
     */
    ADMIN_SERVICE_GRPC_CONTAINER_NAME: string;
    /**
     * Admin service gRPC server port.
     * @type {string}
     */
    ADMIN_SERVICE_GRPC_PORT: string;
    /**
     * Notification service gRPC server hostname.
     * @type {string}
     */
    NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME: string;
    /**
     * Notification service gRPC server port.
     * @type {string}
     */
    NOTIFICATION_SERVICE_GRPC_PORT: string;
    /**
     * gRPC SSL/TLS encryption configuration flag.
     * @type {string}
     * @values "true", "false"
     */
    GRPC_SSL: string;
  };

  /**
   * SumSub KYC service configuration for identity verification.
   * Contains API credentials for external KYC provider integration.
   */
  SUMSUB: {
    /**
     * SumSub API secret key for service authentication.
     * @type {string}
     */
    SUMSUB_SECRET_KEY: string;
    /**
     * SumSub API token for request authorization.
     * @type {string}
     */
    SUMSUB_TOKEN: string;
  };

  /**
   * DocuSign digital signature service configuration.
   * Contains comprehensive settings for document signing integration.
   */
  DOCUSIGN: {
    /**
     * DocuSign OAuth client ID for application identification.
     * @type {string}
     */
    clientId: string;
    /**
     * DocuSign impersonated user GUID for API operations.
     * @type {string}
     */
    impersonatedUserGuid: string;
    /**
     * DocuSign account ID for API access.
     * @type {string}
     */
    accountId: string;
    /**
     * DocuSign OAuth server URL for authentication.
     * @type {string}
     * @format URL
     */
    OAuthServer: string;
    /**
     * Frontend application URL for DocuSign redirects.
     * @type {string}
     * @format URL
     */
    frontendUrl: string;
    /**
     * DocuSign private key for JWT authentication.
     * @type {string}
     */
    privateKey: string;
    /**
     * DocuSign webhook authentication credentials.
     * Used for verifying incoming webhook requests.
     */
    webhookAuth: {
      /**
       * Webhook authentication username.
       * @type {string}
       */
      userName: string;
      /**
       * Webhook authentication password.
       * @type {string}
       */
      pwd: string;
    };
  };

  /**
   * Blockchain fund contract address for token operations.
   * Smart contract address for tokenized fund management.
   *
   * @type {string}
   * @format Ethereum address (0x...)
   */
  FUND_CONTRACT_ADDRESS: string;

  /**
   * USDC token contract address for payment operations.
   * Stablecoin contract address for transaction processing.
   *
   * @type {string}
   * @format Ethereum address (0x...)
   */
  USDC_ADDRESS: string;

  /**
   * Blockchain RPC URL for network connectivity.
   * Node endpoint for blockchain interaction and transaction submission.
   *
   * @type {string}
   * @format URL
   */
  RPC_URL: string;

  /**
   * Socket.IO server port for real-time communication.
   * WebSocket server port for live updates and notifications.
   *
   * @type {string}
   */
  SOCKET_PORT: string;

  /**
   * Amazon Web Services configuration for cloud storage.
   * Contains credentials and settings for AWS S3 integration.
   */
  AWS: {
    /**
     * AWS S3 bucket name for file storage.
     * @type {string}
     */
    BUCKET_NAME: string;
    /**
     * AWS region for S3 bucket and services.
     * @type {string}
     * @format AWS region code (e.g., "us-east-1", "eu-west-1")
     */
    REGION: string;
  };
}

/**
 * Current Node.js environment identifier.
 * Determines which configuration set to load and environment-specific behavior.
 *
 * @constant {string} NODE_ENV - Environment name from process.env.NODE_ENV
 * @default "development"
 */
const NODE_ENV: string = process.env.NODE_ENV || 'development';

/**
 * Development environment configuration.
 * Optimized for local development with relaxed security and verbose logging.
 *
 * @constant {IConfig} development - Development environment settings
 */
const development: IConfig = {
  SOCKET_PORT: process.env.SOCKET_PORT || '9004',
  CAP_TABLE_URL: process.env.CAP_TABLE_URL,
  ENVIRONMENT: NODE_ENV,
  PROJECT: { NAME: process.env.PROJECT_NAME, LOG_LEVEL: process.env.LOG_LEVEL },
  DATABASE: {
    MONGODB_HOST: process.env.MONGODB_HOST || 'localhost',
    MONGODB_USER: process.env.MONGODB_USER,
    MONGODB_PASSWORD: process.env.MONGODB_PASSWORD,
    MONGODB_PORT: Number(process.env.MONGODB_PORT) || 27017,
    MONGODB_DATABASE: process.env.MONGODB_DATABASE || 'mvp_db',
  },

  REDIS: { HOST: process.env.REDIS_HOST, LOGIN_MAX_ATTEMPT: Number(process.env.LOGIN_MAX_ATTEMPT), LOGIN_BLOCK_TIME: Number(process.env.LOGIN_BLOCK_TIME), OTP_EXPIRY: Number(process.env.OTP_EXPIRY) },
  JWT_AUTH: {
    TOKEN: process.env.JWT_AUTH_SECRET,
    AUTH_EXPIRE_TIME: process.env.JWT_AUTH_EXPIRE,
    REFRESH_TOKEN: process.env.JWT_REFRESH_SECRET,
    REFRESH_EXPIRE_TIME: process.env.JWT_REFRESH_EXPIRE,
    FORGOT_EXPIRE_TIME: process.env.JWT_FORGOT_EXPIRE,
    JWT_2FA_EXPIRE: process.env.JWT_2FA_EXPIRE,
  },
  SENDGRID: { API_KEY: process.env.SENDGRID_API_KEY, SENDER: process.env.SENDER },
  TWILIO: { ACCOUNT_SID: process.env.TWILIO_ACCOUNT_SID, AUTH_TOKEN: process.env.TWILIO_AUTH_TOKEN, PHONE_NUMBER: process.env.TWILIO_PHONE_NUMBER },
  GOOGLE: {
    CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
    PROJECT_ID: process.env.GOOGLE_PROJECT_ID,
    BUCKET_NAME: process.env.BUCKET_NAME,
    UPLOAD_URL: `https://storage.googleapis.com/${process.env.GOOGLE_KEY_FILE_NAME}/`,
    MAX_SIZE: Number(process.env.MAX_SIZE || 5),
  },
  KAFKA: { BROKERS: `${process.env.KAFKA_BROKER}:${process.env.KAFKA_BROKER_PORT}` },
  GRPC: {
    USER_SERVICE_GRPC_CONTAINER_NAME: process.env.USER_SERVICE_GRPC_CONTAINER_NAME || 'localhost',
    USER_SERVICE_GRPC_PORT: process.env.USER_SERVICE_GRPC_PORT || '4001',
    ADMIN_SERVICE_GRPC_CONTAINER_NAME: process.env.ADMIN_SERVICE_GRPC_CONTAINER_NAME || 'localhost',
    ADMIN_SERVICE_GRPC_PORT: process.env.ADMIN_SERVICE_GRPC_PORT || '7001',
    NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME: process.env.NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME || 'localhost',
    NOTIFICATION_SERVICE_GRPC_PORT: process.env.NOTIFICATION_SERVICE_GRPC_PORT || '8001',
    GRPC_SSL: process.env.GRPC_SSL,
  },
  SUMSUB: { SUMSUB_SECRET_KEY: process.env.SUMSUB_SECRET_KEY, SUMSUB_TOKEN: process.env.SUMSUB_TOKEN },
  RPC_URL: process.env.RPC_URL,
  FUND_CONTRACT_ADDRESS: process.env.FUND_CONTRACT_ADDRESS,
  USDC_ADDRESS: process.env.USDC_ADDRESS,
  DOCUSIGN: {
    clientId: process.env.DOCUSIGN_CLIENT_ID ?? '',
    impersonatedUserGuid: process.env.DOCUSIGN_IMPERSONATED_USER_ID ?? '',
    accountId: process.env.DOCUSIGN_ACCOUNT_ID ?? '',
    OAuthServer: process.env.DOCUSIGN_OAUTH_URL ?? '',
    privateKey: process.env.DOCUSIGN_PRIVATE_KEY ?? '',
    frontendUrl: process.env.FRONTEND_URL ?? '',
    webhookAuth: { userName: process.env.DOCUSIGN_WEBHOOK_USERNAME ?? '', pwd: process.env.DOCUSIGN_WEBHOOK_PWD ?? '' },
  },
  AWS: {
    // ACCESS_KEY_ID: process.env.AWS_ACCESS_KEY_ID || '',
    // SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY || '',
    BUCKET_NAME: process.env.AWS_BUCKET_NAME || '',
    REGION: process.env.AWS_REGION || '',
  },
};

/**
 * Staging environment configuration.
 * Pre-production environment for testing with production-like settings.
 *
 * @constant {IConfig} stage - Staging environment settings
 */
const stage: IConfig = {
  SOCKET_PORT: process.env.SOCKET_PORT || '9004',
  CAP_TABLE_URL: process.env.CAP_TABLE_URL,
  ENVIRONMENT: NODE_ENV,
  PROJECT: { NAME: process.env.PROJECT_NAME, LOG_LEVEL: process.env.LOG_LEVEL },
  DATABASE: {
    MONGODB_HOST: process.env.MONGODB_HOST || 'localhost',
    MONGODB_USER: process.env.MONGODB_USER,
    MONGODB_PASSWORD: process.env.MONGODB_PASSWORD,
    MONGODB_PORT: Number(process.env.MONGODB_PORT) || 27017,
    MONGODB_DATABASE: process.env.MONGODB_DATABASE || 'mvp_db',
  },

  REDIS: { HOST: process.env.REDIS_HOST, LOGIN_MAX_ATTEMPT: Number(process.env.LOGIN_MAX_ATTEMPT), LOGIN_BLOCK_TIME: Number(process.env.LOGIN_BLOCK_TIME), OTP_EXPIRY: Number(process.env.OTP_EXPIRY) },
  JWT_AUTH: {
    TOKEN: process.env.JWT_AUTH_SECRET,
    AUTH_EXPIRE_TIME: process.env.JWT_AUTH_EXPIRE,
    REFRESH_TOKEN: process.env.JWT_REFRESH_SECRET,
    REFRESH_EXPIRE_TIME: process.env.JWT_REFRESH_EXPIRE,
    FORGOT_EXPIRE_TIME: process.env.JWT_FORGOT_EXPIRE,
    JWT_2FA_EXPIRE: process.env.JWT_2FA_EXPIRE,
  },
  SENDGRID: { API_KEY: process.env.SENDGRID_API_KEY, SENDER: process.env.SENDER },
  TWILIO: { ACCOUNT_SID: process.env.TWILIO_ACCOUNT_SID, AUTH_TOKEN: process.env.TWILIO_AUTH_TOKEN, PHONE_NUMBER: process.env.TWILIO_PHONE_NUMBER },
  GOOGLE: {
    CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
    PROJECT_ID: process.env.GOOGLE_PROJECT_ID,
    BUCKET_NAME: process.env.BUCKET_NAME,
    UPLOAD_URL: `https://storage.googleapis.com/${process.env.GOOGLE_KEY_FILE_NAME}/`,
    MAX_SIZE: Number(process.env.MAX_SIZE || 5),
  },
  KAFKA: { BROKERS: `${process.env.KAFKA_BROKER}:${process.env.KAFKA_BROKER_PORT}` },
  GRPC: {
    USER_SERVICE_GRPC_CONTAINER_NAME: process.env.USER_SERVICE_GRPC_CONTAINER_NAME || 'localhost',
    USER_SERVICE_GRPC_PORT: process.env.USER_SERVICE_GRPC_PORT || '4001',
    ADMIN_SERVICE_GRPC_CONTAINER_NAME: process.env.ADMIN_SERVICE_GRPC_CONTAINER_NAME || 'localhost',
    ADMIN_SERVICE_GRPC_PORT: process.env.ADMIN_SERVICE_GRPC_PORT || '7001',
    NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME: process.env.NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME || 'localhost',
    NOTIFICATION_SERVICE_GRPC_PORT: process.env.NOTIFICATION_SERVICE_GRPC_PORT || '8001',
    GRPC_SSL: process.env.GRPC_SSL,
  },
  SUMSUB: { SUMSUB_SECRET_KEY: process.env.SUMSUB_SECRET_KEY, SUMSUB_TOKEN: process.env.SUMSUB_TOKEN },
  FUND_CONTRACT_ADDRESS: process.env.FUND_CONTRACT_ADDRESS,
  USDC_ADDRESS: process.env.USDC_ADDRESS,
  RPC_URL: process.env.RPC_URL,
  DOCUSIGN: {
    clientId: process.env.DOCUSIGN_CLIENT_ID ?? '',
    impersonatedUserGuid: process.env.DOCUSIGN_IMPERSONATED_USER_ID ?? '',
    accountId: process.env.DOCUSIGN_ACCOUNT_ID ?? '',
    OAuthServer: process.env.DOCUSIGN_OAUTH_URL ?? '',
    privateKey: process.env.DOCUSIGN_PRIVATE_KEY ?? '',
    frontendUrl: process.env.FRONTEND_URL ?? '',
    webhookAuth: { userName: process.env.DOCUSIGN_WEBHOOK_USERNAME ?? '', pwd: process.env.DOCUSIGN_WEBHOOK_PWD ?? '' },
  },
  AWS: {
    // ACCESS_KEY_ID: process.env.AWS_ACCESS_KEY_ID || '',
    // SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY || '',
    BUCKET_NAME: process.env.AWS_BUCKET_NAME || '',
    REGION: process.env.AWS_REGION || '',
  },
};

/**
 * Quality assurance environment configuration.
 * Testing environment for quality assurance and automated testing.
 *
 * @constant {IConfig} qa - QA environment settings
 */
const qa: IConfig = {
  SOCKET_PORT: process.env.SOCKET_PORT || '9004',
  CAP_TABLE_URL: process.env.CAP_TABLE_URL,
  ENVIRONMENT: NODE_ENV,
  PROJECT: { NAME: process.env.PROJECT_NAME, LOG_LEVEL: process.env.LOG_LEVEL },
  DATABASE: {
    MONGODB_HOST: process.env.MONGODB_HOST || 'localhost',
    MONGODB_USER: process.env.MONGODB_USER,
    MONGODB_PASSWORD: process.env.MONGODB_PASSWORD,
    MONGODB_PORT: Number(process.env.MONGODB_PORT) || 27017,
    MONGODB_DATABASE: process.env.MONGODB_DATABASE || 'mvp_db',
  },

  REDIS: { HOST: process.env.REDIS_HOST, LOGIN_MAX_ATTEMPT: Number(process.env.LOGIN_MAX_ATTEMPT), LOGIN_BLOCK_TIME: Number(process.env.LOGIN_BLOCK_TIME), OTP_EXPIRY: Number(process.env.OTP_EXPIRY) },
  JWT_AUTH: {
    TOKEN: process.env.JWT_AUTH_SECRET,
    AUTH_EXPIRE_TIME: process.env.JWT_AUTH_EXPIRE,
    REFRESH_TOKEN: process.env.JWT_REFRESH_SECRET,
    REFRESH_EXPIRE_TIME: process.env.JWT_REFRESH_EXPIRE,
    FORGOT_EXPIRE_TIME: process.env.JWT_FORGOT_EXPIRE,
    JWT_2FA_EXPIRE: process.env.JWT_2FA_EXPIRE,
  },
  SENDGRID: { API_KEY: process.env.SENDGRID_API_KEY, SENDER: process.env.SENDER },
  TWILIO: { ACCOUNT_SID: process.env.TWILIO_ACCOUNT_SID, AUTH_TOKEN: process.env.TWILIO_AUTH_TOKEN, PHONE_NUMBER: process.env.TWILIO_PHONE_NUMBER },
  GOOGLE: {
    CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
    PROJECT_ID: process.env.GOOGLE_PROJECT_ID,
    BUCKET_NAME: process.env.BUCKET_NAME,
    UPLOAD_URL: `https://storage.googleapis.com/${process.env.GOOGLE_KEY_FILE_NAME}/`,
    MAX_SIZE: Number(process.env.MAX_SIZE || 5),
  },
  KAFKA: { BROKERS: `${process.env.KAFKA_BROKER}:${process.env.KAFKA_BROKER_PORT}` },
  GRPC: {
    USER_SERVICE_GRPC_CONTAINER_NAME: process.env.USER_SERVICE_GRPC_CONTAINER_NAME || 'localhost',
    USER_SERVICE_GRPC_PORT: process.env.USER_SERVICE_GRPC_PORT || '4001',
    ADMIN_SERVICE_GRPC_CONTAINER_NAME: process.env.ADMIN_SERVICE_GRPC_CONTAINER_NAME || 'localhost',
    ADMIN_SERVICE_GRPC_PORT: process.env.ADMIN_SERVICE_GRPC_PORT || '7001',
    NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME: process.env.NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME || 'localhost',
    NOTIFICATION_SERVICE_GRPC_PORT: process.env.NOTIFICATION_SERVICE_GRPC_PORT || '8001',
    GRPC_SSL: process.env.GRPC_SSL,
  },
  SUMSUB: { SUMSUB_SECRET_KEY: process.env.SUMSUB_SECRET_KEY, SUMSUB_TOKEN: process.env.SUMSUB_TOKEN },
  FUND_CONTRACT_ADDRESS: process.env.FUND_CONTRACT_ADDRESS,
  USDC_ADDRESS: process.env.USDC_ADDRESS,
  RPC_URL: process.env.RPC_URL,
  DOCUSIGN: {
    clientId: process.env.DOCUSIGN_CLIENT_ID ?? '',
    impersonatedUserGuid: process.env.DOCUSIGN_IMPERSONATED_USER_ID ?? '',
    accountId: process.env.DOCUSIGN_ACCOUNT_ID ?? '',
    OAuthServer: process.env.DOCUSIGN_OAUTH_URL ?? '',
    privateKey: process.env.DOCUSIGN_PRIVATE_KEY ?? '',
    frontendUrl: process.env.FRONTEND_URL ?? '',
    webhookAuth: { userName: process.env.DOCUSIGN_WEBHOOK_USERNAME ?? '', pwd: process.env.DOCUSIGN_WEBHOOK_PWD ?? '' },
  },
  AWS: {
    // ACCESS_KEY_ID: process.env.AWS_ACCESS_KEY_ID || '',
    // SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY || '',
    BUCKET_NAME: process.env.AWS_BUCKET_NAME || '',
    REGION: process.env.AWS_REGION || '',
  },
};

/**
 * User acceptance testing environment configuration.
 * Final testing environment before production deployment.
 *
 * @constant {IConfig} uat - UAT environment settings
 */
const uat: IConfig = {
  SOCKET_PORT: process.env.SOCKET_PORT || '9004',
  CAP_TABLE_URL: process.env.CAP_TABLE_URL,

  ENVIRONMENT: NODE_ENV,
  PROJECT: { NAME: process.env.PROJECT_NAME, LOG_LEVEL: process.env.LOG_LEVEL },
  DATABASE: {
    MONGODB_HOST: process.env.MONGODB_HOST || 'localhost',
    MONGODB_USER: process.env.MONGODB_USER,
    MONGODB_PASSWORD: process.env.MONGODB_PASSWORD,
    MONGODB_PORT: Number(process.env.MONGODB_PORT) || 27017,
    MONGODB_DATABASE: process.env.MONGODB_DATABASE || 'mvp_db',
  },

  REDIS: { HOST: process.env.REDIS_HOST, LOGIN_MAX_ATTEMPT: Number(process.env.LOGIN_MAX_ATTEMPT), LOGIN_BLOCK_TIME: Number(process.env.LOGIN_BLOCK_TIME), OTP_EXPIRY: Number(process.env.OTP_EXPIRY) },
  JWT_AUTH: {
    TOKEN: process.env.JWT_AUTH_SECRET,
    AUTH_EXPIRE_TIME: process.env.JWT_AUTH_EXPIRE,
    REFRESH_TOKEN: process.env.JWT_REFRESH_SECRET,
    REFRESH_EXPIRE_TIME: process.env.JWT_REFRESH_EXPIRE,
    FORGOT_EXPIRE_TIME: process.env.JWT_FORGOT_EXPIRE,
    JWT_2FA_EXPIRE: process.env.JWT_2FA_EXPIRE,
  },
  SENDGRID: { API_KEY: process.env.SENDGRID_API_KEY, SENDER: process.env.SENDER },
  TWILIO: { ACCOUNT_SID: process.env.TWILIO_ACCOUNT_SID, AUTH_TOKEN: process.env.TWILIO_AUTH_TOKEN, PHONE_NUMBER: process.env.TWILIO_PHONE_NUMBER },
  GOOGLE: {
    CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
    PROJECT_ID: process.env.GOOGLE_PROJECT_ID,
    BUCKET_NAME: process.env.BUCKET_NAME,
    UPLOAD_URL: `https://storage.googleapis.com/${process.env.GOOGLE_KEY_FILE_NAME}/`,
    MAX_SIZE: Number(process.env.MAX_SIZE || 5),
  },
  KAFKA: { BROKERS: `${process.env.KAFKA_BROKER}:${process.env.KAFKA_BROKER_PORT}` },
  GRPC: {
    USER_SERVICE_GRPC_CONTAINER_NAME: process.env.USER_SERVICE_GRPC_CONTAINER_NAME || 'localhost',
    USER_SERVICE_GRPC_PORT: process.env.USER_SERVICE_GRPC_PORT || '4001',
    ADMIN_SERVICE_GRPC_CONTAINER_NAME: process.env.ADMIN_SERVICE_GRPC_CONTAINER_NAME || 'localhost',
    ADMIN_SERVICE_GRPC_PORT: process.env.ADMIN_SERVICE_GRPC_PORT || '7001',
    NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME: process.env.NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME || 'localhost',
    NOTIFICATION_SERVICE_GRPC_PORT: process.env.NOTIFICATION_SERVICE_GRPC_PORT || '8001',
    GRPC_SSL: process.env.GRPC_SSL,
  },
  SUMSUB: { SUMSUB_SECRET_KEY: process.env.SUMSUB_SECRET_KEY, SUMSUB_TOKEN: process.env.SUMSUB_TOKEN },
  FUND_CONTRACT_ADDRESS: process.env.FUND_CONTRACT_ADDRESS,
  USDC_ADDRESS: process.env.USDC_ADDRESS,
  RPC_URL: process.env.RPC_URL,
  DOCUSIGN: {
    clientId: process.env.DOCUSIGN_CLIENT_ID ?? '',
    impersonatedUserGuid: process.env.DOCUSIGN_IMPERSONATED_USER_ID ?? '',
    accountId: process.env.DOCUSIGN_ACCOUNT_ID ?? '',
    OAuthServer: process.env.DOCUSIGN_OAUTH_URL ?? '',
    privateKey: process.env.DOCUSIGN_PRIVATE_KEY ?? '',
    frontendUrl: process.env.FRONTEND_URL ?? '',
    webhookAuth: { userName: process.env.DOCUSIGN_WEBHOOK_USERNAME ?? '', pwd: process.env.DOCUSIGN_WEBHOOK_PWD ?? '' },
  },
  AWS: {
    // ACCESS_KEY_ID: process.env.AWS_ACCESS_KEY_ID || '',
    // SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY || '',
    BUCKET_NAME: process.env.AWS_BUCKET_NAME || '',
    REGION: process.env.AWS_REGION || '',
  },
};

/**
 * Production environment configuration.
 * Optimized for production deployment with enhanced security and performance settings.
 *
 * @constant {IConfig} production - Production environment settings
 */
const production: IConfig = {
  SOCKET_PORT: process.env.SOCKET_PORT || '9004',
  CAP_TABLE_URL: process.env.CAP_TABLE_URL,
  ENVIRONMENT: NODE_ENV,
  PROJECT: { NAME: process.env.PROJECT_NAME, LOG_LEVEL: process.env.LOG_LEVEL },
  DATABASE: {
    MONGODB_HOST: process.env.MONGODB_HOST || 'localhost',
    MONGODB_USER: process.env.MONGODB_USER,
    MONGODB_PASSWORD: process.env.MONGODB_PASSWORD,
    MONGODB_PORT: Number(process.env.MONGODB_PORT) || 27017,
    MONGODB_DATABASE: process.env.MONGODB_DATABASE || 'mvp_db',
  },

  REDIS: { HOST: process.env.REDIS_HOST, LOGIN_MAX_ATTEMPT: Number(process.env.LOGIN_MAX_ATTEMPT), LOGIN_BLOCK_TIME: Number(process.env.LOGIN_BLOCK_TIME), OTP_EXPIRY: Number(process.env.OTP_EXPIRY) },
  JWT_AUTH: {
    TOKEN: process.env.JWT_AUTH_SECRET,
    AUTH_EXPIRE_TIME: process.env.JWT_AUTH_EXPIRE,
    REFRESH_TOKEN: process.env.JWT_REFRESH_SECRET,
    REFRESH_EXPIRE_TIME: process.env.JWT_REFRESH_EXPIRE,
    FORGOT_EXPIRE_TIME: process.env.JWT_FORGOT_EXPIRE,
    JWT_2FA_EXPIRE: process.env.JWT_2FA_EXPIRE,
  },
  SENDGRID: { API_KEY: process.env.SENDGRID_API_KEY, SENDER: process.env.SENDER },
  TWILIO: { ACCOUNT_SID: process.env.TWILIO_ACCOUNT_SID, AUTH_TOKEN: process.env.TWILIO_AUTH_TOKEN, PHONE_NUMBER: process.env.TWILIO_PHONE_NUMBER },
  GOOGLE: {
    CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
    PROJECT_ID: process.env.GOOGLE_PROJECT_ID,
    BUCKET_NAME: process.env.BUCKET_NAME,
    UPLOAD_URL: `https://storage.googleapis.com/${process.env.GOOGLE_KEY_FILE_NAME}/`,
    MAX_SIZE: Number(process.env.MAX_SIZE || 5),
  },
  KAFKA: { BROKERS: `${process.env.KAFKA_BROKER}:${process.env.KAFKA_BROKER_PORT}` },
  GRPC: {
    USER_SERVICE_GRPC_CONTAINER_NAME: process.env.USER_SERVICE_GRPC_CONTAINER_NAME || 'localhost',
    USER_SERVICE_GRPC_PORT: process.env.USER_SERVICE_GRPC_PORT || '4001',
    ADMIN_SERVICE_GRPC_CONTAINER_NAME: process.env.ADMIN_SERVICE_GRPC_CONTAINER_NAME || 'localhost',
    ADMIN_SERVICE_GRPC_PORT: process.env.ADMIN_SERVICE_GRPC_PORT || '7001',
    NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME: process.env.NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME || 'localhost',
    NOTIFICATION_SERVICE_GRPC_PORT: process.env.NOTIFICATION_SERVICE_GRPC_PORT || '8001',
    GRPC_SSL: process.env.GRPC_SSL,
  },
  SUMSUB: { SUMSUB_SECRET_KEY: process.env.SUMSUB_SECRET_KEY, SUMSUB_TOKEN: process.env.SUMSUB_TOKEN },
  FUND_CONTRACT_ADDRESS: process.env.FUND_CONTRACT_ADDRESS,
  USDC_ADDRESS: process.env.USDC_ADDRESS,
  RPC_URL: process.env.RPC_URL,
  DOCUSIGN: {
    clientId: process.env.DOCUSIGN_CLIENT_ID ?? '',
    impersonatedUserGuid: process.env.DOCUSIGN_IMPERSONATED_USER_ID ?? '',
    accountId: process.env.DOCUSIGN_ACCOUNT_ID ?? '',
    OAuthServer: process.env.DOCUSIGN_OAUTH_URL ?? '',
    privateKey: process.env.DOCUSIGN_PRIVATE_KEY ?? '',
    frontendUrl: process.env.FRONTEND_URL ?? '',
    webhookAuth: { userName: process.env.DOCUSIGN_WEBHOOK_USERNAME ?? '', pwd: process.env.DOCUSIGN_WEBHOOK_PWD ?? '' },
  },
  AWS: {
    // ACCESS_KEY_ID: process.env.AWS_ACCESS_KEY_ID || '',
    // SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY || '',
    BUCKET_NAME: process.env.AWS_BUCKET_NAME || '',
    REGION: process.env.AWS_REGION || '',
  },
};

/**
 * Environment configuration map for dynamic configuration selection.
 * Maps environment names to their respective configuration objects.
 *
 * @constant {Object} config - Environment configuration mapping
 * @type {{ [name: string]: IConfig }}
 */
const config: { [name: string]: IConfig } = { development, production, stage, qa, uat };

/**
 * Active configuration object based on current NODE_ENV.
 * Automatically selects the appropriate configuration for the current environment.
 *
 * @exports {IConfig} CONFIG - Current environment configuration
 *
 * // Access configuration values
 * const dbHost = CONFIG.DATABASE.MONGODB_HOST;
 * const jwtSecret = CONFIG.JWT_AUTH.TOKEN;
 * const environment = CONFIG.ENVIRONMENT;
 *
 * // Configuration is type-safe and environment-specific
 * console.log(`Running in ${CONFIG.ENVIRONMENT} mode`);
 * ```
 */
export default config[NODE_ENV];
