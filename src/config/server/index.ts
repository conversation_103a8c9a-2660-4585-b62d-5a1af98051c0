import * as http from 'http';
import * as serverHandlers from './serverHandlers';
import server from './server';

/**
 * Creates an HTTP server instance using the configured Express application.
 * This server will handle all incoming HTTP requests and route them through
 * the Express middleware and route handlers.
 *
 * @constant {http.Server} Server - The HTTP server instance
 */
const Server: http.Server = http.createServer(server);

/**
 * Attach event listeners to the server
 *
 * @description
 * This section binds event handlers to the server for handling specific events such as errors and listening state.
 * The error handler manages server startup errors and port conflicts, while the listening handler
 * logs successful server startup information.
 */

/**
 * Handles server error events such as port conflicts and permission issues.
 *
 * @event Server#error
 * @param {Error} error - The error object containing details about the server error
 * @listens Server#error
 */
Server.on('error', (error: Error) => serverHandlers.onError(error, server.get('port')));

/**
 * Handles the server listening event when the server successfully starts.
 * Logs server startup information and confirms the server is ready to accept connections.
 *
 * @event Server#listening
 * @listens Server#listening
 */
Server.on('listening', serverHandlers.onListening.bind(Server));
