/* eslint-disable no-undef */
import { Address } from 'cluster';
import logger from '../../helpers/logging/logger.helper';

/**
 * Handles server startup errors such as port conflicts and permission issues.
 * Provides specific error handling for common server startup problems and
 * gracefully exits the process with appropriate error messages.
 *
 * @export
 * @function onError
 * @param {NodeJS.ErrnoException} error - The error object containing system call error details
 * @param {number | string | boolean} port - The port number, named pipe, or boolean flag where the error occurred
 * @returns {void} - This function either throws the error or exits the process
 * @throws {NodeJS.ErrnoException} Re-throws the error if it's not a 'listen' syscall error
 *
 * @description
 * Error handling scenarios:
 * - EACCES: Port requires elevated privileges (typically ports < 1024)
 * - EADDRINUSE: Port is already in use by another process
 * - Other errors: Re-throws the original error for handling upstream
 *
 */
// eslint-disable-next-line no-undef
export function onError(error: NodeJS.ErrnoException, port: number | string | boolean): void {
  if (error.syscall !== 'listen') {
    throw error;
  }

  const bind: string = typeof port === 'string' ? `Pipe ${port}` : `Port ${port}`;

  switch (error.code) {
    case 'EACCES':
      logger.error(`${bind} requires elevated privileges`);
      process.exit(1);
      break;

    case 'EADDRINUSE':
      logger.error(`${bind} is already in use`);
      process.exit(1);
      break;

    default:
      throw error;
  }
}

/**
 * Handles the server listening event when the server successfully starts.
 * Logs server startup information including the address and port where
 * the server is listening for incoming connections.
 *
 * @export
 * @function onListening
 * @returns {void}
 * @this {import('http').Server} The HTTP server instance that is listening
 *
 * @description
 * This function is bound to the server context and uses 'this.address()'
 * to get the actual address where the server is listening. It handles both
 * named pipes and network ports for logging purposes.
 *
 */
export function onListening(): void {
  const addr: Address = this.address();
  const bind: string = typeof addr === 'string' ? `pipe ${addr}` : `port ${addr.port}`;

  logger.info(`Server started & Listening on ${bind}!`);
}
