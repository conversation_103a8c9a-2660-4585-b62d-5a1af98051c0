import * as express from 'express';
import * as path from 'path';
import * as Middleware from '../middleware/middleware';
import * as Routes from '../../routes';
import RedisHelper from '../../helpers/redis.helper';
import Db from '../connection/connection';
import logger from '../../helpers/logging/logger.helper';
import GrpcServer from '../../_grpc';
import CONFIG from '../env';
import { Server } from 'socket.io';
import { initializeSocket } from '../../helpers/socket.helper'; // Import the singleton getter
import cronHandler from '../../service/cronHandler';

/**
 * Express application instance configured with all middleware, routes, and static file serving.
 * This serves as the main application server for the Valuit tokenization platform.
 *
 * @constant {express.Application} app - The main Express application instance
 */
const app: express.Application = express();

/**
 * Configure static file serving for public assets.
 * Serves files from the public directory for images, stylesheets, and other static content.
 *
 * @static
 * @path {string} /public - The public route path
 * @directory {string} ../../public - The directory containing static files
 */
app.use('/public', express.static(path.join(__dirname, '../../public')));

/**
 * Configure Express application middleware including security, parsing, and compression.
 * Sets up body parsing, CORS, helmet security headers, compression, and custom error handling.
 *
 * @function
 * @param {express.Application} app - The Express application instance to configure
 */
Middleware.configure(app);

/**
 * Initialize all application routes including authentication, business logic, and API endpoints.
 * Sets up versioned API routes under /v1 prefix and configures all service-specific route handlers.
 *
 * @function
 * @param {express.Application} app - The Express application instance to add routes to
 */
Routes.init(app);

/**
 * Initialize the global error handler middleware.
 * Catches and processes all unhandled errors throughout the application lifecycle.
 *
 * @function
 * @param {express.Application} app - The Express application instance to add error handling to
 */
Middleware.initErrorHandler(app);

/**
 * Starts the server and initializes all required services including database, Redis, gRPC, and Socket.IO.
 * This function orchestrates the complete application startup sequence with proper error handling.
 *
 * @async
 * @function startServer
 * @returns {Promise<void>} Promise that resolves when server startup is complete
 * @throws {Error} Throws error if any critical service fails to start
 *
 * @description
 * Startup sequence:
 * 1. Establishes MongoDB database connection
 * 2. Configures Express server port
 * 3. Starts gRPC server for inter-service communication
 * 4. Starts HTTP server for REST API
 * 5. Connects to Redis for caching and session storage
 * 6. Initializes Socket.IO for real-time communication
 * 7. Starts cron scheduler for background tasks
 */
const startServer = async () => {
  try {
    // Establish database connection
    await Db.dbConnection();

    // Sets port 3000 to default or unless otherwise specified in the environment
    app.set('port', process.env.PORT || 3000);
    const port = app.get('port');

    // Start gRPC Server
    const grpcServer = new GrpcServer(CONFIG.GRPC.USER_SERVICE_GRPC_CONTAINER_NAME.toString(), CONFIG.GRPC.USER_SERVICE_GRPC_PORT.toString());
    grpcServer.start();

    // Start Express server
    app.listen(port, () => {
      logger.info(`Marketplace Service Server is running on http://localhost:${port}`);
    });

    // Connect to Redis with a slight delay
    setTimeout(() => {
      RedisHelper.connectRedis();
    }, 200); // 200 milliseconds

    // Connect to socket with delay
    setTimeout(() => {
      const port = Number(CONFIG.SOCKET_PORT);
      // Initializing socket globally
      const io = new Server(port, { cors: { origin: '*' } });
      initializeSocket(io);
    }, 300); // 300 milliseconds

    // save data at record date
    cronHandler.cronScheduler();
  } catch (error) {
    logger.error(error, 'Failed to start the server');
  }
};

/**
 * Start the server immediately when this module is loaded.
 * Initiates the complete application startup sequence.
 */
startServer();

/**
 * Handles graceful shutdown on SIGINT signal (Ctrl+C).
 * Ensures proper cleanup of database connections before process termination.
 *
 * @event process#SIGINT
 * @async
 * @function
 * @returns {Promise<void>} Promise that resolves when cleanup is complete
 */
process.on('SIGINT', async () => {
  await Db.dbDisconnect();
  process.exit(0);
});

/**
 * Handles graceful shutdown on SIGTERM signal (process termination).
 * Ensures proper cleanup of database connections before process termination.
 *
 * @event process#SIGTERM
 * @async
 * @function
 * @returns {Promise<void>} Promise that resolves when cleanup is complete
 */
process.on('SIGTERM', async () => {
  await Db.dbDisconnect();
  process.exit(0);
});

/**
 * Export the configured Express application instance.
 * Used for testing and integration with other modules.
 *
 * @exports {express.Application} app - The fully configured Express application
 */
export default app;
