export const swaggerDefinition: any = {
  openapi: '3.0.0',
  info: {
    title: 'Valuit Tokenization Platform API',
    description: `
      **Comprehensive API for Enterprise Asset Tokenization Platform**
      
      The Valuit Tokenization Platform provides a complete suite of APIs for:
      
      🏢 **Asset Tokenization**: Convert real estate, equity, and other assets into blockchain tokens
      👤 **Identity Management**: KYC/KYB verification, user profiles, and compliance
      💼 **Investment Operations**: Order management, portfolio tracking, and performance analytics  
      🗳️ **Governance**: Proposal creation, voting systems, and stakeholder management
      💰 **Financial Services**: Dividend distributions, redemptions, and transfer management
      📋 **Compliance**: Document signing, representative management, and audit trails
      🔔 **Notifications**: Real-time updates and communication systems
      
      **Key Features:**
      - Regulatory compliant tokenization workflows
      - Multi-signature wallet support and security
      - Comprehensive audit trails and reporting
      - Real-time blockchain transaction monitoring
      - Integration with DocuSign for legal compliance
      - Advanced portfolio analytics and performance tracking
      
      **Security & Authentication:**
      All endpoints require JWT bearer token authentication unless specified.
      Rate limiting is enforced to prevent abuse.
      
      **Data Format:**
      - All timestamps are in ISO 8601 format (UTC)
      - Monetary amounts are in USD unless specified
      - Blockchain addresses follow Ethereum format
      - File uploads support JPG, PNG, and PDF formats
      
      **Error Handling:**
      Standard HTTP status codes with detailed error messages and request IDs for support.
    `,
    version: '2.0.0',
    contact: {
      name: 'Valuit Platform Support',
      email: '<EMAIL>',
      url: 'https://valuit.com/support',
    },
    license: {
      name: 'Proprietary License',
      url: 'https://valuit.com/terms/api-license',
    },
    termsOfService: 'https://valuit.com/terms-of-service',
  },
  servers: [
    {
      url: 'https://api.valuit.com',
      description: 'Production API Server',
    },
    {
      url: 'https://api-staging.valuit.com',
      description: 'Staging API Server',
    },
    {
      url: 'http://localhost:4000',
      description: 'Local Development Server',
    },
  ],
  externalDocs: {
    description: 'Complete API Documentation',
    url: 'https://docs.valuit.com/api',
  },

  tags: [
    {
      name: 'Health',
      description: 'API health monitoring and service status endpoints',
      externalDocs: {
        description: 'Health Check Documentation',
        url: 'https://docs.valuit.com/api/health',
      },
    },
    {
      name: 'Authentication',
      description: 'User authentication, registration, and session management',
      externalDocs: {
        description: 'Authentication Guide',
        url: 'https://docs.valuit.com/api/auth',
      },
    },
    {
      name: 'User Management',
      description: 'User profile management, 2FA setup, and account settings',
      externalDocs: {
        description: 'User Management Guide',
        url: 'https://docs.valuit.com/api/users',
      },
    },
    {
      name: 'KYC/KYB',
      description: 'Know Your Customer and Know Your Business verification workflows',
      externalDocs: {
        description: 'KYC/KYB Integration Guide',
        url: 'https://docs.valuit.com/api/kyc',
      },
    },
    {
      name: 'Offerings',
      description: 'Investment offering creation, management, and marketplace operations',
      externalDocs: {
        description: 'Offerings API Guide',
        url: 'https://docs.valuit.com/api/offerings',
      },
    },
    {
      name: 'Orders',
      description: 'Investment order management, portfolio tracking, and performance analytics',
      externalDocs: {
        description: 'Orders and Portfolio Guide',
        url: 'https://docs.valuit.com/api/orders',
      },
    },
    {
      name: 'Dividends',
      description: 'Dividend distribution management, calculations, and investor reporting',
      externalDocs: {
        description: 'Dividend Management Guide',
        url: 'https://docs.valuit.com/api/dividends',
      },
    },
    {
      name: 'Proposals',
      description: 'Governance proposals, voting systems, and stakeholder management',
      externalDocs: {
        description: 'Governance API Guide',
        url: 'https://docs.valuit.com/api/governance',
      },
    },
    {
      name: 'Transfers',
      description: 'Token transfer management, approval workflows, and compliance',
      externalDocs: {
        description: 'Transfer Management Guide',
        url: 'https://docs.valuit.com/api/transfers',
      },
    },
    {
      name: 'Redemptions',
      description: 'Token redemption requests, processing, and liquidity management',
      externalDocs: {
        description: 'Redemption Guide',
        url: 'https://docs.valuit.com/api/redemptions',
      },
    },
    {
      name: 'DocuSign',
      description: 'Digital document signing integration and workflow management',
      externalDocs: {
        description: 'DocuSign Integration Guide',
        url: 'https://docs.valuit.com/api/docusign',
      },
    },
    {
      name: 'Notifications',
      description: 'User notification management, delivery, and preferences',
      externalDocs: {
        description: 'Notifications API Guide',
        url: 'https://docs.valuit.com/api/notifications',
      },
    },
    {
      name: 'Representatives',
      description: 'Representative delegation, multisig management, and authorization',
      externalDocs: {
        description: 'Representative Management Guide',
        url: 'https://docs.valuit.com/api/representatives',
      },
    },
    {
      name: 'Transactions',
      description: 'Blockchain transaction history, monitoring, and analytics',
      externalDocs: {
        description: 'Transaction Analytics Guide',
        url: 'https://docs.valuit.com/api/transactions',
      },
    },
  ],
  paths: {
    '/health-check': {
      get: {
        summary: 'API Health Check',
        tags: ['Health'],
        description: 'Endpoint to verify API availability and service status',
        responses: {
          200: {
            description: 'API is healthy and operational',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string', example: 'User service is up and running' },
                    status: { type: 'integer', example: 200 },
                    data: {
                      type: 'object',
                      properties: {
                        timestamp: { type: 'string', format: 'date-time', example: '2024-01-15T10:26:22.270Z' },
                      },
                    },
                    error: { type: 'boolean', example: false },
                  },
                },
              },
            },
          },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/signup': {
      post: {
        summary: 'User Registration',
        tags: ['Authentication'],
        description: 'Register a new user account for investor or institution type users',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/SignupRequest' },
              examples: {
                investor: {
                  summary: 'Investor Registration',
                  value: {
                    name: 'John Doe',
                    email: '<EMAIL>',
                    password: 'SecurePass123!',
                    countryCode: '+1',
                    mobile: '**********',
                    userType: 'investor',
                    otpMethods: 'Email',
                  },
                },
                institution: {
                  summary: 'Institution Registration',
                  value: {
                    name: 'TechCorp LLC',
                    email: '<EMAIL>',
                    password: 'SecurePass123!',
                    countryCode: '+1',
                    mobile: '**********',
                    userType: 'institution',
                    legalFullName: 'TechCorp LLC',
                    otpMethods: 'Email',
                  },
                },
              },
            },
          },
        },
        responses: {
          201: { $ref: '#/components/responses/SignupSuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          409: { $ref: '#/components/responses/ConflictResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/login': {
      post: {
        summary: 'User Login',
        tags: ['Authentication'],
        description: 'Authenticate user with email/username and password',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/LoginRequest' },
              examples: {
                emailLogin: {
                  summary: 'Email Login',
                  value: {
                    userName: '<EMAIL>',
                    password: 'SecurePass123!',
                  },
                },
              },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/LoginSuccessResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          429: { $ref: '#/components/responses/TooManyRequestsResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/social-login': {
      post: {
        summary: 'Social Media Login',
        tags: ['Authentication'],
        description: 'Authenticate user using social media OAuth tokens (Google, Facebook, etc.)',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/SocialLoginRequest' },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/LoginSuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/verify': {
      post: {
        summary: 'OTP Verification',
        tags: ['Authentication'],
        description: 'Verify OTP for signup, login, or password reset processes',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/VerifyOTPRequest' },
              examples: {
                signupVerification: {
                  summary: 'Signup OTP Verification',
                  value: {
                    otp: '123456',
                    email: '<EMAIL>',
                    type: 'sign_up',
                    otpMethods: 'Email',
                  },
                },
                loginVerification: {
                  summary: 'Login OTP Verification',
                  value: {
                    otp: '123456',
                    email: '<EMAIL>',
                    type: 'login',
                    otpMethods: 'Email',
                  },
                },
                forgotPasswordVerification: {
                  summary: 'Password Reset OTP Verification',
                  value: {
                    otp: '123456',
                    email: '<EMAIL>',
                    type: 'forgot',
                    otpMethods: 'Email',
                  },
                },
              },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/VerifySuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          429: { $ref: '#/components/responses/TooManyRequestsResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/resend-otp': {
      post: {
        summary: 'Resend OTP',
        tags: ['Authentication'],
        description: 'Resend OTP code to user email or phone',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/ResendOTPRequest' },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          429: { $ref: '#/components/responses/TooManyRequestsResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/forgot-password': {
      post: {
        summary: 'Forgot Password',
        tags: ['Authentication'],
        description: 'Initiate password reset process by sending OTP to user email',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/ForgotPasswordRequest' },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          404: { $ref: '#/components/responses/NotFoundResponse' },
          429: { $ref: '#/components/responses/TooManyRequestsResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/reset-password': {
      post: {
        summary: 'Reset Password',
        tags: ['Authentication'],
        description: 'Reset user password using verification token',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/ResetPasswordRequest' },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/verify-2fa': {
      post: {
        summary: 'Verify 2FA for Login',
        tags: ['Authentication'],
        description: 'Verify two-factor authentication code during login process',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/VerifyLogin2FARequest' },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/LoginSuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/forgot-2fa': {
      patch: {
        summary: 'Reset 2FA Settings',
        tags: ['Authentication'],
        description: 'Reset two-factor authentication settings using verification token',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/Reset2FARequest' },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/Reset2FAResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/getToken': {
      post: {
        summary: 'Get KYC Token',
        tags: ['KYC/KYB'],
        description: 'Generate access token for KYC/KYB verification services',
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/kyc-webhook': {
      post: {
        summary: 'KYC Webhook',
        tags: ['KYC/KYB'],
        description: 'Webhook endpoint for receiving KYC/KYB verification updates from third-party providers',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                description: 'Webhook payload from KYC provider',
              },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/profile': {
      get: {
        summary: 'Get User Profile',
        tags: ['User Management'],
        description: "Retrieve the authenticated user's profile information",
        security: [{ bearerAuth: [] }],
        responses: {
          200: { $ref: '#/components/responses/GetProfileResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
      put: {
        summary: 'Update User Profile',
        tags: ['User Management'],
        description: "Update the authenticated user's profile information",
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'multipart/form-data': {
              schema: { $ref: '#/components/schemas/UpdateProfileRequest' },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/profile/{id}': {
      get: {
        summary: 'Get User Profile by ID',
        tags: ['User Management'],
        description: "Retrieve another user's profile information by their ID",
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            schema: {
              type: 'string',
              pattern: '^[0-9a-fA-F]{24}$',
              example: '507f1f77bcf86cd799439011',
            },
            description: 'MongoDB ObjectId of the user',
          },
        ],
        responses: {
          200: { $ref: '#/components/responses/GetProfileResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          404: { $ref: '#/components/responses/NotFoundResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/kyc': {
      put: {
        summary: 'Submit KYC/KYB Data',
        tags: ['KYC/KYB'],
        description: 'Submit KYC data for investors or KYB data for institutions through multi-step process',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                oneOf: [
                  { $ref: '#/components/schemas/InvestorKYCStep1' },
                  { $ref: '#/components/schemas/InvestorKYCStep2' },
                  { $ref: '#/components/schemas/InvestorKYCStep3' },
                  { $ref: '#/components/schemas/InvestorKYCStep4' },
                  { $ref: '#/components/schemas/InvestorKYCStep5' },
                  { $ref: '#/components/schemas/InstitutionKYBStep1' },
                  { $ref: '#/components/schemas/InstitutionKYBStep2' },
                  { $ref: '#/components/schemas/InstitutionKYBStep3' },
                  { $ref: '#/components/schemas/InstitutionKYBStep4' },
                  { $ref: '#/components/schemas/InstitutionKYBStep5' },
                  { $ref: '#/components/schemas/InstitutionKYBStep6' },
                  { $ref: '#/components/schemas/InstitutionKYBStep7' },
                  { $ref: '#/components/schemas/InstitutionKYBStep8' },
                ],
              },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/KYCSuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/upload-docs': {
      post: {
        summary: 'Upload Documents',
        tags: ['User Management'],
        description: 'Upload user documents for KYC/KYB verification',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'multipart/form-data': {
              schema: { $ref: '#/components/schemas/UploadDocsRequest' },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/UploadDocsResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          413: {
            description: 'File too large',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string', example: 'File size exceeds limit' },
                    status: { type: 'integer', example: 413 },
                    error: { type: 'boolean', example: true },
                  },
                },
              },
            },
          },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/enable-2fa': {
      get: {
        summary: 'Enable Two-Factor Authentication',
        tags: ['User Management'],
        description: 'Generate QR code and secret for enabling 2FA on user account',
        security: [{ bearerAuth: [] }],
        responses: {
          200: { $ref: '#/components/responses/Enable2FAResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/verify-2fa': {
      post: {
        summary: 'Verify Two-Factor Authentication',
        tags: ['User Management'],
        description: 'Verify 2FA token to complete 2FA setup',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/Verify2FARequest' },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/log-out': {
      get: {
        summary: 'User Logout',
        tags: ['User Management'],
        description: 'Log out the authenticated user and invalidate session',
        security: [{ bearerAuth: [] }],
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/portfolio': {
      get: {
        summary: 'Get User Portfolio',
        tags: ['User Management'],
        description: "Retrieve user's investment portfolio with holdings and performance",
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'page',
            in: 'query',
            required: false,
            schema: { type: 'integer', minimum: 1, default: 1 },
            description: 'Page number for pagination',
          },
          {
            name: 'limit',
            in: 'query',
            required: false,
            schema: { type: 'integer', minimum: 1, maximum: 100, default: 10 },
            description: 'Number of items per page',
          },
        ],
        responses: {
          200: { $ref: '#/components/responses/PortfolioResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/change-password': {
      patch: {
        summary: 'Change Password',
        tags: ['User Management'],
        description: 'Change user password',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/ChangePasswordRequest' },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/disable-2fa': {
      patch: {
        summary: 'Disable Two-Factor Authentication',
        tags: ['User Management'],
        description: 'Disable 2FA for user account',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/Verify2FARequest' },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/become': {
      patch: {
        summary: 'Become Issuer',
        tags: ['User Management'],
        description: 'Request to become an issuer (ability to create offerings)',
        security: [{ bearerAuth: [] }],
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/disable-otp': {
      post: {
        summary: 'Disable OTP',
        tags: ['User Management'],
        description: 'Disable OTP authentication for user account',
        security: [{ bearerAuth: [] }],
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/send-verification-otp': {
      post: {
        summary: 'Send Verification OTP',
        tags: ['User Management'],
        description: 'Send verification OTP to user',
        security: [{ bearerAuth: [] }],
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/transferagent': {
      get: {
        summary: 'Get Transfer Agents List',
        tags: ['User Management'],
        description: 'Retrieve list of available transfer agents',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'page',
            in: 'query',
            required: false,
            schema: { type: 'integer', minimum: 1, default: 1 },
            description: 'Page number for pagination',
          },
          {
            name: 'limit',
            in: 'query',
            required: false,
            schema: { type: 'integer', minimum: 1, maximum: 100, default: 10 },
            description: 'Number of items per page',
          },
        ],
        responses: {
          200: { $ref: '#/components/responses/TransferAgentListResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/transferagent/{transferAgentId}': {
      get: {
        summary: 'Get Transfer Agent Details',
        tags: ['User Management'],
        description: 'Retrieve details of a specific transfer agent by ID',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'transferAgentId',
            in: 'path',
            required: true,
            schema: {
              type: 'string',
              pattern: '^[0-9a-fA-F]{24}$',
              example: '507f1f77bcf86cd799439011',
            },
            description: 'MongoDB ObjectId of the transfer agent',
          },
        ],
        responses: {
          200: { $ref: '#/components/responses/TransferAgentDetailsResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          404: { $ref: '#/components/responses/NotFoundResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/order/create': {
      post: {
        summary: 'Create Order',
        tags: ['Orders'],
        description: 'Create a new investment order for an offering',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/CreateOrderRequest' },
            },
          },
        },
        responses: {
          201: { $ref: '#/components/responses/CreateOrderResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/order': {
      get: {
        summary: 'Get User Orders',
        tags: ['Orders'],
        description: 'Retrieve all orders for the authenticated user',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'page',
            in: 'query',
            required: false,
            schema: { type: 'integer', minimum: 1, default: 1 },
            description: 'Page number for pagination',
          },
          {
            name: 'limit',
            in: 'query',
            required: false,
            schema: { type: 'integer', minimum: 1, maximum: 100, default: 10 },
            description: 'Number of items per page',
          },
          {
            name: 'status',
            in: 'query',
            required: false,
            schema: { type: 'string', enum: ['pending', 'approved', 'rejected', 'cancelled'] },
            description: 'Filter orders by status',
          },
        ],
        responses: {
          200: { $ref: '#/components/responses/GetOrdersResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/order/csv': {
      get: {
        summary: 'Export Orders to CSV',
        tags: ['Orders'],
        description: 'Export user orders to CSV format',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'startDate',
            in: 'query',
            required: false,
            schema: { type: 'string', format: 'date' },
            description: 'Start date for filtering orders',
          },
          {
            name: 'endDate',
            in: 'query',
            required: false,
            schema: { type: 'string', format: 'date' },
            description: 'End date for filtering orders',
          },
        ],
        responses: {
          200: {
            description: 'CSV file with orders data',
            content: {
              'text/csv': {
                schema: {
                  type: 'string',
                  format: 'binary',
                },
              },
            },
          },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/order/topHoldings': {
      get: {
        summary: 'Get Top Holdings',
        tags: ['Orders'],
        description: "Retrieve user's top holdings by value",
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'limit',
            in: 'query',
            required: false,
            schema: { type: 'integer', minimum: 1, maximum: 20, default: 5 },
            description: 'Number of top holdings to retrieve',
          },
        ],
        responses: {
          200: { $ref: '#/components/responses/TopHoldingsResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/order/portfolio-report': {
      get: {
        summary: 'Get Portfolio Performance Report',
        tags: ['Orders'],
        description: 'Retrieve detailed portfolio performance analytics',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'period',
            in: 'query',
            required: false,
            schema: { type: 'string', enum: ['1D', '7D', '1M', '3M', '6M', '1Y', 'ALL'], default: '1M' },
            description: 'Time period for performance analysis',
          },
        ],
        responses: {
          200: { $ref: '#/components/responses/PortfolioReportResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/order/{orderId}': {
      get: {
        summary: 'Get Order by ID',
        tags: ['Orders'],
        description: 'Retrieve details of a specific order by ID',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'orderId',
            in: 'path',
            required: true,
            schema: {
              type: 'string',
              pattern: '^[0-9a-fA-F]{24}$',
              example: '507f1f77bcf86cd799439011',
            },
            description: 'MongoDB ObjectId of the order',
          },
        ],
        responses: {
          200: { $ref: '#/components/responses/GetOrderResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          404: { $ref: '#/components/responses/NotFoundResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/order/reject': {
      put: {
        summary: 'Reject Order',
        tags: ['Orders'],
        description: 'Reject an investment order (issuer only)',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/RejectOrderRequest' },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          403: { $ref: '#/components/responses/ForbiddenResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/dividend': {
      post: {
        summary: 'Create Dividend Request',
        tags: ['Dividends'],
        description: 'Create a new dividend distribution request (issuer only)',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'multipart/form-data': {
              schema: { $ref: '#/components/schemas/CreateDividendRequest' },
            },
          },
        },
        responses: {
          201: { $ref: '#/components/responses/GenericSuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          403: { $ref: '#/components/responses/ForbiddenResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
      get: {
        summary: 'Get Dividend Requests',
        tags: ['Dividends'],
        description: 'Retrieve all dividend requests (issuer only)',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'page',
            in: 'query',
            required: false,
            schema: { type: 'integer', minimum: 1, default: 1 },
            description: 'Page number for pagination',
          },
          {
            name: 'limit',
            in: 'query',
            required: false,
            schema: { type: 'integer', minimum: 1, maximum: 100, default: 10 },
            description: 'Number of items per page',
          },
          {
            name: 'offeringId',
            in: 'query',
            required: false,
            schema: { type: 'string', pattern: '^[0-9a-fA-F]{24}$' },
            description: 'Filter by offering ID',
          },
        ],
        responses: {
          200: { $ref: '#/components/responses/GetDividendsResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          403: { $ref: '#/components/responses/ForbiddenResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/dividend/investors': {
      get: {
        summary: 'Get Dividend Investors',
        tags: ['Dividends'],
        description: 'Retrieve investors eligible for dividend distribution (issuer only)',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'offeringId',
            in: 'query',
            required: true,
            schema: { type: 'string', pattern: '^[0-9a-fA-F]{24}$' },
            description: 'Offering ID to get investors for',
          },
        ],
        responses: {
          200: { $ref: '#/components/responses/DividendInvestorsResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          403: { $ref: '#/components/responses/ForbiddenResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/dividend/calculate': {
      post: {
        summary: 'Calculate Dividends',
        tags: ['Dividends'],
        description: 'Calculate dividend amounts for investors (issuer only)',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/CalculateDividendRequest' },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/CalculateDividendResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          403: { $ref: '#/components/responses/ForbiddenResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/dividend/investor-history': {
      get: {
        summary: 'Get Investor Dividend History',
        tags: ['Dividends'],
        description: 'Retrieve dividend history for the authenticated investor',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'page',
            in: 'query',
            required: false,
            schema: { type: 'integer', minimum: 1, default: 1 },
            description: 'Page number for pagination',
          },
          {
            name: 'limit',
            in: 'query',
            required: false,
            schema: { type: 'integer', minimum: 1, maximum: 100, default: 10 },
            description: 'Number of items per page',
          },
          {
            name: 'offeringId',
            in: 'query',
            required: false,
            schema: { type: 'string', pattern: '^[0-9a-fA-F]{24}$' },
            description: 'Filter by offering ID',
          },
        ],
        responses: {
          200: { $ref: '#/components/responses/InvestorDividendHistoryResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/proposal': {
      post: {
        summary: 'Create Proposal',
        tags: ['Proposals'],
        description: 'Create a new governance proposal',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'multipart/form-data': {
              schema: { $ref: '#/components/schemas/CreateProposalRequest' },
            },
          },
        },
        responses: {
          201: { $ref: '#/components/responses/GenericSuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/proposal/issuer': {
      get: {
        summary: 'Get Issuer Proposals',
        tags: ['Proposals'],
        description: 'Retrieve proposals created by the issuer',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'page',
            in: 'query',
            required: false,
            schema: { type: 'integer', minimum: 1, default: 1 },
            description: 'Page number for pagination',
          },
          {
            name: 'limit',
            in: 'query',
            required: false,
            schema: { type: 'integer', minimum: 1, maximum: 100, default: 10 },
            description: 'Number of items per page',
          },
        ],
        responses: {
          200: { $ref: '#/components/responses/GetProposalsResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/proposal/details': {
      get: {
        summary: 'Get Proposal Details',
        tags: ['Proposals'],
        description: 'Retrieve detailed information about a specific proposal',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'proposalId',
            in: 'query',
            required: true,
            schema: { type: 'string', pattern: '^[0-9a-fA-F]{24}$' },
            description: 'ID of the proposal to retrieve',
          },
        ],
        responses: {
          200: { $ref: '#/components/responses/GetProposalDetailsResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          404: { $ref: '#/components/responses/NotFoundResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/proposal/set-proposal-cap': {
      post: {
        summary: 'Set Proposal Investor Cap',
        tags: ['Proposals'],
        description: 'Set the minimum investment threshold for voting on proposals',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/SetProposalCapRequest' },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/proposal/investor': {
      get: {
        summary: 'Get Investor Proposals',
        tags: ['Proposals'],
        description: 'Retrieve proposals available for investor voting',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'page',
            in: 'query',
            required: false,
            schema: { type: 'integer', minimum: 1, default: 1 },
            description: 'Page number for pagination',
          },
          {
            name: 'limit',
            in: 'query',
            required: false,
            schema: { type: 'integer', minimum: 1, maximum: 100, default: 10 },
            description: 'Number of items per page',
          },
        ],
        responses: {
          200: { $ref: '#/components/responses/GetProposalsResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/proposal/investor/vote': {
      post: {
        summary: 'Vote on Proposal',
        tags: ['Proposals'],
        description: 'Submit a vote on a governance proposal (investor only)',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/VoteProposalRequest' },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          403: { $ref: '#/components/responses/ForbiddenResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/proposal/whitelisted-offerings': {
      get: {
        summary: 'Get Whitelisted Offerings for Proposals',
        tags: ['Proposals'],
        description: 'Retrieve offerings that the investor is whitelisted for proposal voting',
        security: [{ bearerAuth: [] }],
        responses: {
          200: { $ref: '#/components/responses/WhitelistedOfferingsResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/offering': {
      post: {
        summary: 'Create Offering',
        tags: ['Offerings'],
        description: 'Create a new investment offering (issuer only)',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/CreateOfferingRequest' },
            },
          },
        },
        responses: {
          201: { $ref: '#/components/responses/GenericSuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          403: { $ref: '#/components/responses/ForbiddenResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
      get: {
        summary: 'Get User Offerings',
        tags: ['Offerings'],
        description: 'Retrieve list of offerings for the authenticated user',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'page',
            in: 'query',
            required: false,
            schema: { type: 'integer', minimum: 1, default: 1 },
            description: 'Page number for pagination',
          },
          {
            name: 'limit',
            in: 'query',
            required: false,
            schema: { type: 'integer', minimum: 1, maximum: 100, default: 10 },
            description: 'Number of items per page',
          },
        ],
        responses: {
          200: { $ref: '#/components/responses/GetOfferingsResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/offering/update-offering': {
      patch: {
        summary: 'Update Offering',
        tags: ['Offerings'],
        description: 'Update an existing offering (issuer only)',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/UpdateOfferingRequest' },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          403: { $ref: '#/components/responses/ForbiddenResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/offering/nav-history/{id}': {
      get: {
        summary: 'Get NAV History',
        tags: ['Offerings'],
        description: 'Retrieve price history for an offering',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            schema: { type: 'string', pattern: '^[0-9a-fA-F]{24}$' },
            description: 'Offering ID',
          },
        ],
        responses: {
          200: { $ref: '#/components/responses/NAVHistoryResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          404: { $ref: '#/components/responses/NotFoundResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/offering/primary-market-offerings': {
      get: {
        summary: 'Get Primary Market Offerings',
        tags: ['Offerings'],
        description: 'Retrieve available primary market offerings',
        security: [{ bearerAuth: [] }],
        responses: {
          200: { $ref: '#/components/responses/GetOfferingsResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/offering/request-for-Offering': {
      post: {
        summary: 'Request Offering Addition',
        tags: ['Offerings'],
        description: 'Request for an offering to be added to the platform (issuer only)',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/RequestOfferingRequest' },
            },
          },
        },
        responses: {
          201: { $ref: '#/components/responses/GenericSuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          403: { $ref: '#/components/responses/ForbiddenResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/offering/requested-offerings': {
      get: {
        summary: 'Get Requested Offerings',
        tags: ['Offerings'],
        description: 'Retrieve list of requested offerings',
        security: [{ bearerAuth: [] }],
        responses: {
          200: { $ref: '#/components/responses/GetRequestedOfferingsResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/offering/duplicate-offering/{offeringId}': {
      post: {
        summary: 'Duplicate Offering',
        tags: ['Offerings'],
        description: 'Create a copy of an existing offering (issuer only)',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'offeringId',
            in: 'path',
            required: true,
            schema: { type: 'string', pattern: '^[0-9a-fA-F]{24}$' },
            description: 'ID of the offering to duplicate',
          },
        ],
        responses: {
          201: { $ref: '#/components/responses/GenericSuccessResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          403: { $ref: '#/components/responses/ForbiddenResponse' },
          404: { $ref: '#/components/responses/NotFoundResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/offering/subscribe': {
      post: {
        summary: 'Subscribe to Offering',
        tags: ['Offerings'],
        description: 'Subscribe to receive updates about an offering',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/SubscribeOfferingRequest' },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/offering/subscribers/{id}': {
      get: {
        summary: 'Get Offering Subscribers',
        tags: ['Offerings'],
        description: 'Retrieve list of subscribers for an offering',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            schema: { type: 'string', pattern: '^[0-9a-fA-F]{24}$' },
            description: 'Offering ID',
          },
        ],
        responses: {
          200: { $ref: '#/components/responses/GetSubscribersResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          404: { $ref: '#/components/responses/NotFoundResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/offering/order/{offeringId}': {
      get: {
        summary: 'Get Orders for Offering',
        tags: ['Offerings'],
        description: 'Retrieve all orders for a specific offering',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'offeringId',
            in: 'path',
            required: true,
            schema: { type: 'string', pattern: '^[0-9a-fA-F]{24}$' },
            description: 'Offering ID',
          },
        ],
        responses: {
          200: { $ref: '#/components/responses/GetOrdersResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          404: { $ref: '#/components/responses/NotFoundResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/offering/invested-offering': {
      get: {
        summary: 'Get Invested Offerings',
        tags: ['Offerings'],
        description: 'Retrieve list of offerings the user has invested in',
        security: [{ bearerAuth: [] }],
        responses: {
          200: { $ref: '#/components/responses/GetOfferingsResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/offering/whitelist-status': {
      put: {
        summary: 'Update Whitelist Status',
        tags: ['Offerings'],
        description: 'Update whitelist status for an offering (issuer only)',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/UpdateWhitelistRequest' },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          403: { $ref: '#/components/responses/ForbiddenResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/offering/total-count': {
      get: {
        summary: 'Get Total Offerings Count',
        tags: ['Offerings'],
        description: 'Retrieve total count of offerings (issuer only)',
        security: [{ bearerAuth: [] }],
        responses: {
          200: { $ref: '#/components/responses/TotalCountResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          403: { $ref: '#/components/responses/ForbiddenResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/offering/report/{offeringId}': {
      get: {
        summary: 'Get Single Offering Report',
        tags: ['Offerings'],
        description: 'Retrieve detailed report for a specific offering (issuer only)',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'offeringId',
            in: 'path',
            required: true,
            schema: { type: 'string', pattern: '^[0-9a-fA-F]{24}$' },
            description: 'Offering ID',
          },
        ],
        responses: {
          200: { $ref: '#/components/responses/OfferingReportResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          403: { $ref: '#/components/responses/ForbiddenResponse' },
          404: { $ref: '#/components/responses/NotFoundResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/offering/top-offering': {
      get: {
        summary: 'Get Top Offerings',
        tags: ['Offerings'],
        description: 'Retrieve top performing offerings (issuer only)',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'limit',
            in: 'query',
            required: false,
            schema: { type: 'integer', minimum: 1, maximum: 20, default: 10 },
            description: 'Number of top offerings to retrieve',
          },
        ],
        responses: {
          200: { $ref: '#/components/responses/GetOfferingsResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          403: { $ref: '#/components/responses/ForbiddenResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/offering/report': {
      get: {
        summary: 'Get All Offerings Report',
        tags: ['Offerings'],
        description: 'Retrieve comprehensive report for all offerings (issuer only)',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'page',
            in: 'query',
            required: false,
            schema: { type: 'integer', minimum: 1, default: 1 },
            description: 'Page number for pagination',
          },
          {
            name: 'limit',
            in: 'query',
            required: false,
            schema: { type: 'integer', minimum: 1, maximum: 100, default: 10 },
            description: 'Number of items per page',
          },
        ],
        responses: {
          200: { $ref: '#/components/responses/OfferingsReportResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          403: { $ref: '#/components/responses/ForbiddenResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/offering/{id}': {
      get: {
        summary: 'Get Offering Details',
        tags: ['Offerings'],
        description: 'Retrieve detailed information about a specific offering',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            schema: { type: 'string', pattern: '^[0-9a-fA-F]{24}$' },
            description: 'Offering ID',
          },
        ],
        responses: {
          200: { $ref: '#/components/responses/GetOfferingDetailsResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          404: { $ref: '#/components/responses/NotFoundResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
      delete: {
        summary: 'Delete Offering',
        tags: ['Offerings'],
        description: 'Delete an offering (issuer only)',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            schema: { type: 'string', pattern: '^[0-9a-fA-F]{24}$' },
            description: 'Offering ID',
          },
        ],
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          403: { $ref: '#/components/responses/ForbiddenResponse' },
          404: { $ref: '#/components/responses/NotFoundResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/transfer': {
      post: {
        summary: 'Create Transfer Request',
        tags: ['Transfers'],
        description: 'Create a new token transfer request',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/CreateTransferRequest' },
            },
          },
        },
        responses: {
          201: { $ref: '#/components/responses/GenericSuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
      get: {
        summary: 'Get Transfer Requests',
        tags: ['Transfers'],
        description: 'Retrieve all transfer requests for the authenticated user',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'page',
            in: 'query',
            required: false,
            schema: { type: 'integer', minimum: 1, default: 1 },
            description: 'Page number for pagination',
          },
          {
            name: 'limit',
            in: 'query',
            required: false,
            schema: { type: 'integer', minimum: 1, maximum: 100, default: 10 },
            description: 'Number of items per page',
          },
        ],
        responses: {
          200: { $ref: '#/components/responses/GetTransfersResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/transfer/reject': {
      put: {
        summary: 'Reject Transfer Request',
        tags: ['Transfers'],
        description: 'Reject a transfer request (issuer only)',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/RejectTransferRequest' },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          403: { $ref: '#/components/responses/ForbiddenResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/transfer/{offeringId}': {
      get: {
        summary: 'Get Transfer Requests by Offering',
        tags: ['Transfers'],
        description: 'Retrieve all transfer requests for a specific offering (issuer only)',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'offeringId',
            in: 'path',
            required: true,
            schema: { type: 'string', pattern: '^[0-9a-fA-F]{24}$' },
            description: 'Offering ID',
          },
        ],
        responses: {
          200: { $ref: '#/components/responses/GetTransfersResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          403: { $ref: '#/components/responses/ForbiddenResponse' },
          404: { $ref: '#/components/responses/NotFoundResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/redeem': {
      post: {
        summary: 'Create Redemption Request',
        tags: ['Redemptions'],
        description: 'Create a new token redemption request',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/CreateRedemptionRequest' },
            },
          },
        },
        responses: {
          201: { $ref: '#/components/responses/GenericSuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/redeem/{id}': {
      get: {
        summary: 'Get User Redemption Requests',
        tags: ['Redemptions'],
        description: 'Retrieve redemption requests for a user',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            schema: { type: 'string', pattern: '^[0-9a-fA-F]{24}$' },
            description: 'User ID',
          },
        ],
        responses: {
          200: { $ref: '#/components/responses/GetRedemptionsResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          404: { $ref: '#/components/responses/NotFoundResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/redeem/orders/{id}': {
      get: {
        summary: 'Get Redemption Requests for Offering',
        tags: ['Redemptions'],
        description: 'Retrieve all redemption requests for an offering (issuer only)',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            schema: { type: 'string', pattern: '^[0-9a-fA-F]{24}$' },
            description: 'Offering ID',
          },
        ],
        responses: {
          200: { $ref: '#/components/responses/GetRedemptionsResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          403: { $ref: '#/components/responses/ForbiddenResponse' },
          404: { $ref: '#/components/responses/NotFoundResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/docusign/generate-embedded-signing-url': {
      post: {
        summary: 'Generate DocuSign Embedded Signing URL',
        tags: ['DocuSign'],
        description: 'Generate an embedded signing URL for DocuSign documents',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/GenerateDocuSignURLRequest' },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/DocuSignURLResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/docusign/webhook_event': {
      post: {
        summary: 'DocuSign Webhook Event',
        tags: ['DocuSign'],
        description: 'Handle DocuSign webhook events',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                description: 'DocuSign webhook payload',
              },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/notification': {
      get: {
        summary: 'Get Notifications',
        tags: ['Notifications'],
        description: 'Retrieve user notifications',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'page',
            in: 'query',
            required: false,
            schema: { type: 'integer', minimum: 1, default: 1 },
            description: 'Page number for pagination',
          },
          {
            name: 'limit',
            in: 'query',
            required: false,
            schema: { type: 'integer', minimum: 1, maximum: 100, default: 10 },
            description: 'Number of items per page',
          },
        ],
        responses: {
          200: { $ref: '#/components/responses/GetNotificationsResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/notification/seen': {
      post: {
        summary: 'Mark Notification as Seen',
        tags: ['Notifications'],
        description: 'Mark a notification as seen by the user',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/SeenNotificationRequest' },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/representative/invite': {
      post: {
        summary: 'Invite Representative',
        tags: ['Representatives'],
        description: 'Invite a representative (issuer only)',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/InviteRepresentativeRequest' },
            },
          },
        },
        responses: {
          201: { $ref: '#/components/responses/GenericSuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          403: { $ref: '#/components/responses/ForbiddenResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
      get: {
        summary: 'Get Representatives',
        tags: ['Representatives'],
        description: 'Retrieve list of representatives (issuer only)',
        security: [{ bearerAuth: [] }],
        responses: {
          200: { $ref: '#/components/responses/GetRepresentativesResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          403: { $ref: '#/components/responses/ForbiddenResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/representative/multisig': {
      post: {
        summary: 'Add/Update Multisig',
        tags: ['Representatives'],
        description: 'Add or update multisig wallet configuration (issuer only)',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/MultisigRequest' },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          403: { $ref: '#/components/responses/ForbiddenResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
      get: {
        summary: 'Get Multisig Configuration',
        tags: ['Representatives'],
        description: 'Retrieve multisig wallet configuration (issuer only)',
        security: [{ bearerAuth: [] }],
        responses: {
          200: { $ref: '#/components/responses/GetMultisigResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          403: { $ref: '#/components/responses/ForbiddenResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/v1/auth/transactions/{offeringId}': {
      get: {
        summary: 'Get Transactions for Offering',
        tags: ['Transactions'],
        description: 'Retrieve all transactions for a specific offering (issuer only)',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'offeringId',
            in: 'path',
            required: true,
            schema: { type: 'string', pattern: '^[0-9a-fA-F]{24}$' },
            description: 'Offering ID',
          },
          {
            name: 'page',
            in: 'query',
            required: false,
            schema: { type: 'integer', minimum: 1, default: 1 },
            description: 'Page number for pagination',
          },
          {
            name: 'limit',
            in: 'query',
            required: false,
            schema: { type: 'integer', minimum: 1, maximum: 100, default: 10 },
            description: 'Number of items per page',
          },
        ],
        responses: {
          200: { $ref: '#/components/responses/GetTransactionsResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          403: { $ref: '#/components/responses/ForbiddenResponse' },
          404: { $ref: '#/components/responses/NotFoundResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
  },
  components: {
    securitySchemes: {
      bearerAuth: {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        description: 'JWT authorization header using the Bearer scheme',
      },
    },
    schemas: {
      // Request Schemas
      SignupRequest: {
        type: 'object',
        required: ['name', 'email', 'password', 'countryCode', 'mobile', 'userType', 'otpMethods'],
        properties: {
          name: {
            type: 'string',
            minLength: 2,
            maxLength: 50,
            pattern: '^[a-zA-Z\\s]+$',
            example: 'John Doe',
            description: 'Full name of the user',
          },
          email: {
            type: 'string',
            format: 'email',
            example: '<EMAIL>',
            description: 'Valid email address',
          },
          password: {
            type: 'string',
            minLength: 8,
            pattern: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]',
            example: 'SecurePass123!',
            description: 'Password with at least 8 characters, including uppercase, lowercase, number, and special character',
          },
          countryCode: {
            type: 'string',
            pattern: '^\\+[1-9]\\d{0,3}$',
            example: '+1',
            description: 'International country code',
          },
          mobile: {
            type: 'string',
            pattern: '^[0-9]{7,15}$',
            example: '**********',
            description: 'Mobile phone number without country code',
          },
          userType: {
            type: 'string',
            enum: ['investor', 'institution'],
            example: 'investor',
            description: 'Type of user account',
          },
          legalFullName: {
            type: 'string',
            minLength: 2,
            maxLength: 100,
            example: 'TechCorp LLC',
            description: 'Legal entity name (required for institution accounts)',
          },
          otpMethods: {
            type: 'string',
            enum: ['Email', 'SMS'],
            example: 'Email',
            description: 'Preferred OTP delivery method',
          },
        },
      },
      LoginRequest: {
        type: 'object',
        required: ['userName', 'password'],
        properties: {
          userName: {
            type: 'string',
            example: '<EMAIL>',
            description: 'Email address or username',
          },
          password: {
            type: 'string',
            example: 'SecurePass123!',
            description: 'User password',
          },
        },
      },
      SocialLoginRequest: {
        type: 'object',
        required: ['token', 'userType'],
        properties: {
          token: {
            type: 'string',
            example: '***************...',
            description: 'OAuth token from social provider',
          },
          userType: {
            type: 'string',
            enum: ['investor', 'institution'],
            example: 'investor',
            description: 'Type of user account to create',
          },
        },
      },
      VerifyOTPRequest: {
        type: 'object',
        required: ['otp', 'email', 'type', 'otpMethods'],
        properties: {
          otp: {
            type: 'string',
            pattern: '^[0-9]{6}$',
            example: '123456',
            description: '6-digit OTP code',
          },
          email: {
            type: 'string',
            format: 'email',
            example: '<EMAIL>',
            description: 'Email address associated with the OTP',
          },
          type: {
            type: 'string',
            enum: ['sign_up', 'login', 'forgot'],
            example: 'sign_up',
            description: 'Type of verification',
          },
          otpMethods: {
            type: 'string',
            enum: ['Email', 'SMS'],
            example: 'Email',
            description: 'Method used for OTP delivery',
          },
        },
      },
      ResendOTPRequest: {
        type: 'object',
        required: ['userName', 'type'],
        properties: {
          userName: {
            type: 'string',
            format: 'email',
            example: '<EMAIL>',
            description: 'Email address to resend OTP to',
          },
          type: {
            type: 'string',
            enum: ['sign_up', 'login', 'forgot'],
            example: 'login',
            description: 'Type of OTP being resent',
          },
          otpMethods: {
            type: 'string',
            enum: ['Email', 'SMS'],
            example: 'Email',
            description: 'Preferred OTP delivery method',
          },
        },
      },
      ForgotPasswordRequest: {
        type: 'object',
        required: ['email'],
        properties: {
          email: {
            type: 'string',
            format: 'email',
            example: '<EMAIL>',
            description: 'Email address for password reset',
          },
        },
      },
      ResetPasswordRequest: {
        type: 'object',
        required: ['token', 'newPassword'],
        properties: {
          token: {
            type: 'string',
            example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
            description: 'Password reset verification token',
          },
          newPassword: {
            type: 'string',
            minLength: 8,
            pattern: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]',
            example: 'NewSecurePass123!',
            description: 'New password meeting security requirements',
          },
        },
      },
      VerifyLogin2FARequest: {
        type: 'object',
        required: ['token', 'otp'],
        properties: {
          token: {
            type: 'string',
            example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
            description: 'Temporary login token',
          },
          otp: {
            type: 'string',
            pattern: '^[0-9]{6}$',
            example: '123456',
            description: '6-digit 2FA code from authenticator app',
          },
        },
      },
      Reset2FARequest: {
        type: 'object',
        required: ['token'],
        properties: {
          token: {
            type: 'string',
            example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
            description: 'Verification token for 2FA reset',
          },
        },
      },
      // Additional Request Schemas
      Verify2FARequest: {
        type: 'object',
        required: ['token'],
        properties: {
          token: {
            type: 'string',
            pattern: '^[0-9]{6}$',
            example: '123456',
            description: '6-digit 2FA code from authenticator app',
          },
        },
      },
      UpdateProfileRequest: {
        type: 'object',
        properties: {
          name: {
            type: 'string',
            minLength: 2,
            maxLength: 50,
            example: 'John Doe',
            description: 'Full name of the user',
          },
          dob: {
            type: 'string',
            format: 'date',
            example: '1990-01-15',
            description: 'Date of birth',
          },
          countryCode: {
            type: 'string',
            pattern: '^\\+[1-9]\\d{0,3}$',
            example: '+1',
            description: 'International country code',
          },
          mobile: {
            type: 'string',
            pattern: '^[0-9]{7,15}$',
            example: '**********',
            description: 'Mobile phone number',
          },
          profileImage: {
            type: 'string',
            format: 'binary',
            description: 'Profile image file',
          },
        },
      },
      UploadDocsRequest: {
        type: 'object',
        required: ['file', 'documentType'],
        properties: {
          file: {
            type: 'string',
            format: 'binary',
            description: 'Document file to upload',
          },
          documentType: {
            type: 'string',
            enum: ['passport', 'driversLicense', 'idCard', 'utility', 'bankStatement', 'other'],
            example: 'passport',
            description: 'Type of document being uploaded',
          },
        },
      },
      InvestorKYCStep1: {
        type: 'object',
        required: ['mainInformation', 'kycSteps'],
        properties: {
          mainInformation: {
            type: 'object',
            required: ['birthPlace', 'nationality', 'nationalIdNumber', 'identificationDocument', 'occupation', 'documentExpiration', 'dob'],
            properties: {
              birthPlace: { type: 'string', example: 'New York', description: 'Place of birth' },
              nationality: { type: 'string', example: 'American', description: 'Nationality' },
              nationalIdNumber: { type: 'string', example: '*********', description: 'National ID number' },
              identificationDocument: { type: 'string', enum: ['Passport', 'DriverLicense', 'NationalID'], example: 'Passport' },
              occupation: { type: 'string', example: 'Software Developer', description: 'Current occupation' },
              documentExpiration: { type: 'string', format: 'date', example: '2030-12-31', description: 'Document expiration date' },
              dob: { type: 'string', format: 'date', example: '1990-01-15', description: 'Date of birth' },
            },
          },
          kycSteps: { type: 'integer', example: 1, description: 'Current KYC step number' },
        },
      },
      InvestorKYCStep2: {
        type: 'object',
        required: ['wallets', 'kycSteps'],
        properties: {
          wallets: {
            type: 'array',
            items: {
              type: 'object',
              required: ['type', 'address'],
              properties: {
                type: { type: 'string', enum: ['MetamaskWallet', 'WalletConnect', 'CoinbaseWallet'], example: 'MetamaskWallet' },
                address: { type: 'string', pattern: '^0x[a-fA-F0-9]{40}$', example: '******************************************' },
              },
            },
          },
          kycSteps: { type: 'integer', example: 2 },
        },
      },
      InvestorKYCStep3: {
        type: 'object',
        required: ['documents', 'kycSteps'],
        properties: {
          documents: {
            type: 'object',
            required: ['frontId', 'backId'],
            properties: {
              frontId: { type: 'string', format: 'uri', example: 'https://example.com/front-id.jpg' },
              backId: { type: 'string', format: 'uri', example: 'https://example.com/back-id.jpg' },
              otherIdentification: { type: 'string', format: 'uri', example: 'https://example.com/other-id.jpg' },
            },
          },
          kycSteps: { type: 'integer', example: 3 },
        },
      },
      InvestorKYCStep4: {
        type: 'object',
        required: ['isIdentityVerification', 'kycSteps'],
        properties: {
          isIdentityVerification: {
            type: 'object',
            required: ['status'],
            properties: {
              status: { type: 'boolean', example: false, description: 'Identity verification status' },
            },
          },
          kycSteps: { type: 'integer', example: 4 },
        },
      },
      InvestorKYCStep5: {
        type: 'object',
        required: ['isFinalSubmission', 'kycSteps'],
        properties: {
          kycSteps: { type: 'integer', example: 5 },
          isFinalSubmission: { type: 'boolean', example: true, description: 'Flag indicating final submission' },
        },
      },
      InstitutionKYBStep1: {
        type: 'object',
        required: ['institutions', 'kycSteps'],
        properties: {
          institutions: {
            type: 'object',
            required: ['companyInformation', 'address'],
            properties: {
              companyInformation: {
                type: 'object',
                required: ['name', 'entityType', 'webSite', 'business', 'sourceOfFunds'],
                properties: {
                  name: { type: 'string', example: 'Tech Corp', description: 'Company name' },
                  entityType: { type: 'string', enum: ['LLC', 'Corporation', 'Partnership', 'Trust'], example: 'LLC' },
                  webSite: { type: 'string', format: 'uri', example: 'https://www.techcorp.com' },
                  business: { type: 'string', example: 'Technology', description: 'Line of business' },
                  sourceOfFunds: { type: 'string', example: 'Sales Revenue', description: 'Source of investment funds' },
                },
              },
              address: {
                type: 'object',
                required: ['address', 'country', 'state', 'city', 'zipCode'],
                properties: {
                  address: { type: 'string', example: '123 Tech Street', description: 'Street address' },
                  address2: { type: 'string', example: 'Suite 456', description: 'Additional address line' },
                  country: { type: 'string', example: 'USA', description: 'Country' },
                  state: { type: 'string', example: 'California', description: 'State/Province' },
                  city: { type: 'string', example: 'San Francisco', description: 'City' },
                  zipCode: { type: 'string', example: '94107', description: 'ZIP/Postal code' },
                },
              },
            },
          },
          kycSteps: { type: 'integer', example: 1 },
        },
      },
      InstitutionKYBStep2: {
        type: 'object',
        required: ['primaryContactInfo', 'kycSteps'],
        properties: {
          primaryContactInfo: {
            type: 'object',
            required: ['personalInformation', 'address'],
            properties: {
              personalInformation: {
                type: 'object',
                required: ['name', 'jobTitle', 'dob', 'socialSecurityNumber', 'citizenship', 'countryCode', 'mobile', 'email'],
                properties: {
                  name: { type: 'string', example: 'John Doe', description: 'Full name' },
                  jobTitle: { type: 'string', example: 'CEO', description: 'Job title' },
                  dob: { type: 'string', format: 'date', example: '1985-05-15', description: 'Date of birth' },
                  socialSecurityNumber: { type: 'string', example: '*********', description: 'SSN or tax ID' },
                  citizenship: { type: 'string', example: 'USA', description: 'Citizenship' },
                  countryCode: { type: 'string', example: '+1', description: 'Phone country code' },
                  mobile: { type: 'string', example: '**********', description: 'Mobile number' },
                  email: { type: 'string', format: 'email', example: '<EMAIL>', description: 'Email address' },
                },
              },
            },
          },
          kycSteps: { type: 'integer', example: 2 },
        },
      },
      InstitutionKYBStep3: {
        type: 'object',
        required: ['wallets', 'kycSteps'],
        properties: {
          wallets: {
            type: 'array',
            items: {
              type: 'object',
              required: ['type', 'address'],
              properties: {
                type: { type: 'string', enum: ['MetamaskWallet', 'WalletConnect', 'CoinbaseWallet'], example: 'MetamaskWallet' },
                address: { type: 'string', pattern: '^0x[a-fA-F0-9]{40}$', example: '******************************************' },
              },
            },
          },
          kycSteps: { type: 'integer', example: 3 },
        },
      },
      InstitutionKYBStep4: {
        type: 'object',
        required: ['documents', 'kycSteps'],
        properties: {
          documents: {
            type: 'object',
            required: ['frontId', 'backId'],
            properties: {
              frontId: { type: 'string', format: 'uri', example: 'https://example.com/front-id.jpg' },
              backId: { type: 'string', format: 'uri', example: 'https://example.com/back-id.jpg' },
              otherIdentification: { type: 'string', format: 'uri', example: 'https://example.com/other-id.jpg' },
            },
          },
          kycSteps: { type: 'integer', example: 4 },
        },
      },
      InstitutionKYBStep5: {
        type: 'object',
        required: ['isIdentityVerification', 'kycSteps'],
        properties: {
          isIdentityVerification: {
            type: 'object',
            required: ['status'],
            properties: {
              status: { type: 'boolean', example: false, description: 'Identity verification status' },
            },
          },
          kycSteps: { type: 'integer', example: 5 },
        },
      },
      InstitutionKYBStep6: {
        type: 'object',
        required: ['beneficialOwners', 'kycSteps'],
        properties: {
          beneficialOwners: {
            type: 'array',
            items: {
              type: 'object',
              required: ['personalInformation', 'address', 'identityProof'],
              properties: {
                personalInformation: {
                  type: 'object',
                  required: ['name', 'dob', 'socialSecurityNumber', 'citizenship'],
                  properties: {
                    name: { type: 'string', example: 'Jane Smith' },
                    dob: { type: 'string', format: 'date', example: '1980-02-20' },
                    socialSecurityNumber: { type: 'string', example: '987654321' },
                    citizenship: { type: 'string', example: 'USA' },
                  },
                },
              },
            },
          },
          kycSteps: { type: 'integer', example: 6 },
        },
      },
      InstitutionKYBStep7: {
        type: 'object',
        required: ['managementInfo', 'kycSteps'],
        properties: {
          managementInfo: {
            type: 'array',
            items: {
              type: 'object',
              required: ['personalInformation'],
              properties: {
                personalInformation: {
                  type: 'object',
                  required: ['name', 'jobTitle', 'dob', 'socialSecurityNumber', 'citizenship', 'countryCode', 'mobile', 'email'],
                  properties: {
                    name: { type: 'string', example: 'Alice Johnson', description: 'Full name' },
                    jobTitle: { type: 'string', example: 'CFO', description: 'Job title' },
                    dob: { type: 'string', format: 'date', example: '1975-12-05', description: 'Date of birth' },
                    socialSecurityNumber: { type: 'string', example: '*********', description: 'SSN or tax ID' },
                    citizenship: { type: 'string', example: 'USA', description: 'Citizenship' },
                    countryCode: { type: 'string', example: '+1', description: 'Phone country code' },
                    mobile: { type: 'string', example: '**********', description: 'Mobile number' },
                    email: { type: 'string', format: 'email', example: '<EMAIL>', description: 'Email address' },
                  },
                },
              },
            },
          },
          kycSteps: { type: 'integer', example: 7 },
        },
      },
      InstitutionKYBStep8: {
        type: 'object',
        required: ['isFinalSubmission', 'kycSteps'],
        properties: {
          kycSteps: { type: 'integer', example: 8 },
          isFinalSubmission: { type: 'boolean', example: true },
        },
      },
      // Additional Request Schemas
      ChangePasswordRequest: {
        type: 'object',
        required: ['oldPassword', 'newPassword'],
        properties: {
          oldPassword: {
            type: 'string',
            example: 'CurrentPass123!',
            description: 'Current password',
          },
          newPassword: {
            type: 'string',
            minLength: 8,
            pattern: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]',
            example: 'NewSecurePass123!',
            description: 'New password meeting security requirements',
          },
        },
      },
      CreateOrderRequest: {
        type: 'object',
        required: ['offeringId', 'quantity', 'pricePerShare'],
        properties: {
          offeringId: {
            type: 'string',
            pattern: '^[0-9a-fA-F]{24}$',
            example: '507f1f77bcf86cd799439011',
            description: 'ID of the offering to invest in',
          },
          quantity: {
            type: 'number',
            minimum: 0.01,
            example: 100.5,
            description: 'Number of shares to purchase',
          },
          pricePerShare: {
            type: 'number',
            minimum: 0.01,
            example: 50.25,
            description: 'Price per share',
          },
        },
      },
      RejectOrderRequest: {
        type: 'object',
        required: ['orderId', 'reason'],
        properties: {
          orderId: {
            type: 'string',
            pattern: '^[0-9a-fA-F]{24}$',
            example: '507f1f77bcf86cd799439011',
            description: 'ID of the order to reject',
          },
          reason: {
            type: 'string',
            minLength: 10,
            maxLength: 500,
            example: 'Insufficient compliance documentation',
            description: 'Reason for rejection',
          },
        },
      },
      CreateDividendRequest: {
        type: 'object',
        required: ['offeringId', 'dividendAmount', 'recordDate', 'paymentDate'],
        properties: {
          offeringId: {
            type: 'string',
            pattern: '^[0-9a-fA-F]{24}$',
            example: '507f1f77bcf86cd799439011',
            description: 'ID of the offering for dividend distribution',
          },
          dividendAmount: {
            type: 'number',
            minimum: 0.01,
            example: 2.5,
            description: 'Dividend amount per share',
          },
          recordDate: {
            type: 'string',
            format: 'date',
            example: '2024-03-15',
            description: 'Record date for dividend eligibility',
          },
          paymentDate: {
            type: 'string',
            format: 'date',
            example: '2024-03-30',
            description: 'Payment date for dividend distribution',
          },
          announcement: {
            type: 'string',
            format: 'binary',
            description: 'Dividend announcement document',
          },
        },
      },
      CalculateDividendRequest: {
        type: 'object',
        required: ['offeringId', 'totalDividendAmount'],
        properties: {
          offeringId: {
            type: 'string',
            pattern: '^[0-9a-fA-F]{24}$',
            example: '507f1f77bcf86cd799439011',
            description: 'ID of the offering',
          },
          totalDividendAmount: {
            type: 'number',
            minimum: 0.01,
            example: 50000.0,
            description: 'Total dividend amount to distribute',
          },
        },
      },
      CreateProposalRequest: {
        type: 'object',
        required: ['title', 'description', 'offeringId', 'votingEndDate'],
        properties: {
          title: {
            type: 'string',
            minLength: 5,
            maxLength: 200,
            example: 'Proposal to Increase Management Fee',
            description: 'Title of the proposal',
          },
          description: {
            type: 'string',
            minLength: 20,
            maxLength: 2000,
            example: 'This proposal seeks to increase the management fee from 1% to 1.5% annually to cover increased operational costs.',
            description: 'Detailed description of the proposal',
          },
          offeringId: {
            type: 'string',
            pattern: '^[0-9a-fA-F]{24}$',
            example: '507f1f77bcf86cd799439011',
            description: 'ID of the offering this proposal relates to',
          },
          votingEndDate: {
            type: 'string',
            format: 'date-time',
            example: '2024-04-15T23:59:59.000Z',
            description: 'End date for voting on this proposal',
          },
          documents: {
            type: 'string',
            format: 'binary',
            description: 'Supporting documents for the proposal',
          },
        },
      },
      SetProposalCapRequest: {
        type: 'object',
        required: ['offeringId', 'minimumInvestment'],
        properties: {
          offeringId: {
            type: 'string',
            pattern: '^[0-9a-fA-F]{24}$',
            example: '507f1f77bcf86cd799439011',
            description: 'ID of the offering',
          },
          minimumInvestment: {
            type: 'number',
            minimum: 0.01,
            example: 10000.0,
            description: 'Minimum investment amount required to vote on proposals',
          },
        },
      },
      VoteProposalRequest: {
        type: 'object',
        required: ['proposalId', 'vote'],
        properties: {
          proposalId: {
            type: 'string',
            pattern: '^[0-9a-fA-F]{24}$',
            example: '507f1f77bcf86cd799439011',
            description: 'ID of the proposal to vote on',
          },
          vote: {
            type: 'string',
            enum: ['approve', 'reject', 'abstain'],
            example: 'approve',
            description: 'Vote choice',
          },
        },
      },
      CreateOfferingRequest: {
        type: 'object',
        required: ['name', 'description', 'assetType', 'totalShares', 'pricePerShare', 'minimumInvestment'],
        properties: {
          name: {
            type: 'string',
            minLength: 5,
            maxLength: 100,
            example: 'Downtown Real Estate Fund',
            description: 'Name of the offering',
          },
          description: {
            type: 'string',
            minLength: 50,
            maxLength: 2000,
            example: 'A diversified real estate investment fund focusing on commercial properties in downtown areas.',
            description: 'Detailed description of the offering',
          },
          assetType: {
            type: 'string',
            enum: ['real_estate', 'equity', 'debt', 'commodity', 'other'],
            example: 'real_estate',
            description: 'Type of asset being tokenized',
          },
          totalShares: {
            type: 'number',
            minimum: 1,
            example: 1000000,
            description: 'Total number of shares in the offering',
          },
          pricePerShare: {
            type: 'number',
            minimum: 0.01,
            example: 100.0,
            description: 'Price per share in USD',
          },
          minimumInvestment: {
            type: 'number',
            minimum: 0.01,
            example: 1000.0,
            description: 'Minimum investment amount',
          },
          tickerSymbol: {
            type: 'string',
            minLength: 2,
            maxLength: 10,
            pattern: '^[A-Z0-9]+$',
            example: 'DREF',
            description: 'Ticker symbol for the token',
          },
        },
      },
      UpdateOfferingRequest: {
        type: 'object',
        required: ['offeringId'],
        properties: {
          offeringId: {
            type: 'string',
            pattern: '^[0-9a-fA-F]{24}$',
            example: '507f1f77bcf86cd799439011',
            description: 'ID of the offering to update',
          },
          name: {
            type: 'string',
            minLength: 5,
            maxLength: 100,
            example: 'Updated Real Estate Fund',
            description: 'Updated name of the offering',
          },
          description: {
            type: 'string',
            minLength: 50,
            maxLength: 2000,
            example: 'Updated description of the real estate investment fund.',
            description: 'Updated description of the offering',
          },
          pricePerShare: {
            type: 'number',
            minimum: 0.01,
            example: 105.0,
            description: 'Updated price per share',
          },
        },
      },
      RequestOfferingRequest: {
        type: 'object',
        required: ['assetName', 'assetType', 'requestedShares', 'estimatedValue'],
        properties: {
          assetName: {
            type: 'string',
            minLength: 5,
            maxLength: 100,
            example: 'Prime Office Building Complex',
            description: 'Name of the asset to be tokenized',
          },
          assetType: {
            type: 'string',
            enum: ['real_estate', 'equity', 'debt', 'commodity', 'other'],
            example: 'real_estate',
            description: 'Type of asset',
          },
          requestedShares: {
            type: 'number',
            minimum: 1,
            example: 500000,
            description: 'Number of shares requested',
          },
          estimatedValue: {
            type: 'number',
            minimum: 0.01,
            example: 10000000.0,
            description: 'Estimated total value of the asset',
          },
        },
      },
      SubscribeOfferingRequest: {
        type: 'object',
        required: ['offeringId'],
        properties: {
          offeringId: {
            type: 'string',
            pattern: '^[0-9a-fA-F]{24}$',
            example: '507f1f77bcf86cd799439011',
            description: 'ID of the offering to subscribe to',
          },
        },
      },
      UpdateWhitelistRequest: {
        type: 'object',
        required: ['offeringId', 'walletAddress', 'status'],
        properties: {
          offeringId: {
            type: 'string',
            pattern: '^[0-9a-fA-F]{24}$',
            example: '507f1f77bcf86cd799439011',
            description: 'ID of the offering',
          },
          walletAddress: {
            type: 'string',
            pattern: '^0x[a-fA-F0-9]{40}$',
            example: '******************************************',
            description: 'Wallet address to update',
          },
          status: {
            type: 'string',
            enum: ['approved', 'rejected'],
            example: 'approved',
            description: 'Whitelist status',
          },
        },
      },
      CreateTransferRequest: {
        type: 'object',
        required: ['offeringId', 'toAddress', 'quantity'],
        properties: {
          offeringId: {
            type: 'string',
            pattern: '^[0-9a-fA-F]{24}$',
            example: '507f1f77bcf86cd799439011',
            description: 'ID of the offering',
          },
          toAddress: {
            type: 'string',
            pattern: '^0x[a-fA-F0-9]{40}$',
            example: '******************************************',
            description: 'Recipient wallet address',
          },
          quantity: {
            type: 'number',
            minimum: 0.01,
            example: 50.0,
            description: 'Number of tokens to transfer',
          },
        },
      },
      RejectTransferRequest: {
        type: 'object',
        required: ['transferId', 'reason'],
        properties: {
          transferId: {
            type: 'string',
            pattern: '^[0-9a-fA-F]{24}$',
            example: '507f1f77bcf86cd799439011',
            description: 'ID of the transfer request to reject',
          },
          reason: {
            type: 'string',
            minLength: 10,
            maxLength: 500,
            example: 'Recipient not whitelisted',
            description: 'Reason for rejection',
          },
        },
      },
      CreateRedemptionRequest: {
        type: 'object',
        required: ['offeringId', 'quantity'],
        properties: {
          offeringId: {
            type: 'string',
            pattern: '^[0-9a-fA-F]{24}$',
            example: '507f1f77bcf86cd799439011',
            description: 'ID of the offering',
          },
          quantity: {
            type: 'number',
            minimum: 0.01,
            example: 100.0,
            description: 'Number of tokens to redeem',
          },
        },
      },
      GenerateDocuSignURLRequest: {
        type: 'object',
        required: ['offeringId'],
        properties: {
          offeringId: {
            type: 'string',
            pattern: '^[0-9a-fA-F]{24}$',
            example: '507f1f77bcf86cd799439011',
            description: 'ID of the offering for document signing',
          },
        },
      },
      SeenNotificationRequest: {
        type: 'object',
        required: ['notificationId'],
        properties: {
          notificationId: {
            type: 'string',
            pattern: '^[0-9a-fA-F]{24}$',
            example: '507f1f77bcf86cd799439011',
            description: 'ID of the notification to mark as seen',
          },
        },
      },
      InviteRepresentativeRequest: {
        type: 'object',
        required: ['email', 'name', 'role'],
        properties: {
          email: {
            type: 'string',
            format: 'email',
            example: '<EMAIL>',
            description: 'Email address of the representative',
          },
          name: {
            type: 'string',
            minLength: 2,
            maxLength: 50,
            example: 'John Representative',
            description: 'Full name of the representative',
          },
          role: {
            type: 'string',
            enum: ['admin', 'manager', 'viewer'],
            example: 'manager',
            description: 'Role of the representative',
          },
        },
      },
      MultisigRequest: {
        type: 'object',
        required: ['walletAddress', 'threshold', 'signers'],
        properties: {
          walletAddress: {
            type: 'string',
            pattern: '^0x[a-fA-F0-9]{40}$',
            example: '******************************************',
            description: 'Multisig wallet address',
          },
          threshold: {
            type: 'integer',
            minimum: 1,
            example: 2,
            description: 'Number of signatures required',
          },
          signers: {
            type: 'array',
            items: {
              type: 'string',
              pattern: '^0x[a-fA-F0-9]{40}$',
            },
            example: ['******************************************', '******************************************'],
            description: 'Array of signer wallet addresses',
          },
        },
      },
    },
    responses: {
      SignupSuccessResponse: {
        description: 'User registration successful',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: {
                  type: 'string',
                  example: 'Congratulations! You Have Successfully Registered. Please Verify Your Email To Continue.',
                },
                status: { type: 'integer', example: 201 },
                data: {
                  type: 'object',
                  properties: {
                    isOtpActive: { type: 'boolean', example: true },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      LoginSuccessResponse: {
        description: 'Login successful',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Login successful' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'object',
                  properties: {
                    accessToken: { type: 'string', example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' },
                    is2FAActive: { type: 'boolean', example: false },
                    isOtpActive: { type: 'boolean', example: true },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      VerifySuccessResponse: {
        description: 'OTP verification successful',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Verification successful' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'object',
                  properties: {
                    accessToken: { type: 'string', example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      Reset2FAResponse: {
        description: '2FA reset successful',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: '2FA reset successful' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'object',
                  properties: {
                    qrCode: { type: 'string', example: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...' },
                    secret: { type: 'string', example: 'JBSWY3DPEHPK3PXP' },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      GenericSuccessResponse: {
        description: 'Operation successful',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Operation completed successfully' },
                status: { type: 'integer', example: 200 },
                data: { type: 'object', additionalProperties: true },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      BadRequestResponse: {
        description: 'Bad request - validation error',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Validation error: Invalid email format' },
                status: { type: 'integer', example: 400 },
                data: { type: 'object', example: {} },
                error: { type: 'boolean', example: true },
              },
            },
          },
        },
      },
      UnauthorizedResponse: {
        description: 'Unauthorized access',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Invalid credentials' },
                status: { type: 'integer', example: 401 },
                data: { type: 'object', example: {} },
                error: { type: 'boolean', example: true },
              },
            },
          },
        },
      },
      NotFoundResponse: {
        description: 'Resource not found',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Resource not found' },
                status: { type: 'integer', example: 404 },
                data: { type: 'object', example: {} },
                error: { type: 'boolean', example: true },
              },
            },
          },
        },
      },
      ConflictResponse: {
        description: 'Resource conflict',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Email already exists' },
                status: { type: 'integer', example: 409 },
                data: { type: 'object', example: {} },
                error: { type: 'boolean', example: true },
              },
            },
          },
        },
      },
      TooManyRequestsResponse: {
        description: 'Rate limit exceeded',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Too many requests. Please try again later.' },
                status: { type: 'integer', example: 429 },
                data: { type: 'object', example: {} },
                error: { type: 'boolean', example: true },
              },
            },
          },
        },
      },
      ServerErrorResponse: {
        description: 'Internal server error',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Internal server error' },
                status: { type: 'integer', example: 500 },
                data: { type: 'object', example: {} },
                error: { type: 'boolean', example: true },
              },
            },
          },
        },
      },
      // Additional Response Schemas
      GetProfileResponse: {
        description: 'User profile information',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Profile fetched successfully' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'object',
                  properties: {
                    _id: { type: 'string', example: '507f1f77bcf86cd799439011' },
                    name: { type: 'string', example: 'John Doe' },
                    email: { type: 'string', example: '<EMAIL>' },
                    countryCode: { type: 'string', example: '+1' },
                    mobile: { type: 'string', example: '**********' },
                    dob: { type: 'string', format: 'date', example: '1990-01-15' },
                    userType: { type: 'string', enum: ['investor', 'institution'], example: 'investor' },
                    isActive: { type: 'boolean', example: true },
                    isKycCompleted: { type: 'boolean', example: true },
                    is2FAActive: { type: 'boolean', example: false },
                    createdAt: { type: 'string', format: 'date-time', example: '2024-01-15T10:26:22.270Z' },
                    updatedAt: { type: 'string', format: 'date-time', example: '2024-01-15T10:26:22.270Z' },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      Enable2FAResponse: {
        description: '2FA enablement successful',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: '2FA setup initiated' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'object',
                  properties: {
                    qrCode: { type: 'string', example: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...' },
                    secret: { type: 'string', example: 'JBSWY3DPEHPK3PXP' },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      UploadDocsResponse: {
        description: 'Document upload successful',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Document uploaded successfully' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'object',
                  properties: {
                    url: { type: 'string', format: 'uri', example: 'https://example.com/documents/passport-123.pdf' },
                    documentType: { type: 'string', example: 'passport' },
                    fileName: { type: 'string', example: 'passport-123.pdf' },
                    fileSize: { type: 'integer', example: 2048576 },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      KYCSuccessResponse: {
        description: 'KYC/KYB submission successful',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'KYC data submitted successfully' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'object',
                  properties: {
                    kycSteps: { type: 'integer', example: 1 },
                    isCompleted: { type: 'boolean', example: false },
                    nextStep: { type: 'string', example: 'Please complete wallet verification' },
                    completionPercentage: { type: 'number', format: 'float', example: 20.0 },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      PortfolioResponse: {
        description: 'User portfolio information',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Portfolio fetched successfully' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'object',
                  properties: {
                    totalValue: { type: 'number', format: 'float', example: 150000.5 },
                    totalInvested: { type: 'number', format: 'float', example: 125000.0 },
                    totalReturns: { type: 'number', format: 'float', example: 25000.5 },
                    returnPercentage: { type: 'number', format: 'float', example: 20.0 },
                    holdings: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          offeringId: { type: 'string', example: '507f1f77bcf86cd799439011' },
                          offeringName: { type: 'string', example: 'Real Estate Fund A' },
                          tokenTicker: { type: 'string', example: 'REIT' },
                          quantity: { type: 'number', format: 'float', example: 1000.0 },
                          currentValue: { type: 'number', format: 'float', example: 55000.0 },
                          investedAmount: { type: 'number', format: 'float', example: 50000.0 },
                          returns: { type: 'number', format: 'float', example: 5000.0 },
                          returnPercentage: { type: 'number', format: 'float', example: 10.0 },
                        },
                      },
                    },
                    pagination: {
                      type: 'object',
                      properties: {
                        currentPage: { type: 'integer', example: 1 },
                        totalPages: { type: 'integer', example: 3 },
                        totalCount: { type: 'integer', example: 25 },
                        hasNext: { type: 'boolean', example: true },
                        hasPrevious: { type: 'boolean', example: false },
                      },
                    },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      // Additional Response Schemas
      ForbiddenResponse: {
        description: 'Access forbidden',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Access forbidden: Insufficient permissions' },
                status: { type: 'integer', example: 403 },
                data: { type: 'object', example: {} },
                error: { type: 'boolean', example: true },
              },
            },
          },
        },
      },
      TransferAgentListResponse: {
        description: 'Transfer agents list retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Transfer agents fetched successfully' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'object',
                  properties: {
                    transferAgents: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          _id: { type: 'string', example: '507f1f77bcf86cd799439011' },
                          name: { type: 'string', example: 'Global Transfer Services' },
                          email: { type: 'string', example: '<EMAIL>' },
                          phone: { type: 'string', example: '******-0123' },
                          website: { type: 'string', example: 'https://globaltransfer.com' },
                        },
                      },
                    },
                    pagination: {
                      type: 'object',
                      properties: {
                        currentPage: { type: 'integer', example: 1 },
                        totalPages: { type: 'integer', example: 5 },
                        totalCount: { type: 'integer', example: 45 },
                      },
                    },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      TransferAgentDetailsResponse: {
        description: 'Transfer agent details retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Transfer agent details fetched successfully' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'object',
                  properties: {
                    _id: { type: 'string', example: '507f1f77bcf86cd799439011' },
                    name: { type: 'string', example: 'Global Transfer Services' },
                    email: { type: 'string', example: '<EMAIL>' },
                    phone: { type: 'string', example: '******-0123' },
                    website: { type: 'string', example: 'https://globaltransfer.com' },
                    address: { type: 'string', example: '123 Finance St, New York, NY 10001' },
                    services: { type: 'array', items: { type: 'string' }, example: ['Transfer Agent', 'Shareholder Services', 'Dividend Distribution'] },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      CreateOrderResponse: {
        description: 'Order created successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Order created successfully' },
                status: { type: 'integer', example: 201 },
                data: {
                  type: 'object',
                  properties: {
                    orderId: { type: 'string', example: '507f1f77bcf86cd799439011' },
                    status: { type: 'string', example: 'pending' },
                    totalAmount: { type: 'number', example: 5025.0 },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      GetOrdersResponse: {
        description: 'Orders retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Orders fetched successfully' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'object',
                  properties: {
                    orders: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          _id: { type: 'string', example: '507f1f77bcf86cd799439011' },
                          offeringId: { type: 'string', example: '507f1f77bcf86cd799439012' },
                          quantity: { type: 'number', example: 100.5 },
                          pricePerShare: { type: 'number', example: 50.25 },
                          totalAmount: { type: 'number', example: 5025.0 },
                          status: { type: 'string', example: 'pending' },
                          createdAt: { type: 'string', format: 'date-time' },
                        },
                      },
                    },
                    pagination: {
                      type: 'object',
                      properties: {
                        currentPage: { type: 'integer', example: 1 },
                        totalPages: { type: 'integer', example: 3 },
                        totalCount: { type: 'integer', example: 25 },
                      },
                    },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      GetOrderResponse: {
        description: 'Order details retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Order details fetched successfully' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'object',
                  properties: {
                    _id: { type: 'string', example: '507f1f77bcf86cd799439011' },
                    offeringId: { type: 'string', example: '507f1f77bcf86cd799439012' },
                    offeringName: { type: 'string', example: 'Real Estate Fund A' },
                    quantity: { type: 'number', example: 100.5 },
                    pricePerShare: { type: 'number', example: 50.25 },
                    totalAmount: { type: 'number', example: 5025.0 },
                    status: { type: 'string', example: 'pending' },
                    createdAt: { type: 'string', format: 'date-time' },
                    updatedAt: { type: 'string', format: 'date-time' },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      TopHoldingsResponse: {
        description: 'Top holdings retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Top holdings fetched successfully' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      offeringId: { type: 'string', example: '507f1f77bcf86cd799439011' },
                      offeringName: { type: 'string', example: 'Real Estate Fund A' },
                      totalValue: { type: 'number', example: 75000.0 },
                      percentage: { type: 'number', example: 25.5 },
                    },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      PortfolioReportResponse: {
        description: 'Portfolio performance report retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Portfolio report fetched successfully' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'object',
                  properties: {
                    totalValue: { type: 'number', example: 150000.0 },
                    totalInvested: { type: 'number', example: 125000.0 },
                    totalReturns: { type: 'number', example: 25000.0 },
                    returnPercentage: { type: 'number', example: 20.0 },
                    performanceData: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          date: { type: 'string', format: 'date' },
                          value: { type: 'number' },
                        },
                      },
                    },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      GetOfferingsResponse: {
        description: 'Offerings retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Offerings fetched successfully' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'object',
                  properties: {
                    offerings: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          _id: { type: 'string', example: '507f1f77bcf86cd799439011' },
                          name: { type: 'string', example: 'Downtown Real Estate Fund' },
                          description: { type: 'string', example: 'A diversified real estate investment fund' },
                          assetType: { type: 'string', example: 'real_estate' },
                          totalShares: { type: 'number', example: 1000000 },
                          pricePerShare: { type: 'number', example: 100.0 },
                          tickerSymbol: { type: 'string', example: 'DREF' },
                          status: { type: 'string', example: 'active' },
                        },
                      },
                    },
                    pagination: {
                      type: 'object',
                      properties: {
                        currentPage: { type: 'integer', example: 1 },
                        totalPages: { type: 'integer', example: 5 },
                        totalCount: { type: 'integer', example: 48 },
                      },
                    },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      GetDividendsResponse: {
        description: 'Dividends retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Dividends fetched successfully' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      _id: { type: 'string', example: '507f1f77bcf86cd799439011' },
                      offeringId: { type: 'string', example: '507f1f77bcf86cd799439012' },
                      dividendAmount: { type: 'number', example: 2.5 },
                      recordDate: { type: 'string', format: 'date', example: '2024-03-15' },
                      paymentDate: { type: 'string', format: 'date', example: '2024-03-30' },
                      status: { type: 'string', example: 'pending' },
                    },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      CalculateDividendResponse: {
        description: 'Dividend calculation completed successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Dividend calculation completed' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'object',
                  properties: {
                    totalInvestors: { type: 'integer', example: 150 },
                    totalDividendAmount: { type: 'number', example: 50000.0 },
                    calculations: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          investorId: { type: 'string', example: '507f1f77bcf86cd799439013' },
                          shares: { type: 'number', example: 1000.0 },
                          dividendAmount: { type: 'number', example: 2500.0 },
                        },
                      },
                    },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      GetProposalsResponse: {
        description: 'Proposals retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Proposals fetched successfully' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      _id: { type: 'string', example: '507f1f77bcf86cd799439011' },
                      title: { type: 'string', example: 'Proposal to Increase Management Fee' },
                      description: { type: 'string', example: 'This proposal seeks to increase the management fee...' },
                      offeringId: { type: 'string', example: '507f1f77bcf86cd799439012' },
                      votingEndDate: { type: 'string', format: 'date-time' },
                      status: { type: 'string', example: 'active' },
                      votesFor: { type: 'integer', example: 45 },
                      votesAgainst: { type: 'integer', example: 12 },
                      votesAbstain: { type: 'integer', example: 3 },
                    },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      GetNotificationsResponse: {
        description: 'Notifications retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Notifications fetched successfully' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      _id: { type: 'string', example: '507f1f77bcf86cd799439011' },
                      title: { type: 'string', example: 'Order Approved' },
                      message: { type: 'string', example: 'Your order for Real Estate Fund A has been approved' },
                      type: { type: 'string', example: 'order_update' },
                      isRead: { type: 'boolean', example: false },
                      createdAt: { type: 'string', format: 'date-time' },
                    },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      GetTransfersResponse: {
        description: 'Transfer requests retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Transfer requests fetched successfully' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      _id: { type: 'string', example: '507f1f77bcf86cd799439011' },
                      offeringId: { type: 'string', example: '507f1f77bcf86cd799439012' },
                      fromAddress: { type: 'string', example: '******************************************' },
                      toAddress: { type: 'string', example: '******************************************' },
                      quantity: { type: 'number', example: 50.0 },
                      status: { type: 'string', example: 'pending' },
                      createdAt: { type: 'string', format: 'date-time' },
                    },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      GetRedemptionsResponse: {
        description: 'Redemption requests retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Redemption requests fetched successfully' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      _id: { type: 'string', example: '507f1f77bcf86cd799439011' },
                      offeringId: { type: 'string', example: '507f1f77bcf86cd799439012' },
                      quantity: { type: 'number', example: 100.0 },
                      redemptionValue: { type: 'number', example: 10500.0 },
                      status: { type: 'string', example: 'pending' },
                      createdAt: { type: 'string', format: 'date-time' },
                    },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      DocuSignURLResponse: {
        description: 'DocuSign embedded signing URL generated successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Embedded signing URL generated successfully' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'object',
                  properties: {
                    signingUrl: { type: 'string', format: 'uri', example: 'https://demo.docusign.net/Signing/StartInSession.aspx?...' },
                    envelopeId: { type: 'string', example: 'ab123c45-6789-0def-gh12-34567890ijkl' },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      GetRepresentativesResponse: {
        description: 'Representatives retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Representatives fetched successfully' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      _id: { type: 'string', example: '507f1f77bcf86cd799439011' },
                      name: { type: 'string', example: 'John Representative' },
                      email: { type: 'string', example: '<EMAIL>' },
                      role: { type: 'string', example: 'manager' },
                      status: { type: 'string', example: 'active' },
                      invitedAt: { type: 'string', format: 'date-time' },
                    },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      GetMultisigResponse: {
        description: 'Multisig configuration retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Multisig configuration fetched successfully' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'object',
                  properties: {
                    walletAddress: { type: 'string', example: '******************************************' },
                    threshold: { type: 'integer', example: 2 },
                    signers: {
                      type: 'array',
                      items: { type: 'string' },
                      example: ['******************************************', '******************************************'],
                    },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      GetTransactionsResponse: {
        description: 'Transactions retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Transactions fetched successfully' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      _id: { type: 'string', example: '507f1f77bcf86cd799439011' },
                      offeringId: { type: 'string', example: '507f1f77bcf86cd799439012' },
                      transactionHash: { type: 'string', example: '0xabc123def456...' },
                      type: { type: 'string', example: 'mint' },
                      amount: { type: 'number', example: 1000.0 },
                      fromAddress: { type: 'string', example: '******************************************' },
                      toAddress: { type: 'string', example: '******************************************' },
                      status: { type: 'string', example: 'confirmed' },
                      createdAt: { type: 'string', format: 'date-time' },
                    },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      NAVHistoryResponse: {
        description: 'NAV history retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'NAV history fetched successfully' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      date: { type: 'string', format: 'date', example: '2024-01-15' },
                      price: { type: 'number', example: 102.5 },
                      change: { type: 'number', example: 2.5 },
                      changePercent: { type: 'number', example: 2.5 },
                    },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      GetRequestedOfferingsResponse: {
        description: 'Requested offerings retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Requested offerings fetched successfully' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      _id: { type: 'string', example: '507f1f77bcf86cd799439011' },
                      assetName: { type: 'string', example: 'Prime Office Building Complex' },
                      assetType: { type: 'string', example: 'real_estate' },
                      requestedShares: { type: 'number', example: 500000 },
                      estimatedValue: { type: 'number', example: 10000000.0 },
                      status: { type: 'string', example: 'pending' },
                      createdAt: { type: 'string', format: 'date-time' },
                    },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      GetSubscribersResponse: {
        description: 'Offering subscribers retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Subscribers fetched successfully' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      userId: { type: 'string', example: '507f1f77bcf86cd799439011' },
                      name: { type: 'string', example: 'John Doe' },
                      email: { type: 'string', example: '<EMAIL>' },
                      subscribedAt: { type: 'string', format: 'date-time' },
                    },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      TotalCountResponse: {
        description: 'Total count retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Total count fetched successfully' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'object',
                  properties: {
                    totalOfferings: { type: 'integer', example: 25 },
                    activeOfferings: { type: 'integer', example: 18 },
                    pendingOfferings: { type: 'integer', example: 5 },
                    completedOfferings: { type: 'integer', example: 2 },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      OfferingReportResponse: {
        description: 'Offering report retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Offering report fetched successfully' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'object',
                  properties: {
                    offeringId: { type: 'string', example: '507f1f77bcf86cd799439011' },
                    offeringName: { type: 'string', example: 'Real Estate Fund A' },
                    totalInvestors: { type: 'integer', example: 150 },
                    totalInvested: { type: 'number', example: 2500000.0 },
                    totalShares: { type: 'number', example: 25000.0 },
                    currentNAV: { type: 'number', example: 105.25 },
                    performance: {
                      type: 'object',
                      properties: {
                        totalReturn: { type: 'number', example: 125000.0 },
                        returnPercent: { type: 'number', example: 5.25 },
                      },
                    },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      OfferingsReportResponse: {
        description: 'All offerings report retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Offerings report fetched successfully' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'object',
                  properties: {
                    summary: {
                      type: 'object',
                      properties: {
                        totalOfferings: { type: 'integer', example: 25 },
                        totalInvestors: { type: 'integer', example: 450 },
                        totalInvested: { type: 'number', example: 15750000.0 },
                        averageReturn: { type: 'number', example: 8.5 },
                      },
                    },
                    offerings: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          _id: { type: 'string', example: '507f1f77bcf86cd799439011' },
                          name: { type: 'string', example: 'Real Estate Fund A' },
                          totalInvested: { type: 'number', example: 2500000.0 },
                          investorCount: { type: 'integer', example: 150 },
                          performance: { type: 'number', example: 5.25 },
                        },
                      },
                    },
                    pagination: {
                      type: 'object',
                      properties: {
                        currentPage: { type: 'integer', example: 1 },
                        totalPages: { type: 'integer', example: 3 },
                        totalCount: { type: 'integer', example: 25 },
                      },
                    },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      GetOfferingDetailsResponse: {
        description: 'Offering details retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Offering details fetched successfully' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'object',
                  properties: {
                    _id: { type: 'string', example: '507f1f77bcf86cd799439011' },
                    name: { type: 'string', example: 'Downtown Real Estate Fund' },
                    description: { type: 'string', example: 'A diversified real estate investment fund focusing on commercial properties in downtown areas.' },
                    assetType: { type: 'string', example: 'real_estate' },
                    totalShares: { type: 'number', example: 1000000 },
                    availableShares: { type: 'number', example: 750000 },
                    pricePerShare: { type: 'number', example: 100.0 },
                    minimumInvestment: { type: 'number', example: 1000.0 },
                    tickerSymbol: { type: 'string', example: 'DREF' },
                    status: { type: 'string', example: 'active' },
                    createdAt: { type: 'string', format: 'date-time' },
                    updatedAt: { type: 'string', format: 'date-time' },
                    documents: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          name: { type: 'string', example: 'Prospectus.pdf' },
                          url: { type: 'string', format: 'uri', example: 'https://example.com/docs/prospectus.pdf' },
                          type: { type: 'string', example: 'prospectus' },
                        },
                      },
                    },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      DividendInvestorsResponse: {
        description: 'Dividend investors retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Dividend investors fetched successfully' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      investorId: { type: 'string', example: '507f1f77bcf86cd799439011' },
                      name: { type: 'string', example: 'John Doe' },
                      email: { type: 'string', example: '<EMAIL>' },
                      shares: { type: 'number', example: 1000.0 },
                      eligibleDividend: { type: 'number', example: 2500.0 },
                    },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      InvestorDividendHistoryResponse: {
        description: 'Investor dividend history retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Dividend history fetched successfully' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'object',
                  properties: {
                    totalDividendsReceived: { type: 'number', example: 15750.0 },
                    dividends: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          _id: { type: 'string', example: '507f1f77bcf86cd799439011' },
                          offeringId: { type: 'string', example: '507f1f77bcf86cd799439012' },
                          offeringName: { type: 'string', example: 'Real Estate Fund A' },
                          dividendAmount: { type: 'number', example: 2500.0 },
                          shares: { type: 'number', example: 1000.0 },
                          paymentDate: { type: 'string', format: 'date', example: '2024-03-30' },
                          status: { type: 'string', example: 'paid' },
                        },
                      },
                    },
                    pagination: {
                      type: 'object',
                      properties: {
                        currentPage: { type: 'integer', example: 1 },
                        totalPages: { type: 'integer', example: 2 },
                        totalCount: { type: 'integer', example: 15 },
                      },
                    },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      GetProposalDetailsResponse: {
        description: 'Proposal details retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Proposal details fetched successfully' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'object',
                  properties: {
                    _id: { type: 'string', example: '507f1f77bcf86cd799439011' },
                    title: { type: 'string', example: 'Proposal to Increase Management Fee' },
                    description: { type: 'string', example: 'This proposal seeks to increase the management fee from 1% to 1.5% annually to cover increased operational costs.' },
                    offeringId: { type: 'string', example: '507f1f77bcf86cd799439012' },
                    offeringName: { type: 'string', example: 'Real Estate Fund A' },
                    votingStartDate: { type: 'string', format: 'date-time' },
                    votingEndDate: { type: 'string', format: 'date-time' },
                    status: { type: 'string', example: 'active' },
                    votingResults: {
                      type: 'object',
                      properties: {
                        totalVoters: { type: 'integer', example: 60 },
                        votesFor: { type: 'integer', example: 45 },
                        votesAgainst: { type: 'integer', example: 12 },
                        votesAbstain: { type: 'integer', example: 3 },
                        totalShares: { type: 'number', example: 125000.0 },
                        sharesFor: { type: 'number', example: 95000.0 },
                        sharesAgainst: { type: 'number', example: 25000.0 },
                        sharesAbstain: { type: 'number', example: 5000.0 },
                      },
                    },
                    documents: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          name: { type: 'string', example: 'proposal-document.pdf' },
                          url: { type: 'string', format: 'uri' },
                        },
                      },
                    },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      WhitelistedOfferingsResponse: {
        description: 'Whitelisted offerings for proposals retrieved successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Whitelisted offerings fetched successfully' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      offeringId: { type: 'string', example: '507f1f77bcf86cd799439011' },
                      offeringName: { type: 'string', example: 'Real Estate Fund A' },
                      userShares: { type: 'number', example: 1000.0 },
                      canVote: { type: 'boolean', example: true },
                      minimumInvestment: { type: 'number', example: 500.0 },
                    },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
    },
  },
};
