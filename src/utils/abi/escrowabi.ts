export default [
  {
    anonymous: false,
    inputs: [
      { indexed: false, internalType: 'address', name: 'token', type: 'address' },
      { indexed: false, internalType: 'uint16', name: 'new<PERSON>ee', type: 'uint16' },
      { indexed: false, internalType: 'string', name: 'id', type: 'string' },
      { indexed: false, internalType: 'uint256', name: 'timeStamp', type: 'uint256' },
    ],
    name: 'AdminFeeUpdated',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      { indexed: false, internalType: 'address', name: 'newAdminWallet', type: 'address' },
      { indexed: false, internalType: 'string', name: 'id', type: 'string' },
      { indexed: false, internalType: 'uint256', name: 'timeStamp', type: 'uint256' },
    ],
    name: '<PERSON><PERSON><PERSON>alletUpdated',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      { indexed: false, internalType: 'address', name: '_EquityConfigProxy', type: 'address' },
      { indexed: false, internalType: 'string', name: 'mappingValue', type: 'string' },
    ],
    name: 'EquityConfigCreated',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      { indexed: false, internalType: 'address', name: '_FundProxy', type: 'address' },
      { indexed: false, internalType: 'string', name: 'mappingValue', type: 'string' },
    ],
    name: 'FundCreated',
    type: 'event',
  },
  { anonymous: false, inputs: [{ indexed: false, internalType: 'uint8', name: 'version', type: 'uint8' }], name: 'Initialized', type: 'event' },
  {
    anonymous: false,
    inputs: [
      { indexed: true, internalType: 'address', name: 'previousOwner', type: 'address' },
      { indexed: true, internalType: 'address', name: 'newOwner', type: 'address' },
    ],
    name: 'OwnershipTransferred',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      { indexed: false, internalType: 'address', name: 'UserAddress', type: 'address' },
      { indexed: false, internalType: 'address', name: 'OfferingAddress', type: 'address' },
      { indexed: false, internalType: 'string', name: 'salt', type: 'string' },
    ],
    name: 'Whitelisted',
    type: 'event',
  },
  {
    inputs: [
      { internalType: 'address', name: '_token', type: 'address' },
      { internalType: 'bytes', name: '_data', type: 'bytes' },
      { internalType: 'uint16', name: '_adminFee', type: 'uint16' },
      { internalType: 'uint256', name: '_totalTokenSupply', type: 'uint256' },
      { internalType: 'string', name: 'mappingValue', type: 'string' },
    ],
    name: 'createEquityConfig',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      { internalType: 'address', name: '_token', type: 'address' },
      { internalType: 'bytes', name: '_data', type: 'bytes' },
      { internalType: 'uint16', name: '_adminFee', type: 'uint16' },
      { internalType: 'uint256', name: '_totalTokenSupply', type: 'uint256' },
      { internalType: 'string', name: 'mappingValue', type: 'string' },
    ],
    name: 'createFund',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  { inputs: [{ internalType: 'address', name: '_token', type: 'address' }], name: 'getAdminFee', outputs: [{ internalType: 'uint16', name: '', type: 'uint16' }], stateMutability: 'view', type: 'function' },
  { inputs: [], name: 'getAdminWallet', outputs: [{ internalType: 'address', name: '', type: 'address' }], stateMutability: 'view', type: 'function' },
  { inputs: [{ internalType: 'address', name: '_token', type: 'address' }], name: 'getAssetType', outputs: [{ internalType: 'uint8', name: '', type: 'uint8' }], stateMutability: 'view', type: 'function' },
  { inputs: [{ internalType: 'address', name: '_token', type: 'address' }], name: 'getFund', outputs: [{ internalType: 'address', name: '', type: 'address' }], stateMutability: 'view', type: 'function' },
  { inputs: [], name: 'getMasterFactory', outputs: [{ internalType: 'address', name: '', type: 'address' }], stateMutability: 'view', type: 'function' },
  { inputs: [{ internalType: 'address', name: '_token', type: 'address' }], name: 'getTokenTotalSupply', outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }], stateMutability: 'view', type: 'function' },
  { inputs: [], name: 'implEquityConfig', outputs: [{ internalType: 'address', name: '', type: 'address' }], stateMutability: 'view', type: 'function' },
  { inputs: [], name: 'implFund', outputs: [{ internalType: 'address', name: '', type: 'address' }], stateMutability: 'view', type: 'function' },
  { inputs: [{ internalType: 'address', name: '_factory', type: 'address' }], name: 'init', outputs: [], stateMutability: 'nonpayable', type: 'function' },
  { inputs: [], name: 'masterFactory', outputs: [{ internalType: 'address', name: '', type: 'address' }], stateMutability: 'view', type: 'function' },
  { inputs: [], name: 'owner', outputs: [{ internalType: 'address', name: '', type: 'address' }], stateMutability: 'view', type: 'function' },
  { inputs: [], name: 'renounceOwnership', outputs: [], stateMutability: 'nonpayable', type: 'function' },
  {
    inputs: [
      { internalType: 'address', name: '_token', type: 'address' },
      { internalType: 'uint16', name: '_adminFee', type: 'uint16' },
      { internalType: 'string', name: 'actionID', type: 'string' },
    ],
    name: 'setAdminFee',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      { internalType: 'address', name: '_newWallet', type: 'address' },
      { internalType: 'string', name: '_actionID', type: 'string' },
    ],
    name: 'setAdminWallet',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      { internalType: 'address', name: '_implFund', type: 'address' },
      { internalType: 'address', name: '_implEquityConfig', type: 'address' },
    ],
    name: 'setImpl',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  { inputs: [{ internalType: 'address', name: 'factory_', type: 'address' }], name: 'setMasterFactory', outputs: [], stateMutability: 'nonpayable', type: 'function' },
  { inputs: [{ internalType: 'address', name: 'newOwner', type: 'address' }], name: 'transferOwnership', outputs: [], stateMutability: 'nonpayable', type: 'function' },
];
