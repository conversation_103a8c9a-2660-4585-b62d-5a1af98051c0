export default [
  { inputs: [], name: 'FeeOutOfBound', type: 'error' },
  {
    anonymous: false,
    inputs: [
      { indexed: false, internalType: 'address', name: 'token', type: 'address' },
      { indexed: false, internalType: 'uint16', name: 'escrowFee', type: 'uint16' },
      { indexed: false, internalType: 'uint16', name: 'wrapFee', type: 'uint16' },
      { indexed: false, internalType: 'uint16', name: 'dividendFee', type: 'uint16' },
      { indexed: false, internalType: 'uint16', name: 'redemptionFee', type: 'uint16' },
      { indexed: false, internalType: 'string', name: 'id', type: 'string' },
      { indexed: false, internalType: 'uint256', name: 'timeStamp', type: 'uint256' },
    ],
    name: 'AdminFeeUpdated',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      { indexed: false, internalType: 'address', name: 'newAdminWallet', type: 'address' },
      { indexed: false, internalType: 'string', name: 'id', type: 'string' },
      { indexed: false, internalType: 'uint256', name: 'timeStamp', type: 'uint256' },
    ],
    name: 'AdminWalletUpdated',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      { indexed: false, internalType: 'address', name: '_EquityConfigProxy', type: 'address' },
      { indexed: false, internalType: 'string', name: 'mappingValue', type: 'string' },
    ],
    name: 'EquityConfigCreated',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      { indexed: false, internalType: 'address', name: '_FundProxy', type: 'address' },
      { indexed: false, internalType: 'string', name: 'mappingValue', type: 'string' },
    ],
    name: 'FundCreated',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      { indexed: false, internalType: 'address', name: 'FundImplementation', type: 'address' },
      { indexed: false, internalType: 'address', name: 'EquityConfigImplementation', type: 'address' },
    ],
    name: 'ImplementationsUpdated',
    type: 'event',
  },
  { anonymous: false, inputs: [{ indexed: false, internalType: 'uint8', name: 'version', type: 'uint8' }], name: 'Initialized', type: 'event' },
  { anonymous: false, inputs: [{ indexed: false, internalType: 'address', name: 'MasterFactory', type: 'address' }], name: 'MasterFactoryUpdated', type: 'event' },
  {
    anonymous: false,
    inputs: [
      { indexed: false, internalType: 'address', name: 'UserAddress', type: 'address' },
      { indexed: false, internalType: 'address', name: 'OfferingAddress', type: 'address' },
      { indexed: false, internalType: 'string', name: 'salt', type: 'string' },
    ],
    name: 'Whitelisted',
    type: 'event',
  },
  {
    inputs: [
      { internalType: 'address', name: '_token', type: 'address' },
      { internalType: 'bytes', name: '_data', type: 'bytes' },
      { internalType: 'uint256', name: '_totalTokenSupply', type: 'uint256' },
      { internalType: 'string', name: 'mappingValue', type: 'string' },
    ],
    name: 'createEquityConfig',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      { internalType: 'address', name: '_token', type: 'address' },
      { internalType: 'bytes', name: '_data', type: 'bytes' },
      { internalType: 'uint256', name: '_totalTokenSupply', type: 'uint256' },
      { internalType: 'string', name: 'mappingValue', type: 'string' },
    ],
    name: 'createFund',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  { inputs: [], name: 'getAdminWallet', outputs: [{ internalType: 'address', name: '', type: 'address' }], stateMutability: 'view', type: 'function' },
  { inputs: [{ internalType: 'address', name: '_token', type: 'address' }], name: 'getAssetType', outputs: [{ internalType: 'uint8', name: '', type: 'uint8' }], stateMutability: 'view', type: 'function' },
  { inputs: [{ internalType: 'address', name: '_token', type: 'address' }], name: 'getDividendFee', outputs: [{ internalType: 'uint16', name: '', type: 'uint16' }], stateMutability: 'view', type: 'function' },
  { inputs: [{ internalType: 'address', name: '_token', type: 'address' }], name: 'getEscrowFee', outputs: [{ internalType: 'uint16', name: '', type: 'uint16' }], stateMutability: 'view', type: 'function' },
  { inputs: [{ internalType: 'address', name: '_token', type: 'address' }], name: 'getFund', outputs: [{ internalType: 'address', name: '', type: 'address' }], stateMutability: 'view', type: 'function' },
  { inputs: [], name: 'getMasterFactory', outputs: [{ internalType: 'address', name: '', type: 'address' }], stateMutability: 'view', type: 'function' },
  { inputs: [{ internalType: 'address', name: '_token', type: 'address' }], name: 'getRedemptionFee', outputs: [{ internalType: 'uint16', name: '', type: 'uint16' }], stateMutability: 'view', type: 'function' },
  { inputs: [{ internalType: 'address', name: '_token', type: 'address' }], name: 'getTokenTotalSupply', outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }], stateMutability: 'view', type: 'function' },
  { inputs: [{ internalType: 'address', name: '_token', type: 'address' }], name: 'getWrapFee', outputs: [{ internalType: 'uint16', name: '', type: 'uint16' }], stateMutability: 'view', type: 'function' },
  { inputs: [], name: 'implEquityConfig', outputs: [{ internalType: 'address', name: '', type: 'address' }], stateMutability: 'view', type: 'function' },
  { inputs: [], name: 'implFund', outputs: [{ internalType: 'address', name: '', type: 'address' }], stateMutability: 'view', type: 'function' },
  { inputs: [{ internalType: 'address', name: '_factory', type: 'address' }], name: 'init', outputs: [], stateMutability: 'nonpayable', type: 'function' },
  { inputs: [], name: 'masterFactory', outputs: [{ internalType: 'address', name: '', type: 'address' }], stateMutability: 'view', type: 'function' },
  {
    inputs: [
      { internalType: 'address', name: '_newWallet', type: 'address' },
      { internalType: 'string', name: '_actionID', type: 'string' },
    ],
    name: 'setAdminWallet',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      { internalType: 'address', name: '_token', type: 'address' },
      { internalType: 'uint16', name: '_escrowFee', type: 'uint16' },
      { internalType: 'uint16', name: '_wrapFee', type: 'uint16' },
      { internalType: 'uint16', name: '_dividendFee', type: 'uint16' },
      { internalType: 'uint16', name: '_redemptionFee', type: 'uint16' },
      { internalType: 'string', name: 'actionID', type: 'string' },
    ],
    name: 'setFee',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      { internalType: 'address', name: '_implFund', type: 'address' },
      { internalType: 'address', name: '_implEquityConfig', type: 'address' },
    ],
    name: 'setImpl',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  { inputs: [{ internalType: 'address', name: 'factory_', type: 'address' }], name: 'setMasterFactory', outputs: [], stateMutability: 'nonpayable', type: 'function' },
];
