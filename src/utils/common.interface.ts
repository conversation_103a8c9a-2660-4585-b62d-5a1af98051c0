/* eslint-disable @typescript-eslint/no-explicit-any */

/**
 * Standardized response payload interface for all API responses.
 * Ensures consistent response format across the platform with proper typing.
 * Used by response handlers and service methods for uniform API communication.
 *
 * @interface ResponsePayLoad
 * @template T - Type of the data payload (optional, defaults to void)
 * @description Standard API response structure with message, status, data, and error information
 *
 * // Error response without data
 * const errorResponse: ResponsePayLoad = {
 *   message: 'User not found',
 *   status: 404,
 *   error: true
 * };
 * ```
 */
export interface ResponsePayLoad<T = void> {
  /**
   * Human-readable message describing the response.
   * Provides context and information about the operation result.
   *
   * @type {string}
   */
  message: string;

  /**
   * HTTP status code indicating the response type.
   * Standard HTTP status codes (200, 400, 401, 404, 500, etc.)
   *
   * @type {number}
   */
  status: number;

  /**
   * Optional data payload containing response information.
   * Type-safe data based on the generic template parameter.
   *
   * @type {T}
   */
  data?: T;

  /**
   * Flag indicating whether the response represents an error.
   * True for error responses, false for successful operations.
   *
   * @type {boolean}
   */
  error: boolean;
}

/**
 * Joi validation result interface for request data validation.
 * Provides structured validation feedback with error details and processed values.
 * Used by middleware validation functions for request sanitization and error handling.
 *
 * @interface JoiValidationResult
 * @description Validation result structure from Joi schema validation
 *
 * // Validation error
 * const errorResult: JoiValidationResult = {
 *   error: true,
 *   value: null,
 *   message: 'Email format is invalid',
 *   status: 400
 * };
 * ```
 */
export interface JoiValidationResult {
  /**
   * Flag indicating whether validation failed.
   * @type {boolean}
   */
  error: boolean;

  /**
   * Validated and sanitized data value.
   * Contains processed data when validation succeeds.
   *
   * @type {any}
   */
  value: any;

  /**
   * Optional error message for validation failures.
   * Provides specific details about validation errors.
   *
   * @type {string}
   */
  message?: string;

  /**
   * Optional HTTP status code for validation errors.
   * Suggested status code for the validation failure response.
   *
   * @type {number}
   */
  status?: number;
}

/**
 * Decoded JWT token interface for authentication payload.
 * Represents the structure of decoded JWT tokens used for user authentication.
 * Contains user identification and session information.
 *
 * @interface DecodedToken
 * @description JWT token payload structure for authentication
 *
 */
export interface DecodedToken {
  /**
   * Unique identifier of the authenticated user.
   * @type {string}
   */
  userId: string;
}

/**
 * Message payload interface for real-time communications.
 * Used for socket communications and notification systems.
 * Provides structured messaging between users and system components.
 *
 * @interface MessagePayload
 * @description Real-time message structure for socket communications
 *
 */
export interface MessagePayload {
  /**
   * Target user ID for the message.
   * @type {string}
   */
  userId: string;

  /**
   * Message content or notification text.
   * @type {string}
   */
  message: string;
}

/**
 * User information interface for authentication context.
 * Contains comprehensive user details extracted from JWT tokens.
 * Used throughout the application for authorization and user context.
 *
 * @interface UserInfo
 * @description Complete user context information from authentication tokens
 *
 */
export interface UserInfo {
  /**
   * Unique user identifier.
   * @type {string}
   */
  userId: string;

  /**
   * User's email address.
   * @type {string}
   */
  email: string;

  /**
   * User's role type in the system.
   * @type {string}
   */
  userType: string;

  /**
   * KYC verification completion status.
   * @type {boolean}
   */
  isKyc: boolean;

  /**
   * Issuer role designation.
   * @type {boolean}
   */
  isIssuer: boolean;

  /**
   * Representative role designation.
   * @type {boolean}
   */
  isRepresentatives: boolean;

  /**
   * Token expiration timestamp.
   * @type {number}
   */
  exp: number;
}

/**
 * User type enumeration for role-based access control.
 * Defines the different user roles available in the tokenization platform.
 * Used for authorization, feature access, and user interface customization.
 *
 * @enum {string} UserTypeEnum
 * @description User role types for platform access control
 *
 * // Check user role
 * if (user.userType === UserTypeEnum.Representative) {
 *   enableRepresentativeFeatures();
 * }
 * ```
 */
export enum UserTypeEnum {
  /**
   * Individual or institutional investor role.
   * Can view offerings, make investments, and manage portfolio.
   */
  Investor = 'investor',

  /**
   * Institutional investor role.
   * Large-scale investors with enhanced features and limits.
   */
  Institution = 'institution',

  /**
   * Representative or agent role.
   * Can act on behalf of other users with delegated permissions.
   */
  Representative = 'representative',
}

/**
 * Entity type enumeration for business and individual classifications.
 * Defines legal entity types for KYC compliance and regulatory requirements.
 * Used for proper classification and compliance management.
 *
 * @enum {string} EntityTypeEnum
 * @description Legal entity types for KYC and compliance classification
 *
 */
export enum EntityTypeEnum {
  /**
   * Individual person entity type.
   */
  IndividualPerson = 'Individual (Person)',

  /**
   * Sole proprietorship business entity.
   */
  SoleProprietorship = 'Sole Proprietorship',

  /**
   * Partnership business entity.
   */
  Partnership = 'Partnership',

  /**
   * Corporation or company entity.
   */
  Corporation = 'Corporation (or Company)',

  /**
   * Limited Liability Company entity.
   */
  LLC = 'Limited Liability Company (LLC)',

  /**
   * Non-profit organization entity.
   */
  NonProfitOrganization = 'Non-Profit Organization',

  /**
   * Government entity or agency.
   */
  GovernmentEntity = 'Government Entity',

  /**
   * Trust entity type.
   */
  Trust = 'Trust',

  /**
   * Joint venture entity.
   */
  JointVenture = 'Joint Venture',

  /**
   * Association entity type.
   */
  Association = 'Association',

  /**
   * Cooperative business entity.
   */
  Cooperative = 'Cooperative',

  /**
   * Publicly traded company.
   */
  PubliclyTradedCompany = 'Publicly Traded Company',

  /**
   * Private company entity.
   */
  PrivateCompany = 'Private Company',

  /**
   * Educational institution entity.
   */
  EducationalInstitution = 'Educational Institution',

  /**
   * Foundation entity type.
   */
  Foundation = 'Foundation',
}

/**
 * Income source enumeration for investor classification and compliance.
 * Defines various sources of income for KYC verification and risk assessment.
 * Used for regulatory compliance and investor suitability analysis.
 *
 * @enum {string} IncomeSourceEnum
 * @description Income source types for investor verification and compliance
 */
export enum IncomeSourceEnum {
  /**
   * Income from employment or salary.
   */
  EmploymentIncome = 'Employment Income',

  /**
   * Income from business operations.
   */
  BusinessIncome = 'Business Income',

  /**
   * Income from investments and returns.
   */
  InvestmentIncome = 'Investment Income',

  /**
   * Personal savings and accumulated wealth.
   */
  Savings = 'Savings',

  /**
   * Inherited wealth or assets.
   */
  Inheritance = 'Inheritance',

  /**
   * Gifted money or assets.
   */
  Gift = 'Gift',

  /**
   * Proceeds from property or asset sales.
   */
  SaleOfPropertyOrAssets = 'Sale of Property or Assets',

  /**
   * Borrowed funds or credit.
   */
  LoanOrCredit = 'Loan or Credit',

  /**
   * Pension or retirement fund distributions.
   */
  PensionRetirementFund = 'Pension/Retirement Fund',

  /**
   * Dividend payments from investments.
   */
  Dividends = 'Dividends',

  /**
   * Government assistance or benefit payments.
   */
  GovernmentAssistanceBenefits = 'Government Assistance/Benefits',
}

/**
 * Asset type enumeration for tokenized investment categories.
 * Defines the main categories of assets that can be tokenized on the platform.
 * Used for offering classification, risk assessment, and regulatory compliance.
 *
 * @enum {string} AssetTypeEnum
 * @description Asset categories for tokenized investment offerings
 *
 */
export enum AssetTypeEnum {
  /**
   * Real estate properties and developments.
   */
  RealEstate = 'Real Estate',

  /**
   * Equity stakes in companies or ventures.
   */
  Equity = 'Equity',

  /**
   * Debt instruments and fixed-income securities.
   */
  Debt = 'Debt',

  /**
   * Other asset types not covered by main categories.
   */
  Others = 'Others',
}

/**
 * Blockchain network enumeration for token deployment.
 * Defines supported blockchain networks for token creation and management.
 * Currently focused on Base network for optimal performance and cost.
 *
 * @enum {string} BlockchainEnum
 * @description Supported blockchain networks for token deployment
 */
export enum BlockchainEnum {
  /**
   * Base blockchain network for token deployment.
   */
  Base = 'Base',
}

/**
 * Offering type enumeration for investment structures.
 * Defines different types of token offerings and investment structures.
 * Used for regulatory compliance and offering classification.
 *
 * @enum {string} OfferingTypeEnum
 * @description Types of token offerings and investment structures
 */
export enum OfferingTypeEnum {
  /**
   * Public offering open to all eligible investors.
   */
  PublicOffering = 'Public Offering',

  /**
   * Private placement for accredited investors.
   */
  PrivatePlacement = 'Private Placement',

  /**
   * Share capital management for existing entities.
   */
  ShareCapitalManagement = 'Share Capital Management',
}

/**
 * Token standard enumeration for blockchain token types.
 * Defines supported token standards for regulatory compliance.
 * Currently focused on ERC-3643 for compliant security tokens.
 *
 * @enum {string} StandardTokenEnum
 * @description Supported token standards for compliant tokenization
 */
export enum StandardTokenEnum {
  /**
   * ERC-3643 standard for compliant security tokens.
   */
  ERC3643 = 'ERC-3643',
}

/**
 * Property type enumeration for real estate asset classification.
 * Defines different types of real estate properties for investment.
 * Used for asset-specific analysis and risk assessment.
 *
 * @enum {string} PropertyTypeEnum
 * @description Real estate property types for investment classification
 */
export enum PropertyTypeEnum {
  /**
   * Commercial real estate properties.
   */
  Commercial = 'Commercial',

  /**
   * Residential properties and developments.
   */
  Residential = 'Residential',

  /**
   * Office buildings and complexes.
   */
  Office = 'Office',

  /**
   * Multifamily residential properties.
   */
  Multifamily = 'Multifamily',

  /**
   * Retail properties and shopping centers.
   */
  Retail = 'Retail',

  /**
   * Hotel and hospitality properties.
   */
  Hotel = 'Hotel',

  /**
   * Industrial properties and warehouses.
   */
  Industrial = 'Industrial',
  Others = 'Others',
}

export enum sumSubKycStatusEnum {
  NOT_STARTED = 'NOT_STARTED',
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  DECLINED = 'DECLINED',
  HOLD = 'HOLD',
  PRECHECKRED = 'PRECHECKRED',
}

export enum KycStatusEnum {
  REQUESTED = 'REQUESTED',
  STARTED = 'STARTED',
  IN_PROGRESS = 'IN_PROGRESS',
  NOT_STARTED = 'NOT_STARTED',
  PENDING = 'PENDING',
  REJECTED = 'REJECTED',
  APPROVED = 'APPROVED',
  RESUBMIT = 'RESUBMIT',
  MINTED = 'MINTED',
  DELETED = 'DELETED',
  REVIEW = 'REVIEW',
}

export enum IssuerStatusEnum {
  NOT_APPLIED = 'NOT_APPLIED',
  PENDING = 'PENDING',
  REJECTED = 'REJECTED',
  APPROVED = 'APPROVED',
  BLOCKED = 'BLOCKED',
}

export enum notificationEnum {
  CREATE_OFFERING = 'create-offering',
  KYC = 'kyc',
  OFFERING_DEPLOYED_REAL_ESTATE = 'offering-deployed-realEstate',
  OFFERING_DEPLOYED_EQUITY = 'offering-deployed-equity',
  OFFERING_REJECTED = 'offering-rejected',
  INVESTMENT_RECEIVED = 'investment-received',
  WALLET_WHITELIST = 'wallet-whitelist',
  SUBSCRIBE = 'subscribe',
  DIVIDEND_RECORD_DATE = 'dividend-record-date-set',
}

export enum offeringStatusEnum {
  STARTED = 'STARTED',
  // MINTED = 'MINTED',
  IN_PROGRESS = 'IN_PROGRESS',
  PENDING = 'PENDING',
  REJECTED = 'REJECTED',
  APPROVED = 'APPROVED',
  RESUBMIT = 'RESUBMIT',
  DELETED = 'DELETED',
  REVIEW = 'REVIEW',
}
export enum orderStatusEnum {
  STARTED = 'STARTED',
  MINTED = 'MINTED',
  BURN = 'BURN',
  FREEZE = 'FREEZE',
  UNFREEZE = 'UNFREEZE',
  TRANSFER = 'TRANSFER',
  PENDING = 'PENDING',
  REDEEM = 'REDEEM',
  REJECTED = 'REJECTED',
  APPROVED = 'APPROVED',
  RESUBMIT = 'RESUBMIT',
  DELETED = 'DELETED',
  TRANSFER_FROM = 'TRANSFER_FROM',
  TRANSFER_TO = 'TRANSFER_TO',
  CONVERT_FROM = 'CONVERT_FROM',
  CONVERT_TO = 'CONVERT_TO',
  CONVERT = 'CONVERT',
  PRICE_CHANGE = 'PRICE_CHANGE',
  PRICE_CANCELLED = 'CANCELLED',
  INITIAL_ORDER = 'INITIAL_ORDER',
}
export enum transferStatusEnum {
  PENDING = 'PENDING',
  REJECTED = 'REJECTED',
  APPROVED = 'APPROVED',
}

export enum whiteListEnum {
  STARTED = 'STARTED',
  IN_PROGRESS = 'IN_PROGRESS',
  PENDING = 'PENDING',
  REJECTED = 'REJECTED',
  APPROVED = 'APPROVED',
  RESUBMIT = 'RESUBMIT',
}

export enum WalletTypeEnum {
  METAMASK_WALLET = 'MetamaskWallet',
  TRUST_WALLET = 'TrustWallet',
  WALLET_CONNECT = 'WalletConnect',
  FIREBLOCKS_WALLET = 'FireblocksWallet',
  SMART_WALLET = 'SmartWallet',
}

export const otpTypeEnum = { SIGN_UP: 'sign_up', LOGIN: 'login', FORGOT: 'forgot', DISABLE_2FA: 'tfa', DISABLE_OTP: 'disable', VERIFICATION_OTP: 'verification' };

export enum otpMethodsEnum {
  EMAIL = 'Email',
  MOBILE = 'Mobile',
}

export enum DocumentTypesEnum {
  FRONT_ID_CARD = 'frontId',
  BACK_ID_CARD = 'backId',
  NATIONAL_ID = 'proofOfResidence',
  PROFILE = 'profile',
  DIVIDEND = 'DIVIDEND',
  PROPOSAL = 'proposal',
}
export enum DocumentFolderTypesEnum {
  USER = 'users',
  OFFERING = 'offering',
  DIVIDEND = 'DIVIDEND',
  PROPOSAL = 'proposal',
}
export enum offeringDocumentTypesEnum {
  eSign = 'eSign',
  pitchDeck = 'pitchDeck',
  confidentialInformationMemorendum = 'confidentialInformationMemorendum',
  landRegistration = 'landRegistration',
  titleDocs = 'titleDocs',
  bankApproval = 'bankApproval',
  encumbranceCertificate = 'encumbranceCertificate',
  propertyTaxReceipt = 'propertyTaxReceipt',
  articlesOfAssociation = 'articlesOfAssociation',
  operatingAgreement = 'operatingAgreement',
  taxAssignmentLetter = 'taxAssignmentLetter',
  certificateOfRegistration = 'certificateOfRegistration',
  registerOfManagers = 'registerOfManagers',
  icon = 'icon',
  companyLogo = 'companyLogo',
  offeringCoverImage = 'offeringCoverImage',
  url = 'url',
  customImage = 'customImage',
}
export interface PromiseResolve {
  status: number;
  error: boolean;
  message: string;
  data?: any;
}

export interface AuthTokenResponseType {
  iat: number;
  exp: number;
  aud: string;
  iss: string;
}

export interface PutCommandResponse {
  endPoint: string;
  keyName: string;
}
export const enum queueMessageTypeEnum {
  USER = 'user',
  OFFERING = 'offering',
  REQ_OFFERING = 'reqOffering',
  REQ_WHITELIST = 'reqWhitelist',
  ORDER = 'order',
  WHITELIST = 'whitelist',
  TRANSFER = 'ForceTransferred',
}

export interface RedisSetOptions {
  EX?: number; // Optional expiration time in seconds
  NX: boolean; // Optional flag to set the key only if it does not already exist
}

export interface IPagination {
  page?: number;
  limit?: number;
  sort?: any;
  search?: any;
}

export enum WebhookTypeEnum {
  APPLICANT_CREATED = 'applicantCreated',
  APPLICANT_PENDING = 'applicantPending',
  APPLICANT_REVIEWED = 'applicantReviewed',
  APPLICANT_ON_HOLD = 'applicantActionOnHold',
  APPLICANT_PRECHECKED = 'applicantPrechecked',
}

export enum paymentTypeEnum {
  USDC = 'USDC',
  USDT = 'USDT',
  ERC20 = 'ERC20',
  ERC3643 = 'ERC3643',
  // BANK_TRANSFER = 'Bank Transfer',
}
