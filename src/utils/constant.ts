/* eslint-disable no-useless-escape */

/**
 * Length requirement for OTP (One-Time Password) codes used in authentication.
 * Standard 6-digit OTP format for security verification processes.
 *
 * @constant {number} otpLength - Required length for OTP codes
 * @default 6
 *
 * // Validate OTP length
 * if (userOtp.length !== otpLength) {
 *   throw new Error('Invalid OTP format');
 * }
 * ```
 */
export const otpLength = 6;

/**
 * Cooldown period in seconds between OTP generation requests.
 * Prevents spam and abuse of OTP generation endpoints for security.
 *
 * @constant {number} coolDownTimeInSeconds - Minimum time between OTP requests
 * @default 60
 *
 */
export const coolDownTimeInSeconds = 60;

/**
 * Maximum allowed length for user name fields.
 * Ensures reasonable name length limits for UI and database constraints.
 *
 * @constant {number} nameMaxLength - Maximum characters allowed in name fields
 * @default 70
 *
 */
export const nameMaxLength = 70;

/**
 * Minimum required length for user name fields.
 * Ensures meaningful name entries and prevents overly short inputs.
 *
 * @constant {number} nameMinLength - Minimum characters required in name fields
 * @default 3
 *
 */
export const nameMinLength = 3;

/**
 * Maximum allowed length for email address fields.
 * Prevents excessively long email addresses that could cause database issues.
 *
 * @constant {number} emailMaxLength - Maximum characters allowed in email fields
 * @default 200
 *
 */
export const emailMaxLength = 200;

/**
 * Maximum allowed length for password fields.
 * Balances security with usability for password requirements.
 *
 * @constant {number} passwordMaxLength - Maximum characters allowed in passwords
 * @default 50
 *
 */
export const passwordMaxLength = 50;

/**
 * Minimum required length for password fields.
 * Ensures basic password security requirements are met.
 *
 * @constant {number} passwordMinLength - Minimum characters required in passwords
 * @default 2
 *
 */
export const passwordMinLength = 2;

/**
 * Regular expression pattern for strong password validation.
 * Requires at least one lowercase letter, uppercase letter, digit, and special character.
 * Ensures robust password security for user account protection.
 *
 * @constant {RegExp} passwordPattern - Password strength validation regex
 * @pattern ^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).+$
 *
 * @description
 * Password requirements:
 * - At least one lowercase letter (a-z)
 * - At least one uppercase letter (A-Z)
 * - At least one digit (0-9)
 * - At least one special character or underscore
 * - No minimum length enforced by pattern (handled by separate constant)
 *
 * // Examples of valid passwords:
 * passwordPattern.test('MyPass123!'); // true
 * passwordPattern.test('Secure_2024'); // true
 * passwordPattern.test('Strong#Pass1'); // true
 *
 * // Examples of invalid passwords:
 * passwordPattern.test('password'); // false (no uppercase, no digit, no special)
 * passwordPattern.test('PASSWORD'); // false (no lowercase, no digit, no special)
 * passwordPattern.test('Password123'); // false (no special character)
 * ```
 */
export const passwordPattern = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).+$/;

/**
 * Regular expression pattern for name field validation.
 * Allows only alphabetic characters and spaces for name entries.
 * Ensures clean name data entry without numbers or special characters.
 *
 * @constant {RegExp} namePattern - Name field validation regex
 * @pattern ^[A-Za-z\s]+$
 *
 * @description
 * Name validation rules:
 * - Only alphabetic characters (A-Z, a-z)
 * - Spaces allowed for first and last names
 * - No numbers, special characters, or symbols
 * - Case insensitive matching
 *
 * // Valid names:
 * namePattern.test('John Doe'); // true
 * namePattern.test('Mary Jane Smith'); // true
 * namePattern.test('Jean Pierre'); // true
 *
 * // Invalid names:
 * namePattern.test('John123'); // false (contains numbers)
 * namePattern.test('Mary-Jane'); // false (contains hyphen)
 * namePattern.test('John.Doe'); // false (contains period)
 * ```
 */
export const namePattern = /^[A-Za-z\s]+$/;

/**
 * Regular expression pattern for alphanumeric text validation with international support.
 * Allows letters, numbers, spaces, accented characters, apostrophes, and hyphens.
 * Suitable for flexible text fields requiring international character support.
 *
 * @constant {RegExp} alphanumericPattern - Flexible alphanumeric validation regex
 * @pattern ^[A-Za-z0-9À-ÖØ-öø-ÿ\s''\-]+$
 *
 * @description
 * Alphanumeric validation rules:
 * - Basic Latin letters (A-Z, a-z)
 * - Numbers (0-9)
 * - Extended Latin characters with accents (À-ÖØ-öø-ÿ)
 * - Spaces for word separation
 * - Apostrophes (both straight and curly)
 * - Hyphens for compound words
 *
 * // Valid text:
 * alphanumericPattern.test('Café du Marché'); // true
 * alphanumericPattern.test("O'Connor Building"); // true
 * alphanumericPattern.test('Jean-Pierre 123'); // true
 * alphanumericPattern.test('Zürich Office'); // true
 *
 * // Invalid text:
 * alphanumericPattern.test('Text with @symbol'); // false
 * alphanumericPattern.test('Price: $100'); // false
 * alphanumericPattern.test('<EMAIL>'); // false
 * ```
 */
export const alphanumericPattern = /^[A-Za-z0-9À-ÖØ-öø-ÿ\s''\-]+$/;

/**
 * Regular expression pattern for OTP code validation.
 * Ensures OTP codes contain exactly 6 numeric digits.
 * Validates format consistency for two-factor authentication codes.
 *
 * @constant {RegExp} otpPattern - OTP format validation regex
 * @pattern ^[0-9]{6}$
 *
 * @description
 * OTP validation rules:
 * - Exactly 6 characters required
 * - Only numeric digits (0-9) allowed
 * - No letters, spaces, or special characters
 * - Fixed length for security consistency
 *
 * // Valid OTP codes:
 * otpPattern.test('123456'); // true
 * otpPattern.test('000000'); // true
 * otpPattern.test('999999'); // true
 *
 * // Invalid OTP codes:
 * otpPattern.test('12345'); // false (too short)
 * otpPattern.test('1234567'); // false (too long)
 * otpPattern.test('12345a'); // false (contains letter)
 * otpPattern.test('123 456'); // false (contains space)
 * ```
 */
export const otpPattern = /^[0-9]{6}$/;

/**
 * Maximum number of previous passwords to store for password history validation.
 * Prevents users from reusing recent passwords to enhance security.
 *
 * @constant {number} maxPasswordHistory - Number of previous passwords to remember
 * @default 5
 *
 * for (const oldPassword of recentPasswords) {
 *   if (await bcrypt.compare(newPassword, oldPassword.hash)) {
 *     throw new Error('Cannot reuse recent passwords');
 *   }
 * }
 *
 * // Store new password and maintain history limit
 * await storePasswordHistory(userId, hashedNewPassword, maxPasswordHistory);
 * ```
 */
export const maxPasswordHistory = 5;

/**
 * Maximum number of KYC (Know Your Customer) verification attempts allowed.
 * Limits KYC submission attempts to prevent abuse and maintain compliance standards.
 *
 * @constant {number} maxKycAttempt - Maximum KYC verification attempts per user
 * @default 3
 *
 * // Increment attempt counter on submission
 * await incrementKycAttempts(userId);
 * ```
 */
export const maxKycAttempt = 3;

/**
 * Regular expression pattern for URL validation with optional protocol.
 * Validates web URLs for document links, website references, and external resources.
 * Supports both HTTP and HTTPS protocols with flexible subdomain structures.
 *
 * @constant {RegExp} urlPattern - URL format validation regex
 * @pattern ^(https?:\/\/)?([a-zA-Z0-9-]+\.)?[a-zA-Z0-9-]{1,}\.[a-zA-Z]{2,}(\/.*)?$
 *
 * @description
 * URL validation rules:
 * - Optional protocol (http:// or https://)
 * - Optional subdomain with alphanumeric characters and hyphens
 * - Required domain name with alphanumeric characters and hyphens
 * - Required top-level domain with at least 2 letters
 * - Optional path, query parameters, and fragments

 */
export const urlPattern = /^(https?:\/\/)?([a-zA-Z0-9-]+\.)?[a-zA-Z0-9-]{1,}\.[a-zA-Z]{2,}(\/.*)?$/;
