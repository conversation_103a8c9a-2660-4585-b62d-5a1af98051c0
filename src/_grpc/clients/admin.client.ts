import CONFIG from '../../config/env';
import logger from '../../helpers/logging/logger.helper';
import * as path from 'path';
import * as grpc from '@grpc/grpc-js';
import * as protoLoader from '@grpc/proto-loader';

const PROTO_PATH = path.join(__dirname, '../proto/user.proto');
const PROTO_OPTIONS = { keepCase: true, longs: String, enums: String, arrays: true };

class AdminGrpcClient {
  public client: any;

  constructor() {
    this.connectUserClient();
  }

  public async connectUserClient() {
    const host = CONFIG.GRPC.ADMIN_SERVICE_GRPC_CONTAINER_NAME;
    const port = CONFIG.GRPC.ADMIN_SERVICE_GRPC_PORT;
    const isSsl = CONFIG.GRPC.GRPC_SSL;

    // Load the protobuf definition
    const packageDefinition = protoLoader.loadSync(PROTO_PATH, PROTO_OPTIONS);

    // Load the package definition into gRPC
    const grpcObject = grpc.loadPackageDefinition(packageDefinition) as any;

    // Get the service definition from the loaded gRPC object
    const adminGrpcService = grpcObject.user.AdminGrpcService;

    // Create the gRPC client instance using the service constructor
    this.client = new adminGrpcService(`${host}:${port}`, isSsl ? grpc.credentials.createSsl() : grpc.credentials.createInsecure());

    logger.info(`Admin Service Client running at - ${host}:${port}`);
  }
}

export default new AdminGrpcClient();
