import { Request, Response } from 'express';
import DocuSignApi from './service'; // Assuming DocuSignApi is imported from the service file
import { ResponseHandler } from '../../helpers/response.helper';
import { RES_MSG, RESPONSES } from '../../utils/responseUtils';
import logger from '../../helpers/logging/logger.helper';
/**
 * Generates a DocuSign templateId and returns it in the response.
 * @param {Request} req - Request object
 * @param {Response} res - Response object
 */
export async function generateTemplateId(req: Request, res: Response) {
  try {
    const { offeringId } = req.body;
    const generatedTemplate = await DocuSignApi.generateDocuSignTemplateId(offeringId);

    return ResponseHandler.success(res, { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS, data: generatedTemplate });
  } catch (error) {
    logger.error('Error generating template Id:', error);

    // Send error response
    return ResponseHandler.error(res, { message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR, error: true });
  }
}
/**
 * Generates an embedded signing URL and returns it in the response.
 * @param {Request} req - Request object
 * @param {Response} res - Response object
 */
export async function generateEmbeddedSigningUrl(req: Request, res: Response) {
  try {
    const { offeringId, orderId } = req.body;
    const { userId } = req.userInfo;

    const generatedEmbeddedUrl = await DocuSignApi.generateEmbeddedSignedUrl(offeringId, orderId, userId);

    return ResponseHandler.success(res, { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS, data: generatedEmbeddedUrl });
  } catch (error) {
    logger.error('Error generating embedded signing URL:', error);

    // Send error response
    return ResponseHandler.error(res, { message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR, error: true });
  }
}

/**
 * Handles DocuSign webhook event.
 * @param {Request} req - Request object containing the webhook event body
 * @param {Response} res - Response object
 */ export async function docusignWebhook(req: Request, res: Response) {
  try {
    await DocuSignApi.handleWebhookEvent(req.body);

    return ResponseHandler.success(res, { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS, data: null });
  } catch (error) {
    logger.error('Error while updating using webhook', error);

    // Send error response
    return ResponseHandler.error(res, { message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR, error: true });
  }
}
