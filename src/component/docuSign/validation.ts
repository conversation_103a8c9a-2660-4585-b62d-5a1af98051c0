import * as <PERSON><PERSON> from 'joi';
import { JoiValidationResult } from '../../utils/common.interface';
import { RES_MSG, RESPONSES } from '../../utils/responseUtils';
import * as joiOptions from '../../helpers/joi.helper';
import logger from '../../helpers/logging/logger.helper';

export default class DocusignValidation {
  // Subscribe Validation
  static async subscribeValidation(params: Record<string, any>): Promise<JoiValidationResult> {
    try {
      const schema = Joi.object({
        offeringId: Joi.string().length(24).hex().required().messages({ 'string.length': 'Invalid Offering Id', 'string.hex': '"offeringId" must be a valid MongoDB ObjectId' }),
        orderId: Joi.string().length(24).hex().required().messages({ 'string.length': 'Invalid Offering Id', 'string.hex': '"offeringId" must be a valid MongoDB ObjectId' }),
      });
      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'er');

      return { error: true, value: '', message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR };
    }
  }

  static async DocusignWebhookValidation(params: Record<string, any>): Promise<JoiValidationResult> {
    try {
      const schema = Joi.object({
        event: Joi.string().valid('envelope-completed', 'envelope-delivered', 'envelope-voided', 'envelope-declined', 'recipient-completed').required().messages({ 'any.required': 'Event is required', 'any.only': 'Invalid event' }),
        data: this.getDocuSignWebhookDataItemSchema().required().messages({ 'any.required': 'Data is required' }),
      });
      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      return { error: true, value: '', message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR };
    }
  }

  // DocuSign Generate Template Request
  static getDocuSignGenerateTemplateRequestSchema() {
    return Joi.object({
      fileKey: Joi.string().required().messages({ 'any.required': 'File key is required', 'string.empty': 'File key cannot be empty' }),
      name: Joi.string().required().messages({ 'any.required': 'Name is required', 'string.empty': 'Name cannot be empty' }),
    });
  }

  // Recipient Item Schema
  static getDocuSignGenerateEmbeddedSigningUrlRecipientItemSchema() {
    return Joi.object({
      email: Joi.string().email().required().messages({ 'string.email': 'Invalid email format', 'any.required': 'Email is required' }),
      name: Joi.string().required().messages({ 'any.required': 'Name is required', 'string.empty': 'Name cannot be empty' }),
    });
  }

  // Generate Embedded Signing URL Request Schema
  static getDocuSignGenerateEmbeddedSigningUrlRequestSchema() {
    return Joi.object({
      fileKey: Joi.string().required().messages({ 'any.required': 'File key is required' }),
      investorEmail: Joi.string().email().required().messages({ 'string.email': 'Invalid email format', 'any.required': 'Investor email is required' }),
      investorName: Joi.string().required().messages({ 'any.required': 'Investor name is required' }),
      investorId: Joi.string().required().messages({ 'any.required': 'Investor ID is required' }),
      templateId: Joi.string().required().messages({ 'any.required': 'Template ID is required' }),
      offeringId: Joi.string().required().messages({ 'any.required': 'Offering ID is required' }),
      recipients: Joi.array().items(this.getDocuSignGenerateEmbeddedSigningUrlRecipientItemSchema()).optional().messages({ 'array.base': 'Recipients must be an array' }),
    });
  }

  // Generate Access Token Response Schema
  static getDocuSignGenerateAccessTokenResponseSchema() {
    return Joi.object({ access_token: Joi.string().required().messages({ 'any.required': 'Access token is required' }) });
  }

  // User Info Account Item Schema
  static getDocuSignUserInfoAccountItemSchema() {
    return Joi.object({
      accountId: Joi.string().email().required().messages({ 'any.required': 'Account ID is required', 'string.email': 'Account ID must be a valid email' }),
      baseUri: Joi.string().required().messages({ 'any.required': 'Base URI is required' }),
      accountName: Joi.string().required().messages({ 'any.required': 'Account name is required' }),
      isDefault: Joi.string().optional(),
    });
  }

  // User Info Response Schema
  static getDocuSignUserInfoResponseSchema() {
    return Joi.object({
      email: Joi.string().email().required().messages({ 'any.required': 'Email is required', 'string.email': 'Invalid email format' }),
      name: Joi.string().required().messages({ 'any.required': 'Name is required' }),
      sub: Joi.string().required().messages({ 'any.required': 'Sub is required' }),
      accounts: Joi.array().items(this.getDocuSignUserInfoAccountItemSchema()).required().messages({ 'any.required': 'Accounts are required' }),
    });
  }

  // Generate Embedded URL Response Schema
  static getDocuSignGenerateEmbeddedUrlResponseSchema() {
    return Joi.object({
      redirectUrl: Joi.string().required().messages({ 'any.required': 'Redirect URL is required' }),
      userId: Joi.string().required().messages({ 'any.required': 'User ID is required' }),
      signingDocumentId: Joi.string().guid().required().messages({ 'any.required': 'Signing Document ID is required', 'string.guid': 'Invalid Signing Document ID' }),
    });
  }

  // Webhook Custom Field Item Schema
  static getDocuSignWebhookDataEnvelopeSummaryCustomFieldItemSchema() {
    return Joi.object({
      fieldId: Joi.string().required().messages({ 'any.required': 'Field ID is required' }),
      name: Joi.string().required().messages({ 'any.required': 'Name is required' }),
      value: Joi.string().required().messages({ 'any.required': 'Value is required' }),
    });
  }

  // Webhook Custom Field Schema
  static getDocuSignWebhookDataEnvelopeSummaryCustomFieldSchema() {
    return Joi.object({ textCustomFields: Joi.array().items(this.getDocuSignWebhookDataEnvelopeSummaryCustomFieldItemSchema()).required().messages({ 'any.required': 'Text custom fields are required' }) });
  }

  // Webhook Envelope Summary Item Schema
  static getDocuSignWebhookDataEnvelopeSummaryItemSchema() {
    return Joi.object({
      status: Joi.string().required().messages({ 'any.required': 'Status is required' }),
      customFields: this.getDocuSignWebhookDataEnvelopeSummaryCustomFieldSchema().required().messages({ 'any.required': 'Custom fields are required' }),
    });
  }

  // Webhook Data Item Schema
  static getDocuSignWebhookDataItemSchema() {
    return Joi.object({
      envelopeId: Joi.string().required().messages({ 'any.required': 'Envelope ID is required' }),
      accountId: Joi.string().required().messages({ 'any.required': 'Account ID is required' }),
      envelopeSummary: this.getDocuSignWebhookDataEnvelopeSummaryItemSchema().required().messages({ 'any.required': 'Envelope summary is required' }),
    });
  }
}
