/* eslint-disable camelcase */
import * as docuSign from 'docusign-esign';
import { Types } from 'mongoose';
import env from '../../config/env';
import { offeringSchema as Offering } from '../offerings/models/offerings.model';
import UserService from '../userAuthentications/service'; // Assuming UserService is the correct path

import {
  DocuSignGenerateEnvelopePayload,
  DocuSignSupportedCheckBoxTagsEnum,
  DocuSignSupportedRadioGroupTagsEnum,
  DocuSignSupportedSignEnum,
  DocuSignSupportedTagsEnum,
  DocuSignUrlPayload,
  DocuSignWebhookDataItem,
  DocuSignWebHookEventsEnum,
  DocuSignWebhookRequest,
  zodDocuSignGenerateAccessToken,
  zodDocuSignUserInfo,
  zodDocuSignUserInfoApiResponse,
} from './docuSign.types';
import CloudHelper from '../../helpers/cloud.helper';
import { whitelistSchema } from '../offerings/models/whitelist.model';
import logger from '../../helpers/logging/logger.helper';

/**
 * DocuSign API integration service for digital signature management in tokenized asset offerings.
 * Provides comprehensive digital signature capabilities including document template creation,
 * embedded signing workflows, webhook processing, and signature status tracking.
 * Integrates with DocuSign eSignature API for legally binding digital signatures.
 *
 * @class DocuSignApi
 * @description Complete digital signature service for tokenization platform compliance
 *
 */
export default class DocuSignApi {
  /**
   * Current status of the DocuSign API service.
   * Indicates whether the service is available for document processing.
   *
   * @public
   * @type {string}
   * @default "Enabled"
   */
  public status: string = 'Enabled';

  /**
   * JWT token lifetime in seconds for DocuSign API authentication.
   * Default 30 minutes for security and API limits compliance.
   *
   * @static
   * @type {number}
   * @default 1800 (30 minutes)
   */
  static jwtLife: number = 30 * 60;

  /**
   * Required OAuth scopes for DocuSign API operations.
   * Defines permissions needed for signature and impersonation capabilities.
   *
   * @static
   * @type {string[]}
   * @default ["signature", "impersonation"]
   */
  static jwtScopes: string[] = ['signature', 'impersonation'];

  /**
   * Instance-level access token for DocuSign API authentication.
   * Used for API requests requiring authentication.
   *
   * @public
   * @type {string}
   * @default ""
   */
  public accessToken: string = '';

  /**
   * Static access token for shared DocuSign API operations.
   * Provides class-level authentication for static methods.
   *
   * @static
   * @type {string}
   */
  static accessToken: string;

  /**
   * Creates and configures a DocuSign API client with proper OAuth settings.
   * Initializes the client with environment-specific OAuth base path for authentication.
   * Provides foundation for all DocuSign API operations and service calls.
   *
   * @static
   * @async
   * @method getApiClient
   * @returns {Promise<docuSign.ApiClient>} Configured DocuSign API client instance
   * @throws {Error} Throws error if API client configuration fails
   *
   * @description
   * API client configuration:
   * - Sets OAuth base path from environment configuration
   * - Removes protocol prefix for proper API endpoint setup
   * - Configures authentication endpoints for token management
   * - Provides base client for all DocuSign service operations
   */
  static async getApiClient() {
    const client = new docuSign.ApiClient();
    client.setOAuthBasePath(env.DOCUSIGN.OAuthServer.replace('https://', ''));

    return client;
  }

  /**
   * Generates a DocuSign document template for tokenized offering documentation.
   * Creates reusable templates from offering documents for streamlined signing workflows.
   * Validates offering existence and document availability before template creation.
   * Provides template ID for subsequent signature requests and document processing.
   *
   * @static
   * @async
   * @method generateDocuSignTemplateId
   * @param {string} offeringId - Unique identifier of the tokenized offering
   * @returns {Promise<string>} Generated DocuSign template ID for the offering
   * @throws {Error} Throws error if offering not found or template creation fails
   *
   * @description
   * Template generation process:
   * 1. Validates offering existence and accessibility
   * 2. Retrieves eSign document from offering data
   * 3. Creates DocuSign template from document content
   * 4. Configures template with offering-specific metadata
   * 5. Returns template ID for signature workflow integration
   *
   * Template features:
   * - Reusable document templates for consistent signing
   * - Offering-specific branding and metadata
   * - Automated field mapping and signature placement
   * - Integration with platform document management
   * - Compliance with regulatory signature requirements
   *

   * @see {@link generateEmbeddedSignedUrl} For using generated templates in signature workflows
   */
  static async generateDocuSignTemplateId(offeringId: string) {
    try {
      // Fetch offering details
      const offeringD = await Offering.findById(new Types.ObjectId(offeringId));
      if (!offeringD) {
        throw new Error('Offering ID not found!');
      }

      // Check for the eSign document key
      const fileKey = offeringD.documents?.eSign;
      if (!fileKey) {
        throw new Error('Document not found for eSigning!');
      }
      // Generate the template ID
      const generateId = await DocuSignApi.generateEnvelopeTemplate({ fileKey, name: offeringD.projectDetails?.offeringName });
      // Update the offering with the new template ID
      // offeringD.template_id = generateId.templateId;
      // await offeringD.save();

      return generateId.templateId;
    } catch {
      throw new Error('Error in generating docusign templateId!');
    }
  }

  /**
   * Generates embedded signing URLs for investor document signature workflows.
   * Creates secure, embedded signing sessions within the platform interface.
   * Validates offering template availability and investor authorization.
   * Provides seamless signature experience without external DocuSign redirects.
   *
   * @static
   * @async
   * @method generateEmbeddedSignedUrl
   * @param {string} offeringId - Unique identifier of the tokenized offering
   * @param {string} orderId - Unique identifier of the investment order
   * @param {string} userId - Unique identifier of the signing investor
   * @returns {Promise<DocuSignGenerateEmbeddedUrlResponse>} Embedded signing URL and session details
   * @throws {Error} Throws error if offering, template, or user not found
   *
   * @description
   * Embedded signing process:
   * 1. Validates offering and template ID availability
   * 2. Retrieves investor details and verification status
   * 3. Creates DocuSign envelope with offering documents
   * 4. Generates embedded signing URL for platform integration
   * 5. Returns secure signing session details
   *
   * Security features:
   * - User authentication and authorization validation
   * - Session-based signing with time limits
   * - Embedded signing prevents external redirects
   * - Audit trail integration with platform logging
   * - Investor identity verification before signing
   *
   * Integration capabilities:
   * - Seamless platform UI integration
   * - Real-time signing status updates
   * - Automated post-signature processing
   * - Document completion notifications
   * - Investment workflow continuation
   * @see {@link generateDocuSignTemplateId} For creating document templates
   * @see {@link processWebhookEvent} For handling signature completion events
   */
  static async generateEmbeddedSignedUrl(offeringId: string, orderId: string, userId: string) {
    // Fetch offering details with template ID
    const offeringD = await Offering.findById(new Types.ObjectId(offeringId));

    if (!offeringD || !offeringD.template_id) {
      throw new Error('Offering or template Id not found !');
    }

    const { data: investorDetails } = await UserService.fetchUserDetails({ _id: userId });

    // Check if a DocuSign record already exists
    const generatedEmbeddedUrl = await DocuSignApi.generateEmbeddedSigningUrl({
      fileKey: offeringD.documents?.eSign,
      investorEmail: investorDetails.email,
      investorName: investorDetails.name,
      templateId: offeringD.template_id,
      investorId: userId,
      offeringId,
      orderId,
    });

    await whitelistSchema.findOneAndUpdate(
      { offeringId: new Types.ObjectId(offeringId) },
      { template_id: offeringD.template_id, docuSignurl: generatedEmbeddedUrl.redirectUrl, envelopeId: generatedEmbeddedUrl.envelopeId, isExpired: false, isSigned: false },
    );

    return generatedEmbeddedUrl;
  }

  /**
   * Generates a JWT access token to hit DocuSign APIs.
   * This method checks if an access token is already available in the class instance.
   * If yes, it returns the existing token. If not, it generates a new one and stores it in the class instance.
   * @returns {Promise<string>} The access token.
   */
  static async generateAccessToken(): Promise<string> {
    try {
      // if (this.accessToken) return this.accessToken;
      const client = await this.getApiClient();
      const { impersonatedUserGuid, clientId, privateKey } = env.DOCUSIGN;
      const rsaKey = Buffer.from(
        privateKey
          .split('\n')
          .map((item: string) => item.trim())
          .join('\n'),
        'utf-8',
      );
      const result = await client.requestJWTUserToken(clientId, impersonatedUserGuid, this.jwtScopes, rsaKey, this.jwtLife);
      const { access_token } = zodDocuSignGenerateAccessToken.parse(result.body);
      this.accessToken = access_token;

      return this.accessToken;
    } catch (err: any) {
      throw new Error(`Failed to generate AccessToken for docusign : ${err}`);
    }
  }

  /**
   * Fetch the DocuSign user information.
   * This function retrieves user details from DocuSign using an access token.
   * It identifies the default user account and constructs a base URI for API interactions.
   *
   * @returns {DocuSignUserInfo} A parsed response object containing user account information.
   * @throws Will throw an error if the user info cannot be retrieved or parsed.
   */
  static async getUserInfo() {
    try {
      const accessToken = await this.generateAccessToken();

      const client = await this.getApiClient();

      const apiResponse = await client.getUserInfo(accessToken);
      const result = zodDocuSignUserInfoApiResponse.parse(apiResponse);

      const userAccount = result.accounts.find((account: { isDefault: string }) => account.isDefault === 'true');
      if (!userAccount) throw new Error('User not found !');

      const response = zodDocuSignUserInfo.parse(userAccount);
      response.baseUri = `${response.baseUri}/restapi`;

      return response;
    } catch (err: any) {
      logger.error(err, 'erro22');
      throw new Error('failed to get userInfo');
    }
  }

  /**
   * Generates an embedded signing URL for a given payload.
   * @param {DocuSignGenerateEnvelopePayload} payload - The payload containing necessary information.
   * @returns {Promise<{envelopeId: string, userId: string, redirectUrl: string}>} The generated embedded signing URL response.
   * @throws Will throw an error if the envelope cannot be generated.
   */
  static async generateEmbeddedSigningUrl(payload: DocuSignGenerateEnvelopePayload) {
    const { investorId } = payload;
    const client = await this.getApiClient();
    const accessToken = await this.generateAccessToken();
    const { accountId, baseUri } = await this.getUserInfo();

    client.setBasePath(baseUri);
    client.addDefaultHeader('Authorization', `Bearer ${accessToken}`);

    const envelopeConfigArgs = await this.generateEnvelopeConfig(payload);
    const envelope = new docuSign.EnvelopesApi(client);
    const { envelopeId } = await envelope.createEnvelope(accountId, { envelopeDefinition: envelopeConfigArgs });

    if (!envelopeId) {
      throw new Error('Cannot generate envelope at the moment !');
    }

    const makeRecipientViewConfig = await this.generateRecipientViewRequestConfig(envelopeId, payload);
    const result = await envelope.createRecipientView(accountId, envelopeId, { recipientViewRequest: makeRecipientViewConfig });

    return { envelopeId, userId: investorId, redirectUrl: result.url };
  }

  /**
   * Generates a DocuSign template from a given file key.
   * This function takes a file key as an argument, fetches the file from the cloud storage,
   * and creates a new DocuSign template with the retrieved file.
   * @param {string} fileKey - The key of the file in cloud storage.
   * @param {string} name - The name of the template.
   * @returns {Promise<{templateId: string, templateName: string}>} The generated template ID and name.
   */
  static async generateEnvelopeTemplate({ fileKey, name }: { fileKey: string; name: string }) {
    const document = await this.generateDocuSignDocumentArgs({ fileKey });
    const templateObj: docuSign.EnvelopeTemplate = { documents: [document], emailSubject: 'Commitment Agreement', description: name, name, shared: 'false', status: 'created' };

    const client = await this.getApiClient();
    const accessToken = await this.generateAccessToken();
    const { accountId, baseUri } = await this.getUserInfo();

    client.setBasePath(baseUri);
    client.addDefaultHeader('Authorization', `Bearer ${accessToken}`);

    const template = new docuSign.TemplatesApi(client);
    const { templateId, name: templateName } = await template.createTemplate(accountId, { envelopeTemplate: templateObj });

    if (!templateId) {
      throw new Error('Cannot generate template at the moment !');
    }

    return { templateId, templateName };
  }

  /**
   * Generates a DocuSign document from a given file key.
   * This function takes a file key as an argument, fetches the file from the cloud storage,
   * and creates a new DocuSign document with the retrieved file.
   * @param {string} fileKey - The key of the file in cloud storage.
   * @returns {Promise<docuSign.Document>} The generated document.
   */
  static async generateDocuSignDocumentArgs({ fileKey }: { fileKey: string }): Promise<docuSign.Document> {
    const Document = await CloudHelper.getFileAsBase64(fileKey);

    if (!Document && Document.error) {
      throw new Error('File Content is missing ~');
    }

    const document: docuSign.Document = { documentBase64: Document, name: 'Commitment Agreement', fileExtension: fileKey.split('.').pop(), documentId: '1' };

    return document;
  }

  /**
   * Generates the envelope configuration for a given payload.
   * This function takes a payload containing file key, investor details, offering ID, order ID, template ID, and recipients,
   * and generates an envelope configuration object.
   * If the template ID is not provided, it generates a document from the file key.
   * The envelope configuration object will contain the document and the template roles.
   * @param {DocuSignGenerateEnvelopePayload} payload - The payload containing necessary information.
   * @returns {Promise<EnvelopeDefinition>} The generated envelope configuration object.
   */
  static async generateEnvelopeConfig(payload: DocuSignGenerateEnvelopePayload) {
    const documentsArr: docuSign.Document[] = [];
    const { investorEmail, investorName, investorId, fileKey, orderId, templateId, recipients = [] } = payload;

    if (!templateId) {
      const document = await this.generateDocuSignDocumentArgs({ fileKey });
      documentsArr.push(document);
    }

    const tabs = await this.generateEnvelopeTabs(payload);
    const signer: docuSign.Signer = { email: investorEmail, name: investorName, clientUserId: investorId, recipientId: '1', tabs };

    const templateRoles: docuSign.TemplateRole[] = [{ ...signer, roleName: `Signer ${signer.recipientId}` }];

    if (recipients.length) {
      recipients.forEach((recipient: { email: string; name: string }) => templateRoles.push({ email: recipient.email, name: recipient.name, roleName: 'cc' }));
    }

    const envelopeArgs: docuSign.EnvelopeDefinition = {
      emailSubject: 'Commitment Agreement',
      templateId,
      templateRoles,
      status: 'sent',
      customFields: {
        textCustomFields: [
          {
            show: 'true',
            required: 'false',
            fieldId: 'orderId',
            // value: offeringId.toString(),
            value: orderId.toString(),
            name: 'orderId',
          },
        ],
      },
    };

    if (!templateId) {
      envelopeArgs.recipients = { signers: [signer] };
      envelopeArgs.documents = documentsArr;
    }

    return envelopeArgs;
  }

  /**
   * Generates a recipient view request configuration for a given envelope ID and payload.
   * This function takes an envelope ID and a payload containing the investor's email, name, and ID,
   * and generates a recipient view request configuration object.
   * The configuration object is used to redirect the user to the DocuSign signing experience.
   * @param {string} envelopeId - The ID of the envelope.
   * @param {DocuSignUrlPayload} payload - The payload containing the investor's details.
   * @returns {docuSign.RecipientViewRequest} The generated recipient view request configuration object.
   */
  static generateRecipientViewRequestConfig(envelopeId: string, payload: DocuSignUrlPayload): docuSign.RecipientViewRequest {
    const { investorEmail, investorName, investorId } = payload;

    const viewRequestConfig = { returnUrl: `${env.DOCUSIGN.frontendUrl}/auth/create-order?state=${envelopeId}`, authenticationMethod: 'none', email: investorEmail, userName: investorName, clientUserId: investorId };

    return viewRequestConfig;
  }

  static async generateEnvelopeTabs(payload: DocuSignGenerateEnvelopePayload): Promise<docuSign.EnvelopeRecipientTabs> {
    const returnTabs: docuSign.EnvelopeRecipientTabs = {};
    returnTabs.signHereTabs = await this.generateEnvelopeSignHereTabs();
    returnTabs.radioGroupTabs = await this.generateEnvelopeRadioGroups();
    if (payload.tags) {
      returnTabs.textTabs = await this.generateEnvelopeTextTabs(payload);
      returnTabs.checkboxTabs = await this.generateEnvelopeCheckBoxTabs(payload);
    }

    return returnTabs;
  }

  /**
   * Generates envelope radio groups based on the DocuSignSupportedRadioGroupTagsEnum.
   * The function iterates over the enum keys and values and creates a radio group for each unique group label.
   * The radio group object contains the group name, requireAll, shared, tooltip, and radios properties.
   * The radios property is an array of radio objects, which contain the anchorString, anchorUnits, anchorYOffset, anchorXOffset, font, fontSize, fontColor, bold, required, selected, and anchorHorizontalAlignment properties.
   * The selected property is set to false by default.
   * @returns {Promise<docuSign.RadioGroup[]>} The generated radio groups.
   */

  static async generateEnvelopeRadioGroups() {
    const returnTags: docuSign.RadioGroup[] = [];

    const enumKeys = Object.keys(DocuSignSupportedRadioGroupTagsEnum);
    Object.values(DocuSignSupportedRadioGroupTagsEnum).forEach((tag, index) => {
      let radioXoffset = '0';
      const radioYoffset = '-2';
      const tagEnumLabel = enumKeys[index];
      const groupLabel = tagEnumLabel.split('_').slice(0, 2).join('');
      const findObj = returnTags.find((item) => item.groupName === groupLabel);

      if (!['SUBSCRIBERTYPE', 'FORMPF'].includes(groupLabel)) {
        radioXoffset = '10';
      }
      if (typeof findObj === 'undefined') {
        returnTags.push({
          groupName: groupLabel,
          requireAll: 'false',
          shared: 'false',
          tooltip: ' ',
          radios: [
            {
              anchorString: tag,
              anchorUnits: 'pixels',
              anchorYOffset: radioYoffset,
              anchorXOffset: radioXoffset,
              font: 'helvetica',
              fontSize: 'size11',
              fontColor: 'black',
              bold: 'true',
              required: 'false',
              selected: 'false',
              anchorHorizontalAlignment: 'left',
            },
          ],
        });
      } else if (typeof findObj !== 'undefined' && Array.isArray(findObj.radios)) {
        findObj.radios.push({
          anchorString: tag,
          anchorUnits: 'pixels',
          anchorYOffset: radioYoffset,
          anchorXOffset: radioXoffset,
          font: 'helvetica',
          fontSize: 'size11',
          fontColor: 'black',
          bold: 'true',
          required: 'false',
          selected: 'false',
          anchorHorizontalAlignment: 'left',
        });
      }
    });

    return returnTags;
  }

  /**
   * Generates checkbox tabs for a given payload.
   * @param {DocuSignGenerateEnvelopePayload} payload - The payload containing necessary information.
   * @returns {Promise<docuSign.Checkbox[]>} The generated array of checkbox tabs.
   */
  static async generateEnvelopeCheckBoxTabs(payload: DocuSignGenerateEnvelopePayload): Promise<docuSign.Checkbox[]> {
    const returnCheckBoxTab: docuSign.Checkbox[] = [];
    const { tags } = payload;

    if (!tags) return returnCheckBoxTab;

    // Iterate over each supported checkbox tag
    Object.values(DocuSignSupportedCheckBoxTagsEnum).forEach((tag) => {
      // Create a checkbox tab for each tag and push it to the result array
      returnCheckBoxTab.push({
        tabLabel: tag, // Set the label for the tab
        anchorString: tag, // Set the anchor string to position the checkbox
        anchorUnits: 'pixels', // Use pixels as units for the anchor
        anchorYOffset: '-5', // Set vertical offset for positioning
        anchorXOffset: '-20', // Set horizontal offset for positioning
        font: 'helvetica', // Specify font type
        fontSize: 'size11', // Specify font size
        fontColor: 'black', // Specify font color
        bold: 'true', // Set text to bold
        required: 'false', // Mark checkbox as not required
        selected: 'false', // Set checkbox as not selected by default
        anchorHorizontalAlignment: 'right', // Align checkbox to the right
      });
    });

    return returnCheckBoxTab;
  }

  /**
   * Renders the checkbox value for a given tag and tags object.
   * @param {string} tag - The tag to check
   * @param {any} tags - The tags object
   * @returns {Promise<boolean>} The value of the checkbox
   */
  static async renderCheckBoxValueForTags(tag: any, tags: any): Promise<boolean> {
    // Initialize the selected value to false
    let selected = false;

    // If the tags object is not present, return the selected value
    if (!tags) return selected;

    // Switch on the tag to determine the checkbox value
    switch (tag) {
      // If the tag is for the subscriber not passing categories, check if the type is individual and the accredited investor type does not include any values
      case DocuSignSupportedCheckBoxTagsEnum.SUBSCRIBER_NOT_PASS_CATEGORIES_CHECKED:
        if (tags.type === 'individual') {
          if (!(tags.accreditedInvestor.type ?? []).length) selected = true;
        }
        break;
      // If the tag is for the subscriber qualification as asset over 5000000, check if the type is individual and the qualified purchaser type includes natural-person-investments-5000000
      case DocuSignSupportedCheckBoxTagsEnum.SUBSCRIBER_QUALIFICATION_ASSET_OVER_5000000_CHECKED:
        if (tags.type === 'individual') {
          if ((tags.qualifiedPurchaser.type ?? []).includes('natural-person-investments-5000000')) selected = true;
        }
        break;
      // If the tag is for the subscriber qualification as personal asset over 5000000, check if the type is institution and the accredited investor type includes trust-assets-5000000
      case DocuSignSupportedCheckBoxTagsEnum.SUBSCRIBER_QUALIFICATION_PERSONAL_ASSET_OVER_5000000_CHECKED:
        if (tags.type === 'institution') {
          if ((tags.accreditedInvestor.type ?? []).includes('trust-assets-5000000')) selected = true;
        }
        break;
      // If the tag is for the subscriber qualification as licensed as bank, check if the accredited investor type includes bank-or-any-savings-and-loan-association-or-other-institution
      case DocuSignSupportedCheckBoxTagsEnum.SUBSCRIBER_QUALIFICATION_LICENSED_AS_BANK:
        if ((tags.accreditedInvestor.type ?? []).includes('bank-or-any-savings-and-loan-association-or-other-institution')) selected = true;
        break;
      // If the tag is for the subscriber qualification as investor broker, check if the type is institution and the qualified buyer type includes private-business-development-company
      case DocuSignSupportedCheckBoxTagsEnum.SUBSCRIBER_QUALIFICATION_AS_INVESTOR_BROKER_CHECKED:
        if (tags.type === 'institution' && (tags.accreditedInvestor.type ?? []).includes('private-business-development-company')) selected = true;
        break;
      // If the tag is for the subscriber qualification as investment adviser, check if the type is institution and the qualified buyer type includes dealer-own-account
      case DocuSignSupportedCheckBoxTagsEnum.SUBSCRIBER_QUALIFICATION_AS_INVESTMENT_ADVISER_CHECKED:
        if (tags.type === 'institution' && (tags.qualifiedBuyer.type ?? []).includes('dealer-own-account')) selected = true;
        break;
      // If the tag is for the subscriber qualification as benefit plan ERISA, check if the type is institution and the qualified buyer type includes e-employee-benefit-plan
      case DocuSignSupportedCheckBoxTagsEnum.SUBSCRIBER_QUALIFICATION_AS_BENEFIT_PLAN_ERISA_CHECKED:
      case DocuSignSupportedCheckBoxTagsEnum.SUBSCRIBER_QUALIFICATION_BENEFIT_PLAN_STATE_CHECKED:
        if (tags.type === 'institution' && (tags.qualifiedBuyer.type ?? []).includes('e-employee-benefit-plan')) selected = true;
        break;
      // If the tag is for the subscriber qualification as equity owner, check if the type is institution and the accredited investor type includes entity-of-accredited-investors
      case DocuSignSupportedCheckBoxTagsEnum.SUBSCRIBER_QUALIFICATION_AS_EQUITY_OWNER_CHECKED:
        if (tags.type === 'institution' && (tags.accreditedInvestor.type ?? []).includes('entity-of-accredited-investors')) selected = true;
        break;
      // If the tag is for the subscriber qualification as family office, check if the type is institution and the accredited investor type includes family-office
      case DocuSignSupportedCheckBoxTagsEnum.SUBSCRIBER_QUALIFICATION_AS_FAMILY_OFFICE_CHECKED:
        if (tags.type === 'institution' && (tags.accreditedInvestor.type ?? []).includes('family-office')) selected = true;
        break;
      // If the tag is for the subscriber qualification as family client, check if the type is institution and the accredited investor type includes family-client
      case DocuSignSupportedCheckBoxTagsEnum.SUBSCRIBER_QUALIFICATION_AS_FAMILY_CLIENT_CHECKED:
        if (tags.type === 'institution' && (tags.accreditedInvestor.type ?? []).includes('family-client')) selected = true;
        break;
      // If the tag is for the subscriber qualification as none, check if the type is institution and the accredited investor type and qualified buyer type are both empty, or if the type is individual and the accredited investor type and qualified purchaser type are both empty
      case DocuSignSupportedCheckBoxTagsEnum.SUBSCRIBER_QUALIFICATION_NONE_CHECKED:
        if (tags.type === 'institution' && (tags.accreditedInvestor.type ?? []).length === 0 && (tags.qualifiedBuyer.type ?? []).length === 0) selected = true;
        else if (tags.type === 'individual' && (tags.accreditedInvestor.type ?? []).length === 0 && (tags.qualifiedPurchaser.type ?? []).length === 0) selected = true;
        break;
      // If the tag is not recognized, set the selected value to false
      default:
        selected = false;
    }

    // Return the selected value
    return selected;
  }

  /**
   * Generates SignHere tabs for the envelope.
   * This function creates an array of SignHere tab configurations based on the supported sign enums.
   * Each tab is positioned using anchor strings and offsets.
   * @returns {Promise<docuSign.SignHere[]>} The generated array of SignHere tabs.
   */
  static async generateEnvelopeSignHereTabs(): Promise<docuSign.SignHere[]> {
    const returnSignHereTabs: docuSign.SignHere[] = [];

    Object.values(DocuSignSupportedSignEnum).forEach((tag) => {
      returnSignHereTabs.push({ anchorString: tag, anchorYOffset: '0', anchorUnits: 'pixels', anchorXOffset: '0', optional: tag.toUpperCase().includes('_OPTIONAL') ? 'true' : 'false' });
    });

    return returnSignHereTabs;
  }
  /**
   * Generates text tabs for the envelope.
   * This function creates an array of text tab configurations based on the supported tags.
   * Each tab is positioned using anchor strings and offsets.
   * @param {any} payload The payload containing the tags and investor's email.
   * @returns {Promise<docuSign.Text[]>} The generated array of text tabs.
   */

  static async generateEnvelopeTextTabs(payload: any): Promise<docuSign.Text[]> {
    const { tags, investorEmail, investmentAmount } = payload;

    const returnTextTabs: docuSign.Text[] = [];
    if (!tags) return returnTextTabs;

    const identityInfo = tags.type === 'individual' ? tags.identityInformation : tags.institutionInformation;
    Object.values(DocuSignSupportedTagsEnum).forEach((tag) => {
      let anchorOffSetY = -2;
      const anchorOffSetX = 0;
      const isOptional = !!(tag.toLowerCase().includes('_joint') || tag.toLowerCase().includes('_optional') || tag.toLowerCase().includes('_mailing') || tag.toLowerCase().includes('_mail'));
      if (isOptional) anchorOffSetY = -5;
      let value = '';
      switch (tag) {
        case DocuSignSupportedTagsEnum.SUBSCRIBER_ADDRESS:
          value = `${identityInfo.addressLine1} ${identityInfo.addressLine2}`;
          break;
        case DocuSignSupportedTagsEnum.SUBSCRIBER_CITIZENSHIP:
          value = identityInfo.country;
          break;
        case DocuSignSupportedTagsEnum.SUBSCRIBER_CITY:
          anchorOffSetY = -4;
          value = identityInfo.city;
          break;
        case DocuSignSupportedTagsEnum.SUBSCRIBER_COUNTRY:
          anchorOffSetY = -4;
          value = identityInfo.country;
          break;
        case DocuSignSupportedTagsEnum.SUBSCRIBER_DOB:
          anchorOffSetY = -4;
          if (tags.type === 'individual') {
            value = tags.identityInformation.dateOfBirth.toDateString();
          }
          break;
        case DocuSignSupportedTagsEnum.SUBSCRIBER_EMAIL_ADDRESS:
        case DocuSignSupportedTagsEnum.SUBSCRIBER_JOINT_EMAIL:
          value = investorEmail;
          anchorOffSetY = -4;
          break;
        case DocuSignSupportedTagsEnum.SUBSCRIBER_NAME:
          if (tags.type === 'individual') {
            value = tags.identityInformation;
          }
          anchorOffSetY = -4;
          break;
        case DocuSignSupportedTagsEnum.SUBSCRIBER_COMPANY_NAME:
          if (tags.type === 'institution') {
            value = tags.name;
          }
          anchorOffSetY = -4;
          break;
        case DocuSignSupportedTagsEnum.SUBSCRIBER_PIN_CODE:
          anchorOffSetY = -4;
          value = identityInfo.zip;
          break;
        case DocuSignSupportedTagsEnum.SUBSCRIBER_SSN:
          if (tags.type === 'individual') {
            value = tags.identityInformation.SSN;
          } else if (tags.type === 'institution') {
            value = tags.institutionInformation.federalTaxID;
          }
          anchorOffSetY = -4;
          break;
        case DocuSignSupportedTagsEnum.SUBSCRIBER_STATE:
          anchorOffSetY = -4;
          value = identityInfo.stateName;
          break;
        case DocuSignSupportedTagsEnum.SUBSCRIBER_TYPE:
          value = tags.type;
          break;
        case DocuSignSupportedTagsEnum.SUBSCRIBER_CURRENT_POSITION:
          if (tags.type === 'institution') {
            value = tags.complianceContactDetails.jobTitle;
          } else if (tags.type === 'individual') {
            value = tags.type;
          }
          break;
        case DocuSignSupportedTagsEnum.SUBSCRIBER_TELEPHONE:
        case DocuSignSupportedTagsEnum.SUBSCRIBER_JOINT_TELEPHONE:
          value = tags.type === 'individual' ? tags.identityInformation.phone : tags.complianceContactDetails.phone;
          break;
        case DocuSignSupportedTagsEnum.CURRENT_DATE:
          value = new Date().toLocaleDateString();
          break;
        case DocuSignSupportedTagsEnum.EMPTY_TEXT_BOX:
        default:
          value = '';
      }

      let textReqVal = !value && !isOptional ? 'false' : 'false';
      if (tag === DocuSignSupportedTagsEnum.CAPITAL_CALL_AMOUNT) {
        if (investmentAmount) value = investmentAmount.toString();
        textReqVal = 'true';
        anchorOffSetY = -4;
      }
      if (!value.trim() || !value.trim().length) value = '';

      returnTextTabs.push({
        tabLabel: tag,
        anchorString: tag,
        anchorUnits: 'pixels',
        anchorYOffset: anchorOffSetY.toString(),
        anchorXOffset: anchorOffSetX.toString(),
        font: 'helvetica',
        fontSize: 'size11',
        fontColor: 'black',
        bold: 'true',
        required: textReqVal,
        value,
      });
    });

    return returnTextTabs;
  }

  /**
   * Fetches the envelope document from DocuSign
   * @param {envelopeId: string} params - The envelope ID to fetch the document for
   * @returns {Promise<string>} The document content
   */
  static async fetchEnvelopeDocument({ envelopeId }: { envelopeId: string }) {
    const client = await this.getApiClient();
    const accessToken = await this.generateAccessToken();
    const { accountId, baseUri } = await this.getUserInfo();
    client.setBasePath(baseUri);
    client.addDefaultHeader('Authorization', `Bearer ${accessToken}`);
    const envelope = new docuSign.EnvelopesApi(client);

    return envelope.getDocument(accountId, envelopeId, 'combined', {});
  }

  /**
   * Retrieves a signed URL for a given DocuSign envelope.
   * @param {DocuSignUrlPayload} payload - The payload containing envelope and investor details.
   * @returns {Promise<any>} The response containing the recipient view URL.
   * @throws Will throw an error if the recipient view cannot be created.
   */
  async getSignedUrl(payload: DocuSignUrlPayload) {
    const client = await DocuSignApi.getApiClient();
    const accessToken = await DocuSignApi.generateAccessToken();
    const { accountId, baseUri } = await DocuSignApi.getUserInfo();
    const { envelopeId } = payload;
    client.setBasePath(baseUri);
    client.addDefaultHeader('Authorization', `Bearer ${accessToken}`);
    const viewRequest = await DocuSignApi.generateRecipientViewRequestConfig(envelopeId, payload);
    const envelope = new docuSign.EnvelopesApi(client);
    const response = await envelope.createRecipientView(accountId, envelopeId, { recipientViewRequest: viewRequest });

    return response;
  }

  /**
   * Handle webhook event when DocuSign signing fails.
   * Updates the whitelist document to mark it as expired and not signed.
   * @param param0 - Object containing the webhook data.
   * @throws Will throw an error if the pending envelope is not found.
   */
  static async handleFailedDocuSignWebhook({ data }: { data: DocuSignWebhookDataItem }) {
    const { envelopeId } = data;

    const pendingDocuSignDocument = await whitelistSchema.findOneAndUpdate(
      { envelopeId, isExpired: false, isSigned: false },
      { isExpired: true, isSigned: false },
      { new: true }, // Optional, return updated document if needed
    );

    if (!pendingDocuSignDocument) {
      throw new Error('Pending envelope not found!');
    }
  }

  /**
   * Handles the webhook event for DocuSign completed envelopes.
   * Updates the whitelist document to mark it as signed.
   * @param {Object} param0 - Object containing the webhook data.
   * @throws Will throw an error if the pending envelope is not found.
   */
  static async handleCompletedDocuSignWebhook({ data }: { data: DocuSignWebhookDataItem }) {
    const { envelopeId } = data;

    const pendingDocuSignDocument = await whitelistSchema.findOneAndUpdate(
      { envelope_id: envelopeId, isExpired: false, isSigned: false },
      { isExpired: false, isSigned: true },
      { new: true }, // Optional, return updated document if needed
    );

    if (!pendingDocuSignDocument) {
      throw new Error('Pending envelope not found!');
    }
  }

  /**
   * Handle webhook events for DocuSign
   * @param {Object} payload - Webhook request payload
   * @property {string} event - Event type
   * @property {Object} data - Webhook data
   * @property {string} data.envelopeId - Envelope ID
   * @returns {Promise<void>}
   */

  static async handleWebhookEvent(payload: DocuSignWebhookRequest) {
    const { event, data } = payload;

    try {
      switch (event) {
        case DocuSignWebHookEventsEnum.ENVELOPE_COMPLETED:
        case DocuSignWebHookEventsEnum.ENVELOPE_DELIVERED:
        case DocuSignWebHookEventsEnum.RECIPIENT_COMPLETED:
          await DocuSignApi.handleCompletedDocuSignWebhook({ data });
          break;

        default:
          await DocuSignApi.handleFailedDocuSignWebhook({ data });
          break;
      }

      logger.info('DocuSign Webhook Handled :: ', JSON.stringify({ payload }));
    } catch (err) {
      logger.error(err?.stack, 'DocuSign Webhook error :: ', JSON.stringify({ payload }));
    }
  }
}
