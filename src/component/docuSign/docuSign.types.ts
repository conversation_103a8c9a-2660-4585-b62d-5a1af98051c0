import { EnvelopeDefinition } from 'docusign-esign';
import { z } from 'zod';

// Define your schemas using Zod
const zodArray = z.array;
const zodEmail = z.string().email();
const zodEnum = z.enum;
const zodNotEmptyString = z.string().min(1, 'Cannot be empty');
const zodNumber = z.number();
const zodObject = z.object;
const zodOptional = z.optional;
const zodString = z.string();

// Example of creating a KYC Application schema
const zodKycApplication = z.object({
  name: zodNotEmptyString,
  email: zodEmail,
  age: zodNumber.optional(), // Optional field
  status: zodEnum(['pending', 'approved', 'rejected']), // Example enum
});
export const zodDocuSignUrlPayload = zodObject({ envelopeId: zodString, investorId: zodString, investorEmail: zodString, investorName: zodString });
export type DocuSignUrlPayload = z.infer<typeof zodDocuSignUrlPayload>;

export const zodDocuSignGenerateEnvelopePayload = zodObject({
  fileKey: zodString,
  investorEmail: zodString,
  investorName: zodString,
  investmentAmount: zodOptional(zodNumber),
  investorId: zodString,
  offeringId: zodString,
  orderId: zodString,
  templateId: zodString,
  tags: zodOptional(zodKycApplication),
  recipients: zodOptional(zodArray(zodObject({ email: zodEmail, name: zodString }))),
});

export type DocuSignGenerateEnvelopePayload = z.infer<typeof zodDocuSignGenerateEnvelopePayload>;

export const zodDocuSignGenerateEmbeddedSigningUrlResponse = zodObject({ envelopeId: zodString, redirectUrl: zodNotEmptyString, userId: zodString });
export type docuSignGenerateEmbeddedSigningUrlResponse = z.infer<typeof zodDocuSignGenerateEmbeddedSigningUrlResponse>;
export interface DocuSignApiContract {
  generateEmbeddedSigningUrl(args: DocuSignGenerateEnvelopePayload): Promise<docuSignGenerateEmbeddedSigningUrlResponse>;
  generateEnvelopeConfig(args: DocuSignGenerateEnvelopePayload): Promise<EnvelopeDefinition>;
  fetchEnvelopeDocument({ envelopeId }: { envelopeId: string }): Promise<string>;
  getSignedUrl(args: DocuSignUrlPayload): Promise<any>;
  generateEnvelopeTemplate({ fileKey, name }: { fileKey: string; name: string }): Promise<{
    templateId: string;
    templateName?: string;
  }>;
}

export enum DocuSignSupportedSignEnum {
  SIGN_HERE = 'SIGN_HERE',
  SIGN__OPTIONAL = 'SIGN__OPTIONAL',
  SUBSCRIBER_PAY_TO_PLAY = 'SUBSCRIBER_PAY_TO_PLAY_SIGN_OPTIONAL',
  TAX_ERISA_SIGN_OPTIONAL = 'TAX_ERISA_SIGN_OPTIONAL',
  SUBSCRIBER_JOINT_SIGN_OPTIONAL = 'SUBSCRIBER_JOINT_SIGN_OPTIONAL',
}

export enum DocuSignSupportedTagsEnum {
  COMMITMENT_AMOUNT = 'COMMITMENT_AMOUNT',
  SUBSCRIBER_INITIAL = 'SUBSCRIBER_INITIAL_OPTIONAL',
  SUBSCRIBER_NAME = 'SUBSCRIBER_NAME',
  SUBSCRIBER_COMPANY_NAME = 'SUBSCRIBER_COMPANY_NAME',
  SUBSCRIBER_SSN = 'SUBSCRIBER_SSN',
  SUBSCRIBER_DOB = 'SUBSCRIBER_DOB',
  SUBSCRIBER_ADDRESS = 'SUBSCRIBER_ADDRESS',
  SUBSCRIBER_COUNTRY = 'SUBSCRIBER_COUNTRY',
  SUBSCRIBER_STATE = 'SUBSCRIBER_STATE',
  SUBSCRIBER_CITY = 'SUBSCRIBER_CITY',
  SUBSCRIBER_PIN_CODE = 'SUBSCRIBER_PIN_CODE',
  SUBSCRIBER_TYPE = 'SUBSCRIBER_TYPE',
  SUBSCRIBER_EMAIL_ADDRESS = 'SUBSCRIBER_EMAIL_ADDRESS',
  SUBSCRIBER_CITIZENSHIP = 'SUBSCRIBER_CITIZENSHIP',
  SUBSCRIBER_CURRENT_POSITION = 'SUBSCRIBER_CURRENT_POSITION',
  SUBSCRIBER_TELEPHONE = 'SUBSCRIBER_TELEPHONE',
  EMPTY_TEXT_BOX = 'EMPTY_TEXT_BOX',
  CURRENT_DATE = 'CURRENT_DATE',
  SUBSCRIBER_JOINT_NAME = 'SUBSCRIBER_JOINT_NAME',
  SUBSCRIBER_JOINT_SSN = 'SUBSCRIBER_JOINT_SSN',
  SUBSCRIBER_JOINT_DOB = 'SUBSCRIBER_JOINT_DOB',
  SUBSCRIBER_JOINT_TELEPHONE = 'SUBSCRIBER_JOINT_TELEPHONE',
  SUBSCRIBER_JOINT_FACSIMILE = 'SUBSCRIBER_JOINT_FACSIMILE',
  SUBSCRIBER_JOINT_EMAIL = 'SUBSCRIBER_JOINT_EMAIL',
  SUBSCRIBER_MAILING_ADDRESS = 'SUBSCRIBER_MAILING_ADDRESS',
  SUBSCRIBER_MAILING_CITY = 'SUB_MAILING_CITY',
  SUBSCRIBER_MAILING_STATE = 'SUB_MAIL_STATE',
  SUBSCRIBER_MAILING_ZIP = 'SUB_MAILING_ZIP',
  SUBSCRIBER_MAILING_COUNTRY = 'SUB_MAILING_COUNTRY',
  SUBSCRIBER_MAILING_TELEPHONE = 'SUBSCRIBER_MAILING_TELEPHONE',
  SUBSCRIBER_MAILING_FACSIMILE = 'SUBSCRIBER_MAILING_FACSIMILE',
  SUBSCRIBER_MAILING_EMAIL = 'SUBSCRIBER_MAILING_EMAIL',
  SUBSCRIBER_CUSTODIAN = 'SUBSCRIBER_CUSTODIAN',
  SUBSCRIBER_CUSTODIAN_EIN = 'SUBSCRIBER_CUSTO_EIN',
  SUBSCRIBER_COMMUNITY_PROPERTY = 'SUBSCRIBER_COMMUNITY_PROPERTY',
  SUBSCRIBER_PAY_TO_PLAY_CERTIFICATION_OPTIONAL = 'SUBSCRIBER_PAY_TO_PLAY_CERTIFICATION_OPTIONAL',
  SUBSCRIBER_FORM_PF_OTHER_OPTIONAL = 'SUBSCRIBER_FORM_PF_OTHER_OPTIONAL',
  PARTNERSHIP_PLACEMENT_OPTIONAL = 'PARTNERSHIP_PLACEMENT_OPTIONAL',
  COMPLIANCE_WIRING_BANK_NAME_OPTIONAL = 'COMPLIANCE_WIRING_BANK_NAME_OPTIONAL',
  SUBSCRIBER_RELATION_INTEREST_OPTIONAL = 'SUBSCRIBER_RELATION_INTEREST_OPTIONAL',
  SUBSCRIBER_WHICH_GOV_OPTIONAL = 'SUBSCRIBER_WHICH_GOV_OPTIONAL',
  SUBSCRIBER_WHICH_GOV_POS_OPTIONAL = 'SUBSCRIBER_WHICH_GOV_POS_OPTIONAL',
  SUBSCRIBER_WHICH_GOV_FAMILY_OPTIONAL = 'SUBSCRIBER_WHICH_GOV_FAMILY_OPTIONAL',
  SUBSCRIBER_TX_YEAR_OPTIONAL = 'SUBSCRIBER_TX_YEAR_OPTIONAL',
  TAX_ERISA_NAME_OPTIONAL = 'TAX_ERISA_NAME_OPTIONAL',
  LEGAL_FORM_SUBSCRIBER_NAME_OPTIONAL = 'LEGAL_FORM_NAME_OPTIONAL',
  US_STATE_SUBSCRIBER_NAME_OPTIONAL = 'US_STATE_NAME_OPTIONAL',
  DATE_OF_INCORPORATION_OPTIONAL = 'DATE_OF_INCORPORATION_OPTIONAL',
  SUBSCRIBER_GENERAL_SUPP_OPTIONAL = 'SUBSCRIBER_GENERAL_SUPP_OPTIONAL',
  SUBSCRIBER_TAX_PERCENT1_CONTACT_OPTIONAL = 'SUB_TAX_PER1_CON_OPTIONAL',
  SUBSCRIBER_TAX_PERCENT1_OPTIONAL = 'SUB_TAX_PER1_OPTIONAL',
  SUBSCRIBER_TAX_PERCENT2_OPTIONAL = 'SUB_TAX_PER2_OPTIONAL',
  SUBSCRIBER_TAX_PERCENT3_OPTIONAL = 'SUB_TAX_PER3_OPTIONAL',
  SUBSCRIBE_TAXYEAR_OPTIONAL = 'SUBSCRIBE_TAXYEAR_OPTIONAL',
  BENEFICIERY_FULL_NAME_OPTIONAL = 'BENEFICIERY_FULL_NAME_OPTIONAL',
  BENEFICIERY_FULL_ADDRESS_OPTIONAL = 'BENEFICIERY_FULL_ADDRESS_OPTIONAL',
  BENEFICIERY_CITIZENSHIP_OPTIONAL = 'BENEFICIERY_CITIZENSHIP_OPTIONAL',
  BENEFICIERY_OWNERSHIP_OPTIONAL = 'BENEFICIERY_OWNERSHIP_OPTIONAL',
  CAPITAL_CALL_AMOUNT = 'CAPITAL_CALL_AMOUNT',
  CAPITAL_CALL_ETH_AMOUNT = 'CAPITAL_CALL_ETH_AMOUNT',
  CAPITAL_CALL_OTHER_ASSET_OPTIONAL = 'CAPITAL_CALL_OTHER_ASSET_OPTIONAL',
  CONTRIBUTION_AMOUNT_OPTIONAL = 'CONTRIBUTION_AMOUNT_OPTIONAL',
  CONTRIBUTION_AMOUNT_ETH_OPTIONAL = 'CONTRIBUTION_AMOUNT_ETH_OPTIONAL',
  CONTRIBUTION_OTHER_ASSET_OPTIONAL = 'CONTRIBUTION_OTHER_ASSET_OPTIONAL',
  WITHDRAW_AMOUNT_OPTIONAL = 'WITHDRAW_AMOUNT_OPTIONAL',
  WITHDRAW_AMOUNT_ETH_OPTIONAL = 'WITHDRAW_AMOUNT_ETH_OPTIONAL',
  MAIL_ADDRESS_OPTIONAL = 'MAIL_ADDRESS_OPTIONAL',
  IRA_CUSTODIAN_NAME_OPTIONAL = 'IRA_CUSTODIAN_NAME_OPTIONAL',
  GENERAL_PARTNER_RELATIONSHIP_OPTIONAL = 'GENERAL_PARTNER_RELATIONSHIP_OPTIONAL',
}

export enum DocuSignSupportedRadioGroupTagsEnum {
  SUBSCRIBER_TYPE_INDIVIDUAL = 'TY_INV',
  SUBSCRIBER_TYPE_JOINT = 'TY_JOT',
  SUBSCRIBER_TYPE_TRUST = 'TY_TRU',
  SUBSCRIBER_TYPE_PARTNERSHIP = 'TY_PAR',
  SUBSCRIBER_TYPE_CORPORATION = 'TY_COR',
  SUBSCRIBER_TYPE_LLC = 'TY_LLC',
  SUBSCRIBER_TYPE_IRA = 'TY_IRA',
  SUBSCRIBER_TYPE_TENANT = 'TY_TEN',
  SUBSCRIBER_TYPE_OTHER = 'TY_OTH',
  FORM_PF_INVESTOR_US = 'PF_US',
  FORM_PF_INVESTOR_NON_US = 'PF_NUS',
  FORM_PF_INVESTOR_BROKER = 'PF_BR',
  FORM_PF_INVESTOR_INSURANCE_COMPANY = 'PF_IC',
  FORM_PF_INVESTOR_INVESTMENT_COMPANY = 'PF_INVC',
  FORM_PF_INVESTOR_PRIVATE_FUND = 'PF_PF',
  FORM_PF_INVESTOR_NON_PROFIT = 'PF_NP',
  FORM_PF_INVESTOR_PENSION_PLAN = 'PF_PP',
  FORM_PF_INVESTOR_BANKING = 'PF_BK',
  FORM_PF_INVESTOR_STATE = 'PF_STE',
  FORM_PF_INVESTOR_STATE_PENSION = 'PF_SEP',
  FORM_PF_INVESTOR_SOVEREIGN_FUND = 'PF_SWF',
  FORM_PF_INVESTOR_NON_US_INV = 'PF_INN',
  FORM_PF_INVESTOR_OTHER = 'PF_OTR',
  PARTNERSHIP_PLACEMENT_YES = 'PP_PL_YES',
  PARTNERSHIP_PLACEMENT_NO = 'PP_PL_NO',
  SCHEDULE_K1_YES = 'SH_K1_Y',
  SCHEDULE_K1_NO = 'SH_K1_N',
  FATF_MEM_Y = 'FATF_Y',
  FATF_MEM_N = 'FATF_N',
  IS_WIRING_BANK_Y = 'WIRBK_Y',
  IS_WIRING_BANK_N = 'WIRBK_N',
  GENERAL_SUBINTEREST_Y = 'GSI1_Y',
  GENERAL_SUBINTEREST_N = 'GSI1_N',
  GENERAL_SUBINTBEN_Y = 'GSI2_Y',
  GENERAL_SUBINTBEN_N = 'GSI2_N',
  GENERAL_SUBINTPART_Y = 'GSI3_Y',
  GENERAL_SUBINTPART_N = 'GSI3_N',
  SUBSCRIBER_POLITICAL_EXPOSED_CHECKED = 'SPE1_Y',
  SUBSCRIBER_POLITICAL_EXPOSED_UNCHECKED = 'SPE1_N',
  SUBSCRIBER_NET_ASSET_CHECKED = 'SNAS_Y',
  SUBSCRIBER_NET_ASSET_UNCHECKED = 'SNAS_N',
  SUBSCRIBER_NET2_ASSET_200000_CHECKED = 'SNAS2_Y',
  SUBSCRIBER_NET2_ASSET_200000_UNCHECKED = 'SNAS2_N',
  SUBSCRIBER_HOLD_PROPERTIES_CHECKED = 'SHPC_Y',
  SUBSCRIBER_HOLD_PROPERTIES_UNCHECKED = 'SHPC_N',
  SUBSCRIBER_PARTNERSHIP_CHECKED = 'SPC_Y',
  SUBSCRIBER_PARTNERSHIP_UNCHECKED = 'SPC_N',
  SUBSCRIBER_KNOWLEDGE_EMP_CHK = 'SKEC_Y',
  SUBSCRIBER_KNOWLEDGE_EMP_UCHK = 'SKEC_N',
  SUBSCRIBER_CITIZEN_US_CHECKED = 'SCU_Y',
  SUBSCRIBER_CITIZEN_US_UNCHECKED = 'SCU_N',
  SUBSCRIBER_CITIZEN2_US_CHECKED = 'SCU2_Y',
  SUBSCRIBER_CITIZEN2_US_UNCHECKED = 'SCU2_N',
  SUBSCRIBER_CITIZEN3_US_CHECKED = 'SCU3_Y',
  SUBSCRIBER_CITIZEN3_US_UNCHECKED = 'SCU3_N',
  SUBSCRIBER_CITIZENSPO_CHK = 'SCUC_Y',
  SUBSCRIBER_CITIZENSPO_UCHK = 'SCUC_N',
  SUBSCRIBER_CITIZENSPO_NA = 'SCUC_NA',
  SUBSCRIBER_TAXYR_CHK = 'STY_Y',
  SUBSCRIBER_TAXYR_UCHK = 'STY_N',
  SUBSCRIBER_GENERALINFO_CHK = 'SGI1_Y',
  SUBSCRIBER_GENERALINFO_UCHK = 'SGI1_N',
  SUBSCRIBER_GENERALINFO2_CHK = 'SGI2_Y',
  SUBSCRIBER_GENERALINFO2_UCHK = 'SGI2_N',
  SUBSCRIBER_GENERALINFO3_CHK = 'SGI3_Y',
  SUBSCRIBER_GENERALINFO3_UCHK = 'SGI3_N',
  SUBSCRIBER_GENERALAFFILIATION_CHK = 'SGA_Y',
  SUBSCRIBER_GENERALAFFILIATION_UCHK = 'SGA_N',
  SUBSCRIBER_GENERALSUPP_CHK = 'SGSC_Y',
  SUBSCRIBER_GENERALSUPP_UCHK = 'SGSC_N',
  SUBSCRIBER_GENERALSUPP2_CHK = 'SGS2_Y',
  SUBSCRIBER_GENERALSUPP2_UCHK = 'SGS2_N',
  SUBSCRIBER_GENERALSUPP3_CHK = 'SGS3_Y',
  SUBSCRIBER_GENERALSUPP3_UCHK = 'SGS3_N',
  SUBSCRIBER_GENERALTAXERISA_CHK = 'SGTC_Y',
  SUBSCRIBER_GENERALTAXERISA_UCHK = 'SGTC_N',
  SUBSCRIBER_GENERALTAXERISA2_CHK = 'SGTC2_Y',
  SUBSCRIBER_GENERALTAXERISA2_UCHK = 'SGTC2_N',
  SUBSCRIBER_GENERALTAXERISA3_CHK = 'SGTC3_Y',
  SUBSCRIBER_GENERALTAXERISA3_UCHK = 'SGTC3_N',
  SUBSCRIBER_GENERALTAXERISA4_CHK = 'SGTC4_Y',
  SUBSCRIBER_GENERALTAXERISA4_UCHK = 'SGTC4_N',
  SUBSCRIBER_BANKHOLDACTINFO_CHK = 'SBIC_Y',
  SUBSCRIBER_BANKHOLDACTINFO_UCHK = 'SBIC_N',
  SUBSCRIBER_TAXINFO_CHK = 'STC_Y',
  SUBSCRIBER_TAXINFO_UCHK = 'STC_N',
  SUBSCRIBER_TAXINFO2_CHK = 'STC2_Y',
  SUBSCRIBER_TAXINFO2_UCHK = 'STC2_N',
  SUBSCRIBER_TAXINFO3_CHK = 'STC3_Y',
  SUBSCRIBER_TAXINFO3_UCHK = 'STC3_N',
  SUBSCRIBER_TAXINFO4_CHK = 'STC4_Y',
  SUBSCRIBER_TAXINFO4_UCHK = 'STC4_N',
  SUBSCRIBER_TAXINFO5_CHK = 'STC5_Y',
  SUBSCRIBER_TAXINFO5_UCHK = 'STC5_N',
  SUBSCRIBER_TAXINFO6_CHK = 'STC6_Y',
  SUBSCRIBER_TAXINFO6_UCHK = 'STC6_N',
  SUBSCRIBER_TAXINFO7_CHK = 'STC7_Y',
  SUBSCRIBER_TAXINFO7_UCHK = 'STC7_N',
  SUBSCRIBER_TAXINFO8_CHK = 'STC8_Y',
  SUBSCRIBER_TAXINFO8_UCHK = 'STC8_N',
  SUBSCRIBER_TAXINFO9_CHK = 'STC9_Y',
  SUBSCRIBER_TAXINFO9_UCHK = 'STC9_N',
  SUBSCRIBER_WITHDRAW_CHK = 'SWC_Y',
  SUBSCRIBER_WITHDRAW_UCHK = 'SWC_N',
  SUBSCRIBER_WITHDRAWDISREGARD_CHK = 'SWDC_Y',
  SUBSCRIBER_WITHDRAWDISREGARD_UCHK = 'SWDC_N',
}

export enum DocuSignSupportedCheckBoxTagsEnum {
  SUBSCRIBER_COMMUNITY_PROPERTY_CHECKED = 'PROP_CHK',
  SUBSCRIBER_NOT_PASS_CATEGORIES_CHECKED = 'SUB_NPCAT_CHK',
  SUBSCRIBER_NOT_EMPLOYEE_AGENT_CHECKED = 'EMP_AG_Y',
  SUBSCRIBER_NOT_EMPLOYEE_AGENT_UCHECKED = 'EMP_AG_N',
  SUBSCRIBER_QUALIFICATION_ASSET_OVER_5000000_CHECKED = 'SUB_QUA_AS_5_CHK',
  SUBSCRIBER_QUALIFICATION_PERSONAL_ASSET_OVER_5000000_CHECKED = 'SUB_QUA_PAS_5_CHK',
  SUBSCRIBER_QUALIFICATION_LICENSED_AS_BANK = 'SUB_QUA_LAB_CHK',
  SUBSCRIBER_QUALIFICATION_AS_INVESTOR_BROKER_CHECKED = 'SUB_QUA_AIB_CHK',
  SUBSCRIBER_QUALIFICATION_AS_INVESTMENT_ADVISER_CHECKED = 'SUB_QUA_AIA_CHK',
  SUBSCRIBER_QUALIFICATION_AS_RURAL_BUSINESS_CHECKED = 'SUB_QUA_ARB_CHK',
  SUBSCRIBER_QUALIFICATION_AS_BENEFIT_PLAN_ERISA_CHECKED = 'SUB_QUA_ABPE_CHK',
  SUBSCRIBER_QUALIFICATION_BENEFIT_PLAN_STATE_CHECKED = 'SUB_QUA_ABPS_CHK',
  SUBSCRIBER_QUALIFICATION_AS_EQUITY_OWNER_CHECKED = 'SUB_QUA_AEO_CHK',
  SUBSCRIBER_QUALIFICATION_AS_FAMILY_OFFICE_CHECKED = 'SUB_QUA_AFO_CHK',
  SUBSCRIBER_QUALIFICATION_AS_FAMILY_CLIENT_CHECKED = 'SUB_QUA_AFC_CHK',
  SUBSCRIBER_QUALIFICATION_AS_ENTITY_CHECKED = 'SUB_QUA_AEC_CHK',
  SUBSCRIBER_QUALIFICATION_NONE_CHECKED = 'SUB_QUA_ANC_CHK',
}

export const zodDocuSignGenerateAccessToken = zodObject({ access_token: zodString });
export type DocuSignGenerateAccessToken = z.infer<typeof zodDocuSignGenerateAccessToken>;

export const zodDocuSignUserInfo = zodObject({ accountId: zodString, baseUri: zodString, accountName: zodString, isDefault: zodString });
export const zodDocuSignUserInfoApiResponse = zodObject({ accounts: zodArray(zodDocuSignUserInfo), sub: zodString, email: zodString, name: zodString });

export type DocuSignUserInfo = z.infer<typeof zodDocuSignUserInfo>;

export const zodDocuSignEnvelopeTags = zodObject({ [DocuSignSupportedTagsEnum.COMMITMENT_AMOUNT]: zodString });
export type DocuSignAvailableEnvelopeTags = {
  [K in DocuSignSupportedTagsEnum]: z.infer<typeof zodString>;
};

export enum DocuSignWebHookEventsEnum {
  ENVELOPE_COMPLETED = 'envelope-completed',
  ENVELOPE_DELIVERED = 'envelope-delivered',
  ENVELOPE_VOIDED = 'envelope-voided',
  ENVELOPE_DECLINED = 'envelope-declined',
  RECIPIENT_COMPLETED = 'recipient-completed',
}

export const zodDocuSignWebHookDataTextCustomField = zodObject({ fieldId: zodString, name: zodString, value: zodString });
export type DocuSignWebHookDataTextCustomField = z.infer<typeof zodDocuSignWebHookDataTextCustomField>;

export const zodDocuSignWebHookDataSummary = zodObject({ status: zodString, customFields: zodObject({ textCustomFields: zodArray(zodDocuSignWebHookDataTextCustomField) }) });
export type DocuSignWebHookDataSummary = z.infer<typeof zodDocuSignWebHookDataSummary>;

export const zodDocuSignWebHookData = zodObject({ accountId: zodString, envelopeId: zodString, envelopeSummary: zodDocuSignWebHookDataSummary });
export type DocuSignWebHookData = z.infer<typeof zodDocuSignWebHookData>;

export type DocuSignGenerateTemplateRequest = {
  fileKey: string;
  name: string;
};

export type DocuSignGenerateEmbeddedSigningUrlRecipientItem = {
  email: string;
  name: string;
};

export type DocuSignGenerateEmbeddedSigningUrlRequest = {
  fileKey: string;
  investorEmail: string;
  investorName: string;
  investorId: string;
  templateId: string;
  offeringId: string;
  recipients?: DocuSignGenerateEmbeddedSigningUrlRecipientItem[];
};

export type DocuSignGenerateAccessTokenResponse = {
  access_token: string;
};

export type DocuSignUserInfoAccountItem = {
  accountId: string;
  baseUri: string;
  accountName: string;
  isDefault?: string;
};

export type DocuSignUserInfoResponse = {
  email: string;
  name: string;
  sub: string;
  accounts: DocuSignUserInfoAccountItem[];
};

export type DocuSignGenerateEmbeddedUrlResponse = {
  redirectUrl: string;
  userId: string;
  signingDocumentId: string;
};

export type DocuSignWebhookDataEnvelopeSummaryCustomFieldItem = {
  fieldId: string;
  name: string;
  value: string;
};

export type DocuSignWebhookDataEnvelopeSummaryCustomField = {
  textCustomFields: DocuSignWebhookDataEnvelopeSummaryCustomFieldItem[];
};

export type DocuSignWebhookDataEnvelopeSummaryItem = {
  status: string;
  customFields: DocuSignWebhookDataEnvelopeSummaryCustomField;
};

export type DocuSignWebhookDataItem = {
  envelopeId: string;
  accountId: string;
  envelopeSummary: DocuSignWebhookDataEnvelopeSummaryItem;
};

export type DocuSignWebhookRequest = {
  event: DocuSignWebHookEventsEnum;
  data: DocuSignWebhookDataItem;
};
