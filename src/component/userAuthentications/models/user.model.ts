import mongoose, { Schema, Document } from 'mongoose';
import * as bcrypt from 'bcrypt';
import { KycStatusEnum, UserTypeEnum, IssuerStatusEnum, sumSubKycStatusEnum, otpMethodsEnum } from '../../../utils/common.interface';
import { RES_MSG } from '../../../utils/responseUtils';

/**
 * Complete user model interface for authentication, profile management, and compliance tracking.
 * Defines the comprehensive data structure for users in the tokenization platform including
 * personal information, security settings, KYC status, and role-based attributes.
 * Extends Mongoose Document for database operations and type safety.
 *
 * @interface IUserModel
 * @extends {Document}
 * @description Complete user data model with authentication, verification, and compliance features
 *
 * // User with full profile
 * const investor: IUserModel = {
 *   name: '<PERSON>',
 */
export interface IUserModel extends Document {
  /**
   * Unique user identifier from MongoDB.
   * Used for database operations and user references across the system.
   *
   * @type {string}
   */
  _id: string;

  /**
   * International country code for user's mobile number.
   * Used for SMS delivery and phone number validation.
   * Required for non-social media registrations.
   *
   * @type {string}
   */
  countryCode?: string;

  /**
   * Account creation timestamp.
   * Automatically set when user account is created.
   *
   * @type {Date}
   */
  createdAt?: Date;

  /**
   * User's date of birth for age verification and compliance.
   * Used for KYC validation and regulatory compliance checks.
   *
   * @type {string}
   * @format ISO date string (YYYY-MM-DD)
   */
  dob?: string;

  /**
   * User's email address for authentication and communication.
   * Must be unique across the platform and properly formatted.
   * Primary identifier for login and account recovery.
   *
   * @type {string}
   * @required
   * @unique
   */
  email?: string;

  /**
   * Two-factor authentication activation status.
   * Indicates whether 2FA is currently enabled for enhanced security.
   *
   * @type {boolean}
   * @default false
   */
  is2FAActive?: boolean;

  /**
   * Account activation status for platform access.
   * Controls whether user can access platform features.
   *
   * @type {boolean}
   * @required
   * @default true
   */
  isActive: boolean;

  /**
   * Soft deletion flag for account management.
   * Allows account recovery while maintaining data integrity.
   *
   * @type {boolean}
   * @default false
   */
  isDeleted?: boolean;

  /**
   * Email verification status for account security.
   * Must be true for full platform access and secure operations.
   *
   * @type {boolean}
   * @required
   * @default false
   */
  isEmailVerify: boolean;

  /**
   * Issuer role designation for asset tokenization capabilities.
   * Grants permission to create and manage tokenized offerings.
   *
   * @type {boolean}
   * @default false
   */
  isIssuer?: boolean;

  /**
   * KYC (Know Your Customer) completion status.
   * Required for investment activities and compliance.
   *
   * @type {boolean}
   * @default false
   */
  isKyc?: boolean;

  /**
   * On-chain identity identifier for blockchain operations.
   * Links user account to blockchain identity and smart contracts.
   *
   * @type {string}
   */
  onchainID?: string;

  /**
   * Mobile number verification status for SMS capabilities.
   * Enables SMS-based OTP and notifications.
   *
   * @type {boolean}
   * @default false
   */
  isMobileVerify?: boolean;

  /**
   * OTP (One-Time Password) system activation status.
   * Controls availability of OTP-based verification methods.
   *
   * @type {boolean}
   * @default true
   */
  isOtpActive?: boolean;

  /**
   * Social media authentication flag.
   * Indicates if account was created via social login.
   *
   * @type {boolean}
   * @default false
   */
  isSocialMedia?: boolean;

  /**
   * Reason provided for issuer status application.
   * Administrative field for issuer approval workflow.
   *
   * @type {string}
   */
  issuerReason?: string;

  /**
   * Current issuer application status.
   * Tracks approval workflow for issuer capabilities.
   *
   * @type {IssuerStatusEnum}
   * @values "PENDING", "APPROVED", "REJECTED", "UNDER_REVIEW"
   */
  issuerStatus?: IssuerStatusEnum;

  /**
   * KYC application identifier for third-party KYC services.
   * Links to external KYC provider systems for verification tracking.
   *
   * @type {string}
   * @required
   */
  kycApplicationId: string;

  /**
   * Number of KYC attempts for compliance tracking.
   * Used for rate limiting and fraud prevention.
   *
   * @type {number}
   * @default 0
   */
  kycCount?: number;

  /**
   * Reason for KYC rejection or status change.
   * Provides feedback for KYC approval workflow.
   *
   * @type {string}
   */
  kycReason?: string;

  /**
   * Current KYC verification status.
   * Determines user's compliance level and platform access.
   *
   * @type {KycStatusEnum}
   * @values "PENDING", "APPROVED", "REJECTED", "INCOMPLETE", "UNDER_REVIEW"
   */
  kycStatus?: KycStatusEnum;

  /**
   * KYC verification level name from external provider.
   * Indicates the tier of verification achieved.
   *
   * @type {string}
   */
  levelName?: string;

  /**
   * User's mobile phone number for SMS and verification.
   * Required for non-social media accounts and 2FA setup.
   *
   * @type {string}
   */
  mobile?: string;

  /**
   * User's full name for identification and legal purposes.
   * Used for KYC verification and legal document generation.
   *
   * @type {string}
   * @required
   */
  name?: string;

  /**
   * Hashed password for authentication security.
   * Stored using bcrypt for secure password management.
   * Not required for social media accounts.
   *
   * @type {string}
   * @encrypted
   */
  password?: string;

  /**
   * SumSub KYC provider specific status.
   * Integration status with SumSub verification service.
   *
   * @type {sumSubKycStatusEnum}
   * @values Provider-specific status codes
   */
  sumSubKycStatus?: sumSubKycStatusEnum;

  /**
   * Two-factor authentication secret key.
   * TOTP secret for generating time-based authentication codes.
   *
   * @type {string}
   * @encrypted
   */
  twoFASecret?: string;

  /**
   * Last profile update timestamp.
   * Tracks when user information was last modified.
   *
   * @type {Date}
   */
  updatedAt?: Date;

  /**
   * Profile image URL for user avatar.
   * Link to uploaded profile picture in cloud storage.
   *
   * @type {string}
   * @format URL
   */
  userImage?: string;

  /**
   * User role type for platform access control.
   * Determines available features and permissions.
   *
   * @type {UserTypeEnum}
   * @values "investor", "issuer", "representative", "admin"
   */
  userType?: UserTypeEnum;

  /**
   * Webhook processing status for external integrations.
   * Tracks webhook delivery and processing state.
   *
   * @type {string}
   */
  webhookReceived?: string;

  /**
   * Legal full name for compliance and regulatory purposes.
   * May differ from display name for legal document accuracy.
   *
   * @type {string}
   */
  legalFullName?: string;

  /**
   * Preferred OTP delivery methods for verification.
   * Controls how one-time passwords are sent to user.
   *
   * @type {otpMethodsEnum}
   * @values "email", "sms", "both"
   */
  otpMethods?: otpMethodsEnum;

  /**
   * Representative role designation flag.
   * Indicates if user can act as a representative for others.
   *
   * @type {boolean}
   * @default false
   */
  isRepresentatives?: boolean;

  /**
   * List of user IDs who invited this user.
   * Tracks invitation chain for referral and onboarding analytics.
   *
   * @type {Array<string>}
   * @default []
   */
  invitedBy?: Array<string>;

  /**
   * Multi-signature wallet address for enhanced security.
   * Blockchain address requiring multiple signatures for transactions.
   *
   * @type {string}
   * @format Ethereum address
   */
  multisigAddress?: string;
}

/**
 * User update interface for partial profile modifications.
 * Provides type-safe structure for user data updates with all optional fields.
 * Used for profile updates, status changes, and administrative operations.
 *
 * @interface IUpdateUserModel
 * @description Flexible user update model with optional fields for partial updates
 *
 * // Status update
 * const statusUpdate: IUpdateUserModel = {
 *   isEmailVerify: true,
 *   kycStatus: 'APPROVED',
 *   isActive: true
 * };
 *
 * // Security update
 * const securityUpdate: IUpdateUserModel = {
 *   is2FAActive: true,
 *   twoFASecret: 'encrypted_secret',
 *   password: 'new_hashed_password'
 * };
 * ```
 */
export interface IUpdateUserModel {
  /**
   * User identifier for update operations.
   * @type {string}
   */
  _id?: string;

  /**
   * Updated country code for mobile number.
   * @type {string}
   */
  countryCode?: string;

  /**
   * Updated date of birth.
   * @type {string}
   */
  dob?: string;

  /**
   * Updated 2FA activation status.
   * @type {boolean}
   */
  is2FAActive?: boolean;

  /**
   * Updated account activation status.
   * @type {boolean}
   */
  isActive?: boolean;

  /**
   * Updated email verification status.
   * @type {boolean}
   */
  isEmailVerify?: boolean;

  /**
   * Updated issuer role designation.
   * @type {boolean}
   */
  isIssuer?: boolean;

  /**
   * Updated KYC completion status.
   * @type {boolean}
   */
  isKyc?: boolean;

  /**
   * Updated mobile verification status.
   * @type {boolean}
   */
  isMobileVerify?: boolean;

  /**
   * Updated OTP system activation.
   * @type {boolean}
   */
  isOtpActive?: boolean;

  /**
   * Updated social media account flag.
   * @type {boolean}
   */
  isSocialMedia?: boolean;

  /**
   * Updated issuer application reason.
   * @type {string}
   */
  issuerReason?: string;

  /**
   * Updated issuer status.
   * @type {IssuerStatusEnum}
   */
  issuerStatus?: IssuerStatusEnum;

  /**
   * Updated KYC application ID.
   * @type {string}
   */
  kycApplicationId?: string;

  /**
   * Updated KYC attempt count.
   * @type {number}
   */
  kycCount?: number;

  /**
   * Updated on-chain identity.
   * @type {string}
   */
  onchainID?: string;

  /**
   * Updated KYC rejection reason.
   * @type {string}
   */
  kycReason?: string;

  /**
   * Updated KYC verification status.
   * @type {KycStatusEnum}
   */
  kycStatus?: KycStatusEnum;

  /**
   * Updated KYC level name.
   * @type {string}
   */
  levelName?: string;

  /**
   * Updated mobile number.
   * @type {string}
   */
  mobile?: string;

  /**
   * Updated email address.
   * @type {string}
   */
  email?: string;

  /**
   * Updated full name.
   * @type {string}
   */
  name?: string;

  /**
   * Updated password hash.
   * @type {string}
   */
  password?: string;

  /**
   * Updated SumSub KYC status.
   * @type {string}
   */
  sumSubKycStatus?: string;

  /**
   * Updated 2FA secret key.
   * @type {string}
   */
  twoFASecret?: string;

  /**
   * Updated profile image URL.
   * @type {string}
   */
  userImage?: string;

  /**
   * Updated user role type.
   * @type {UserTypeEnum}
   */
  userType?: UserTypeEnum;

  /**
   * Updated webhook status.
   * @type {string}
   */
  webhookReceived?: string;

  /**
   * Updated legal full name.
   * @type {string}
   */
  legalFullName?: string;

  /**
   * Updated OTP delivery methods.
   * @type {otpMethodsEnum}
   */
  otpMethods?: otpMethodsEnum;

  /**
   * Updated representative status.
   * @type {boolean}
   */
  isRepresentatives?: boolean;

  /**
   * Updated invitation chain.
   * @type {Array<string>}
   */
  invitedBy?: Array<string>;

  /**
   * Updated multi-signature address.
   * @type {string}
   */
  multisigAddress?: string;
}

/**
 * Mongoose schema definition for user data structure with comprehensive validation and security.
 * Defines database structure, validation rules, and data relationships for user accounts.
 * Includes conditional field requirements based on registration method and user type.
 *
 * @constant {Schema<IUserModel>} UserSchema - Configured Mongoose schema for user data
 *
 * @description
 * Schema features:
 * - Conditional field requirements based on account type
 * - Unique constraints for email addresses
 * - Password hashing middleware for security
 * - Index optimization for performance
 * - Validation rules for data integrity
 * - Default values for optional fields
 *
 * await user.save(); // Password automatically hashed before save
 * ```
 */
const UserSchema: Schema<IUserModel> = new Schema(
  {
    name: { type: String, required: true },
    legalFullName: { type: String, required: false },
    email: { type: String, required: true, unique: true, index: true },
    onchainID: { type: String, default: null },
    password: {
      type: String,
      required() {
        return !this.isSocialMedia;
      },
    },
    countryCode: {
      type: String,
      required() {
        return !this.isSocialMedia;
      },
    },
    mobile: {
      type: String,
      required() {
        return !this.isSocialMedia;
      },
      unique: true,
      index: true,
    },
    dob: { type: String, required: false, default: '' },
    isActive: { type: Boolean, default: true },
    isEmailVerify: { type: Boolean, default: false },
    kycApplicationId: { type: String, required: false },
    sumSubKycStatus: { type: String, enum: sumSubKycStatusEnum, required: false, default: sumSubKycStatusEnum.NOT_STARTED },
    webhookReceived: { type: String, required: false },
    levelName: { type: String, required: false },

    isMobileVerify: { type: Boolean, default: false },
    is2FAActive: { type: Boolean, default: false },
    twoFASecret: { type: String, required: false },
    isOtpActive: { type: Boolean, default: true },
    isDeleted: { type: Boolean, default: false },
    isSocialMedia: { type: Boolean, default: false },
    userType: { type: String, enum: UserTypeEnum, required: true },
    isKyc: { type: Boolean, default: false },
    kycStatus: { type: String, enum: KycStatusEnum, default: KycStatusEnum.NOT_STARTED },
    userImage: { type: String, default: null },
    kycCount: { type: Number, default: null },
    kycReason: { type: String, required: false },
    issuerReason: { type: String, required: false },
    isIssuer: { type: Boolean, default: false },
    issuerStatus: { type: String, enum: IssuerStatusEnum, default: IssuerStatusEnum.NOT_APPLIED },
    isRepresentatives: { type: Boolean, default: false },
    invitedBy: { type: [String], ref: 'User', required: false, default: [] },
    multisigAddress: { type: String, required: false, default: '' },
  },
  { timestamps: true, versionKey: false },
);

UserSchema.post('save', (error: any, doc: any, next: any) => {
  if (error.code === 11000) {
    if (error.keyPattern && error.keyPattern.mobile) {
      next(new Error(RES_MSG.ERROR_MSG.MOBILE_NO_EXIST));
    } else if (error.keyPattern && error.keyPattern.email) {
      next(new Error(RES_MSG.USER.USER_ALREADY_EXIST));
    } else if (error.keyPattern && error.keyPattern.mainInformation.nationalIdNumber) {
      next(new Error(RES_MSG.ERROR_MSG.ID_ERR));
    } else {
      next(new Error('Duplicate key error.'));
    }
  } else {
    next(error);
  }
});
UserSchema.pre('save', async function (next) {
  const user = this as IUserModel;
  if (!user.isModified('password')) {
    return next();
  }

  try {
    const salt = await bcrypt.genSalt(10);
    user.password = await bcrypt.hash(user.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

async function hashPassword(next: any) {
  const update = this.getUpdate();

  if (update.password) {
    try {
      const salt = await bcrypt.genSalt(10);
      update.password = await bcrypt.hash(update.password, salt);
    } catch (error) {
      next(error);
    }
  }
  next();
}

UserSchema.pre('updateOne', hashPassword);
UserSchema.pre('findOneAndUpdate', hashPassword);

const userSchema = mongoose.model<IUserModel>('User', UserSchema);
export { userSchema };
