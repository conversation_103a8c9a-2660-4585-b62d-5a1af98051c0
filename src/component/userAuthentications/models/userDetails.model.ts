/* eslint-disable no-use-before-define */
/* eslint-disable no-await-in-loop */
/* eslint-disable no-restricted-syntax */
import mongoose, { Schema, Document } from 'mongoose';
import { RESPONSES } from '../../../utils/responseUtils';
import CustomError from '../../../helpers/customError.helper';

export interface IAddress {
  address: string;
  address2?: string;
  country: string;
  state: string;
  city: string;
  zipCode: string;
}

export interface IUserDetails extends Document {
  _id: string | object;
  mainInformation?: {
    birthPlace: string;
    nationality: string;
    nationalIdNumber: string;
    identificationDocument: string;
    documentExpiration: string;
    occupation: string;
    dob?: string;
  };
  institutions: {
    companyInformation: {
      name: string;
      entityType: string;
      webSite?: string;
      business: string;
      sourceOfFunds: string;
    };
    address: IAddress;
  };
  primaryContactInfo: {
    personalInformation: {
      name: string;
      jobTitle: string;
      dob?: string;
      socialSecurityNumber: string;
      citizenship: string;
      countryCode: string;
      mobile: string;
      email: string;
    };
    address: IAddress;
  };
  wallets: Array<{
    type: string;
    address: string;
    timestamp: Date;
    isVerify: boolean;
    signerAddress: string;
  }>;
  isIdentityVerification?: {
    url: string;
    status: string;
    expireTime: string;
  };
  documents: {
    frontId: string;
    backId: string;
    otherIdentification: string;
  };
  beneficialOwners: Array<{
    personalInformation: {
      name: string;
      dob?: string;
      socialSecurityNumber: string;
      citizenship: string;
    };
    address: IAddress;
    identityProof: {
      passport?: {
        front?: string;
        back?: string;
      };
      driversLicense?: {
        front?: string;
        back?: string;
      };
      idCard?: {
        front?: string;
        back?: string;
      };
    };
  }>;
  managementInfo: Array<{
    personalInformation: {
      name: string;
      jobTitle: string;
      dob?: string;
      socialSecurityNumber: string;
      citizenship: string;
      countryCode: string;
      mobile: string;
      email: string;
    };
    address: IAddress;
  }>;
  kycSteps?: number;
  isFinalSubmission: boolean;
}

const AddressSchema: Schema<IAddress> = new Schema(
  { address: { type: String, required: true }, address2: { type: String }, country: { type: String, required: true }, state: { type: String, required: true }, city: { type: String, required: true }, zipCode: { type: String, required: true } },
  { _id: false },
);

const UserDetails: Schema<IUserDetails> = new Schema(
  {
    mainInformation: {
      birthPlace: { type: String, required: true },
      nationality: { type: String, required: true },
      nationalIdNumber: {
        type: String,
        required() {
          return !this.mainInformation;
        },
        unique() {
          return !this.mainInformation;
        },
        index: true,
        sparse: true,
      },
      identificationDocument: { type: String, required: true },
      documentExpiration: { type: String, required: true },
      occupation: { type: String, required: true },
      dob: { type: String, required: true },
    },
    institutions: {
      companyInformation: { name: { type: String, required: true }, entityType: { type: String, required: true }, webSite: { type: String }, business: { type: String, required: true }, sourceOfFunds: { type: String, required: true } },
      address: { type: AddressSchema, required: true },
    },
    primaryContactInfo: {
      personalInformation: {
        name: { type: String, required: true },
        jobTitle: { type: String, required: true },
        dob: { type: String, required: true },
        socialSecurityNumber: { type: String, required: true },
        citizenship: { type: String, required: true },
        countryCode: { type: String, required: true },
        mobile: { type: String, required: true },
        email: { type: String, required: true },
      },
      address: { type: AddressSchema, required: true },
    },
    wallets: {
      type: [
        {
          type: { type: String, required: true },
          address: { type: String, required: true, index: { unique: true, sparse: true }, unique: true, set: (value: string) => (value ? value.toLowerCase() : value) },
          timestamp: { type: Date, required: false, default: () => new Date() },
          isVerify: { type: Boolean, required: false, default: false },
          signerAddress: { type: String, required: false, default: '' },
        },
      ],
      _id: false,
      default: undefined,
    },
    isIdentityVerification: { url: { type: String, required: false }, status: { type: String, required: true }, expireTime: { type: String, required: false } },
    documents: { frontId: { type: String, required: true }, backId: { type: String, required: true }, otherIdentification: { type: String, required: true } },
    beneficialOwners: {
      type: [
        {
          personalInformation: { name: { type: String, required: true }, dob: { type: String, required: true }, socialSecurityNumber: { type: String, required: true }, citizenship: { type: String, required: true } },
          address: { type: AddressSchema, required: true },
          identityProof: { passport: { front: { type: String }, back: { type: String } }, driversLicense: { front: { type: String }, back: { type: String } }, idCard: { front: { type: String }, back: { type: String } } },
        },
      ],
      _id: false,
      default: undefined,
    },
    managementInfo: {
      type: [
        {
          personalInformation: {
            name: { type: String, required: true },
            jobTitle: { type: String, required: true },
            dob: { type: String, required: true },
            socialSecurityNumber: { type: String, required: true },
            citizenship: { type: String, required: true },
            countryCode: { type: String, required: true },
            mobile: { type: String, required: true },
            email: { type: String, required: true },
          },
          address: { type: AddressSchema, required: true },
        },
      ],
      _id: false,
      default: undefined,
    },
    kycSteps: { type: Number },
    isFinalSubmission: { type: Boolean, default: false },
  },
  { versionKey: false, timestamps: true },
);

UserDetails.pre('save', async function (next) {
  // eslint-disable-next-line @typescript-eslint/no-this-alias
  const user = this;

  // Iterate over wallets and check if the address is unique
  if (user.wallets && user.wallets.length > 0) {
    for (const wallet of user.wallets) {
      const existingUser = await UserDetailsSchema.findOne({ 'wallets.address': wallet.address });

      if (existingUser && existingUser._id.toString() !== user._id.toString()) {
        // const error = new Error(`Wallet address ${wallet.address} is already in use.`);
        const error = new CustomError('Wallet address is already in use.', RESPONSES.CONFLICT);

        return next(error);
      }
    }
  }
  next();
});

// Pre-update hook to check for unique wallet addresses during updates
UserDetails.pre('findOneAndUpdate', async function (next) {
  const update = this.getUpdate() as { wallets?: { address: string }[] };
  const userId = this.getQuery()._id;

  // If wallets are being updated, check for uniqueness
  if (update.wallets && update.wallets.length > 0) {
    for (const wallet of update.wallets) {
      const existingUser = await UserDetailsSchema.findOne({ 'wallets.address': wallet.address });

      if (existingUser && existingUser._id.toString() !== userId.toString()) {
        const error = new CustomError('Wallet address is already in use.', RESPONSES.CONFLICT);

        // const error = new Error(`Wallet address ${wallet.address} is already in use.`);
        return next(error);
      }
    }
  }
  next();
});

const UserDetailsSchema = mongoose.model<IUserDetails>('UserDetails', UserDetails);
export { UserDetailsSchema };
