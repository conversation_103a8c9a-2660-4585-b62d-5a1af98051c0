import { FilterQuery } from 'mongoose';
import { IPagination, PromiseResolve } from '../../utils/common.interface';
import { IUserModel, IUpdateUserModel } from './models/user.model';
import { IUserDetails } from './models/userDetails.model';

/**
 * Interface defining the contract for user authentication and management services.
 * Provides methods for user registration, authentication, profile management,
 * KYC operations, and portfolio tracking functionality.
 *
 * @interface IUserService
 * @description Service interface for comprehensive user management in the tokenization platform
 */
export interface IUserService {
  /**
   * Registers a new user in the system with initial account setup.
   * Creates user account, password history, and sends verification notifications.
   *
   * @method signUp
   * @param {IUserModel} body - Complete user registration data including email, password, mobile, and OTP preferences
   * @returns {Promise<PromiseResolve>} Promise resolving to registration success response with user data
   * @memberof IUserService
   *
   * @description
   * Registration process includes:
   * - Email normalization and validation
   * - Account creation with initial status
   * - Password history initialization
   * - OTP method configuration
   */
  signUp(body: IUserModel): Promise<PromiseResolve>;

  /**
   * Creates a new user account through administrative or system processes.
   * Alternative user creation method for batch imports or admin-initiated accounts.
   *
   * @method createUser
   * @param {any} body - User data object with flexible structure for different creation scenarios
   * @returns {Promise<PromiseResolve>} Promise resolving to user creation response with account details
   * @memberof IUserService
   *
   * @description
   * Used for:
   * - Administrative user creation
   * - Bulk user imports
   * - System-generated accounts
   * - Third-party integrations
   */
  createUser(body: any): Promise<PromiseResolve>;

  /**
   * Retrieves user details based on flexible search criteria.
   * Supports field selection, exclusion, and optional KYC data inclusion.
   *
   * @method fetchUserDetails
   * @param {FilterQuery<IUserModel>} searchDetails - MongoDB query filter for user lookup (email, mobile, username, ID)
   * @param {string[]} [fields] - Optional array of specific fields to include in response
   * @param {string[]} [excludeFields] - Optional array of fields to exclude from response
   * @param {boolean} [isKyc] - Whether to include KYC verification details in response
   * @returns {Promise<PromiseResolve>} Promise resolving to user data with optional KYC information
   * @memberof IUserService
   *
   * @description
   * Flexible user lookup supporting:
   * - Email or mobile-based search
   * - Username-based authentication
   * - Field projection for optimized responses
   * - KYC data integration when required
   */
  fetchUserDetails(searchDetails: FilterQuery<IUserModel>, fields?: string[], excludeFields?: string[], isKyc?: boolean): Promise<PromiseResolve>;

  /**
   * Updates user profile information and account settings.
   * Handles profile updates with validation, notifications, and audit trail.
   *
   * @method updateUserDetails
   * @param {IUpdateUserModel} body - User data to update including profile, preferences, and settings
   * @param {FilterQuery<IUserModel>} filter - MongoDB filter to identify the user document to update
   * @returns {Promise<PromiseResolve>} Promise resolving to updated user data and confirmation
   * @memberof IUserService
   *
   * @description
   * Update process includes:
   * - Data validation and sanitization
   * - Admin notifications via Kafka
   * - Real-time socket notifications
   * - Audit trail maintenance
   */
  updateUserDetails(body: IUpdateUserModel, filter: FilterQuery<IUserModel>): Promise<PromiseResolve>;

  /**
   * Adds a new password to user's password history for security tracking.
   * Maintains password history for preventing reuse and security compliance.
   *
   * @method addPassword
   * @param {string} _id - User's unique identifier in the database
   * @param {string} password - Hashed password to add to user's password history
   * @returns {Promise<PromiseResolve>} Promise resolving to password addition confirmation
   * @memberof IUserService
   *
   * @description
   * Password management features:
   * - Prevents password reuse within history limit
   * - Maintains configurable password history length
   * - Ensures password uniqueness validation
   * - Supports security compliance requirements
   */
  addPassword(_id: string, password: string): Promise<PromiseResolve>;

  /**
   * Retrieves user's recent password history for validation purposes.
   * Used for password change validation and security policy enforcement.
   *
   * @method fetchRecentPasswords
   * @param {string} _id - User's unique identifier to fetch password history for
   * @returns {Promise<PromiseResolve>} Promise resolving to user's password history data
   * @memberof IUserService
   *
   * @description
   * Used for:
   * - Password reuse prevention
   * - Security policy compliance
   * - Account security auditing
   * - Password strength analysis
   */
  fetchRecentPasswords(_id: string): Promise<PromiseResolve>;

  /**
   * Retrieves user's investment portfolio with detailed asset holdings and performance metrics.
   * Provides comprehensive portfolio view with current values, gains/losses, and pagination.
   *
   * @method fetchUserPortfolio
   * @param {string} userId - User's unique identifier to fetch portfolio for
   * @param {any} isCsv - Flag indicating whether to return data in CSV-friendly format
   * @param {IPagination} pagination - Pagination parameters including page, limit, and search query
   * @param {string} [offeringId] - Optional offering ID to filter portfolio by specific asset
   * @returns {Promise<PromiseResolve>} Promise resolving to portfolio data with performance metrics
   * @memberof IUserService
   *
   * @description
   * Portfolio data includes:
   * - Current token holdings and values
   * - Investment amounts and average prices
   * - Profit/loss calculations and percentages
   * - Asset performance metrics
   * - Transaction history aggregation
   * - Pagination and search capabilities
   */
  fetchUserPortfolio(userId: string, isCsv: any, pagination: IPagination, offeringId?: string): Promise<PromiseResolve>;

  /**
   * Updates user's KYC (Know Your Customer) verification details and compliance status.
   * Manages identity verification data, compliance status, and regulatory requirements.
   *
   * @method updateUserKycDetails
   * @param {IUserDetails} body - KYC verification data including identity documents and compliance info
   * @param {FilterQuery<IUserDetails>} filter - MongoDB filter to identify the user's KYC record
   * @returns {Promise<PromiseResolve>} Promise resolving to updated KYC status and verification details
   * @memberof IUserService
   *
   * @description
   * KYC management includes:
   * - Identity document processing
   * - Compliance status tracking
   * - Regulatory requirement validation
   * - Verification workflow management
   * - Risk assessment integration
   */
  updateUserKycDetails(body: IUserDetails, filter: FilterQuery<IUserDetails>): Promise<PromiseResolve>;
}
