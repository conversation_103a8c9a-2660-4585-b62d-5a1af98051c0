import { FilterQuery, Types } from 'mongoose';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import { PromiseResolve, queueMessageTypeEnum, IPagination } from '../../utils/common.interface';
import { IUserModel, IUpdateUserModel, userSchema } from './models/user.model';
import { maxPasswordHistory } from '../../utils/constant';
import logger from '../../helpers/logging/logger.helper';
import { IUserDetails, UserDetailsSchema } from './models/userDetails.model';
import kafkaService from '../../service/kafkaService';
import { PasswordModel, passwordSchema } from './models/password.model';
import { IUserService } from './interface';
import { OrderSchema } from '../order/models/order.model';
import { socketHelper } from '../../helpers/socket.helper';

/**
 * Service class providing comprehensive user management functionality for the tokenization platform.
 * Handles user registration, authentication, profile management, KYC operations, and portfolio tracking.
 * Implements security features including password history, real-time notifications, and audit trails.
 *
 * @class UserService
 * @implements {IUserService}
 * @description Core user management service with authentication, profile, and portfolio capabilities
 *
 * // Fetch user portfolio
 * const portfolio = await UserService.fetchUserPortfolio(
 *   'userId123',
 *   false,
 *   { page: 1, limit: 10, search: '' }
 * );
 * ```
 */
class UserService implements IUserService {
  /**
   * Registers a new user account with complete profile setup and security initialization.
   * Creates user account, establishes password history, and sends verification notifications.
   * Includes email normalization, validation, and initial account configuration.
   *
   * @async
   * @method signUp
   * @param {IUserModel} body - Complete user registration data including credentials and preferences
   * @param {string} body.email - User's email address (will be normalized to lowercase)
   * @param {string} body.password - User's hashed password for account security
   * @param {string} body.mobile - User's mobile number for SMS verification
   * @param {string} body.countryCode - Country code for mobile number validation
   * @param {string} body.otpMethods - Preferred OTP delivery method ('email', 'sms', or 'both')
   * @returns {Promise<PromiseResolve>} Promise resolving to registration response with user data
   * @throws {Error} Throws error if user already exists or registration fails
   * @memberof UserService
   *
   * @description
   * Registration process:
   * 1. Normalizes email to lowercase for consistency
   * 2. Creates user account with provided details
   * 3. Initializes password history for security
   * 4. Returns success response with user object
   *
   */
  signUp = async (body: IUserModel): Promise<PromiseResolve> => {
    try {
      body.email = body.email.toLowerCase();
      const createQuery: IUserModel = await userSchema.create(body);
      if (createQuery) {
        const userObject = createQuery.toObject();
        await this.addPassword(createQuery._id, createQuery.password);

        return { status: RESPONSES.CREATED, error: false, message: RES_MSG.SUCCESS_MSG.REGISTER_SUCCESS.replace('{methods}', body.otpMethods), data: userObject };
      }

      return { status: RESPONSES.BAD_REQUEST, error: true, message: RES_MSG.COMMON.SOMETHING_WRONG };
    } catch (error) {
      logger.error(error, 'signUp error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * Retrieves user details from the database using flexible search criteria.
   * Supports various lookup methods including email, mobile, username, and MongoDB filters.
   * Provides field selection capabilities and optional KYC data inclusion.
   *
   * @async
   * @method fetchUserDetails
   * @param {FilterQuery<IUserModel>} searchDetails - MongoDB query filter for user lookup
   * @param {FilterQuery<IUserModel>} searchDetails.userName - Username for email/mobile lookup
   * @param {string} [searchDetails.email] - Email address for direct lookup
   * @param {string} [searchDetails.mobile] - Mobile number for direct lookup
   * @param {string[]} [fields] - Specific fields to include in response (whitelist)
   * @param {string[]} [excludeFields] - Fields to exclude from response (blacklist)
   * @param {boolean} [isKyc=false] - Whether to include KYC verification details
   * @returns {Promise<PromiseResolve>} Promise resolving to user data with optional KYC info
   * @throws {Error} Throws error if user not found or database operation fails
   * @memberof UserService
   *
   * @description
   * Search functionality:
   * - Supports username (email/mobile) or direct field matching
   * - Automatic email normalization for consistent lookups
   * - Field projection for optimized database queries
   * - Optional KYC data integration from separate collection
   * - Comprehensive error handling with detailed logging
   *
   * // Direct field search
   * const user = await UserService.fetchUserDetails(
   */
  fetchUserDetails = async (searchDetails: FilterQuery<IUserModel>, fields?: string[], excludeFields?: string[], isKyc: boolean = false): Promise<PromiseResolve> => {
    try {
      let kycDetails: IUserDetails;
      let query: FilterQuery<IUserModel>;
      if (searchDetails?.userName) {
        query = { $or: [{ email: searchDetails.userName.toLowerCase() }, { mobile: searchDetails.userName.toLowerCase() }] };
      } else {
        query = searchDetails;
      }
      const userDetailsQuery = userSchema.findOne(query);
      // const userDetailsQuery = userSchema.findOne(searchDetails);
      if (fields && fields.length > 0) {
        const fieldsString = fields.join(' ');
        userDetailsQuery.select(fieldsString);
      }

      if (excludeFields && excludeFields.length > 0) {
        const excludeFieldsString = excludeFields.map((field) => `-${field}`).join(' ');
        userDetailsQuery.select(excludeFieldsString);
      }

      const userDetails: IUserModel = await userDetailsQuery.exec();

      if (isKyc) {
        kycDetails = await UserDetailsSchema.findOne({ _id: userDetails._id });
      }

      if (userDetails && userDetails.email) {
        return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS, data: { ...userDetails?.toObject(), kycDetails: kycDetails ? kycDetails.toObject() : {} } };
      }

      return { status: RESPONSES.NOTFOUND, error: true, message: RES_MSG.COMMON.NO_USER };
    } catch (error) {
      logger.error(error, 'fetchUserDetails error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * Retrieves comprehensive user portfolio data with detailed asset holdings and performance metrics.
   * Aggregates order data to calculate current positions, investment amounts, and profit/loss figures.
   * Supports pagination, search filtering, and CSV export functionality.
   *
   * @async
   * @method fetchUserPortfolio
   * @param {string} userId - User's unique identifier for portfolio lookup
   * @param {any} isCsv - Flag indicating CSV export format (bypasses pagination when true)
   * @param {IPagination} pagination - Pagination and search parameters
   * @param {number} pagination.page - Page number for pagination (default: 1)
   * @param {number} pagination.limit - Items per page (default: 10)
   * @param {string} [pagination.search] - Search query for filtering by offering name or token address
   * @param {string} [offeringId] - Optional offering ID to filter portfolio by specific asset
   * @returns {Promise<PromiseResolve>} Promise resolving to portfolio data with performance metrics and pagination
   * @throws {Error} Throws error if portfolio calculation fails or database operation errors
   * @memberof UserService
   *
   * @description
   * Portfolio calculation includes:
   * - Current token holdings from all transaction types (MINTED, BURN, REDEEM, TRANSFER, CONVERT)
   * - Investment amounts and average price per token calculations
   * - Current market values and profit/loss computations
   * - Percentage gain/loss calculations with precision handling
   * - Offering details integration (name, icons, token address)
   * - Search functionality across offering names and token addresses
   * - Pagination with total count and navigation metadata
   *
   * Portfolio metrics calculated:
   * - currentQuantity: Net token balance after all transactions
   * - totalInvestedAmount: Total amount invested (purchases minus sales)
   * - averagePricePerToken: Weighted average purchase price
   * - currentValue: Current market value of holdings
   * - profit: Absolute profit/loss amount
   * - totalGainLossPercentage: Percentage gain/loss on investment
   *
   * // Export full portfolio as CSV
   * const csvData = await UserService.fetchUserPortfolio(
   *   'user123',
   *   true,
   *   { page: 1, limit: 10 }
   * );
   *
   * // Get portfolio for specific offering
   * const offeringPortfolio = await UserService.fetchUserPortfolio(
   *   'user123',
   *   false,
   *   { page: 1, limit: 10 },
   *   'offering456'
   * );
   * ```
   */
  fetchUserPortfolio = async (userId: string, isCsv: any, pagination: IPagination, offeringId?: string): Promise<PromiseResolve> => {
    try {
      const { page = 1, limit = 10, search: searchQuery } = pagination;
      // const skip = (page - 1) * limit; // Calculate the number of documents to skip
      const matchQuery: any = {
        userId: new Types.ObjectId(userId),
        status: { $in: ['MINTED', 'BURN', 'REDEEM', 'TRANSFER_FROM', 'TRANSFER_TO', 'CONVERT_TO', 'CONVERT_FROM'] },
      };

      if (offeringId) {
        matchQuery.offeringId = new Types.ObjectId(offeringId);
      }

      const portfolioPipeline = [
        { $match: matchQuery },

        // Convert string fields to numbers
        {
          $addFields: {
            quantity: { $toDecimal: '$quantity' },
            amount: { $toDecimal: '$amount' },
            amountBeforeFee: { $toDecimal: '$amountBeforeFee' },
            feesInPercentage: { $toDecimal: '$feesInPercentage' },
            currentPrice: { $toDecimal: '$currentPrice' },
            wap: { $toDecimal: '$wap' },
          },
        },

        // Lookup details from offerings collection
        {
          $lookup: {
            from: 'offerings',
            localField: 'offeringId',
            foreignField: '_id',
            as: 'offeringDetails',
          },
        },
        { $unwind: '$offeringDetails' },

        // Group data
        {
          $group: {
            _id: '$offeringId',
            tokenDecimal: { $first: '$offeringDetails.projectDetails.tokenDecimal' },
            tokenAddress: { $first: '$offeringDetails.tokenAddress' },
            offeringName: { $first: '$offeringDetails.projectDetails.offeringName' },
            icon: { $first: '$offeringDetails.overview.icon' },
            cover: { $first: '$offeringDetails.overview.cover' },
            logo: { $first: '$offeringDetails.overview.logo' },
            userId: { $first: '$userId' },
            lastUpdated: { $first: '$updatedAt' },
            orderMinted: { $first: '$orderMinted' },
            orderId: { $first: '$_id' },
            currentPrice: { $first: '$currentPrice' },

            // Calculate current quantity
            currentQuantity: {
              $sum: {
                $switch: {
                  branches: [
                    { case: { $eq: ['$status', 'MINTED'] }, then: '$quantity' },
                    { case: { $eq: ['$status', 'TRANSFER_TO'] }, then: '$quantity' },
                    { case: { $eq: ['$status', 'CONVERT_TO'] }, then: '$quantity' },
                    { case: { $in: ['$status', ['BURN', 'REDEEM', 'TRANSFER_FROM', 'CONVERT_FROM']] }, then: { $multiply: ['$quantity', -1] } },
                  ],
                  default: 0,
                },
              },
            },

            // Calculate total invested amount
            totalInvestedAmount: {
              $sum: {
                $switch: {
                  branches: [
                    { case: { $eq: ['$status', 'MINTED'] }, then: '$amount' },
                    { case: { $eq: ['$status', 'TRANSFER_TO'] }, then: '$amount' },
                    { case: { $eq: ['$status', 'CONVERT_TO'] }, then: '$amountBeforeFee' },
                    { case: { $eq: ['$status', 'REDEEM'] }, then: { $multiply: ['$wap', -1] } },
                    { case: { $eq: ['$status', 'CONVERT_FROM'] }, then: { $multiply: ['$amountBeforeFee', -1] } },
                    { case: { $in: ['$status', ['BURN', 'TRANSFER_FROM']] }, then: { $multiply: ['$amount', -1] } },
                  ],
                  default: 0,
                },
              },
            },
          },
        },

        // Add computed fields for price per token
        {
          $addFields: {
            averagePricePerToken: {
              $cond: {
                if: { $eq: ['$currentQuantity', 0] },
                then: 0,
                else: { $divide: [{ $toDecimal: '$totalInvestedAmount' }, { $toDecimal: '$currentQuantity' }] },
              },
            },
          },
        },

        // Final projection
        {
          $project: {
            _id: 0,
            userId: 1,
            offeringId: '$_id',
            totalSupply: 1,
            tokenAddress: 1,
            totalInvestedAmount: 1,
            offeringName: 1,
            icon: 1,
            logo: 1,
            cover: 1,
            latestNav: 1,
            averagePricePerToken: 1,
            currentPrice: '$currentPrice',
            orderMinted: 1,
            currentQuantity: 1,
            orderId: 1,
            currentValue: { $multiply: ['$currentQuantity', '$currentPrice'] },
            investedAmount: { $multiply: ['$currentQuantity', '$averagePricePerToken'] },
          },
        },
        // Add profit and total gain/loss percentage
        {
          $addFields: {
            averagePricePerToken: {
              $cond: {
                if: { $eq: ['$currentQuantity', 0] },
                then: 0,
                else: {
                  $divide: ['$totalInvestedAmount', '$currentQuantity'],
                },
              },
            },
            currentPrice: '$currentPrice',
            currentQuantity: '$currentQuantity',
            totalInvestedAmount: '$totalInvestedAmount',
            investedAmount: '$investedAmount',
            currentValue: '$currentValue',
            profit: {
              $let: {
                vars: {
                  computedProfit: { $trunc: [{ $subtract: ['$currentValue', '$investedAmount'] }, 18] },
                },
                in: {
                  $cond: {
                    if: { $eq: [{ $abs: '$$computedProfit' }, 0] },
                    then: 0,
                    else: '$$computedProfit',
                  },
                },
              },
            },
            totalGainLossPercentage: {
              $cond: {
                if: { $or: [{ $eq: ['$investedAmount', 0] }, { $eq: ['$investedAmount', null] }] },
                then: 0,
                else: {
                  $let: {
                    vars: {
                      computedPercentage: {
                        $trunc: [
                          {
                            $multiply: [
                              {
                                $divide: [{ $subtract: ['$currentValue', '$investedAmount'] }, '$investedAmount'],
                              },
                              100,
                            ],
                          },
                          18,
                        ],
                      },
                    },
                    in: {
                      $cond: {
                        if: { $eq: [{ $abs: '$$computedPercentage' }, 0] },
                        then: 0,
                        else: '$$computedPercentage',
                      },
                    },
                  },
                },
              },
            },
          },
        },

        // Convert Decimal128 fields to strings to preserve precision
        {
          $addFields: {
            currentQuantity: { $toString: '$currentQuantity' },
            totalInvestedAmount: { $toString: '$totalInvestedAmount' },
            averagePricePerToken: { $toString: '$averagePricePerToken' },
            currentPrice: { $toString: '$currentPrice' },
            currentValue: { $toString: '$currentValue' },
            investedAmount: { $toString: '$investedAmount' },
            profit: { $toString: '$profit' },
            totalGainLossPercentage: { $toString: '$totalGainLossPercentage' },
          },
        },

        // Sorting
        { $sort: { orderMinted: -1 } },

        // Optional search query
        ...(searchQuery ? [{ $match: { $or: [{ offeringName: { $regex: searchQuery, $options: 'i' } }, { tokenAddress: { $regex: searchQuery, $options: 'i' } }] } }] : []),

        // Pagination and count
        {
          $facet: {
            portfolio: isCsv
              ? [] // If isCsv is true, no pagination, return all items
              : [{ $skip: (page - 1) * limit }, { $limit: limit }], // Otherwise, apply pagination
            totalCount: [{ $count: 'count' }],
          },
        },
      ];

      const result = await OrderSchema.aggregate(portfolioPipeline as any).exec();

      const totalCount = result[0]?.totalCount[0]?.count || 0;
      const portfolio = result[0]?.portfolio || [];

      // Calculate total pages
      const totalPages = Math.ceil(totalCount / limit);

      // Calculate nextPage and previousPage
      const nextPage = page < totalPages ? page + 1 : null;
      const previousPage = page > 1 ? page - 1 : null;

      return { status: RESPONSES.SUCCESS, error: false, message: 'Portfolio found successfully!', data: { portfolio, currentPage: page, totalPages, totalCount, nextPage, previousPage } };
    } catch (error) {
      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * Updates user profile information and account settings with comprehensive validation and notifications.
   * Handles profile updates, sends admin notifications, and triggers real-time user notifications.
   *
   * @async
   * @method updateUserDetails
   * @param {IUpdateUserModel} data - User data to update including profile fields and account settings
   * @param {FilterQuery<IUserModel>} filter - MongoDB filter to identify the specific user document
   * @returns {Promise<PromiseResolve>} Promise resolving to updated user data and operation status
   * @throws {Error} Throws error if user not found or update operation fails
   * @memberof UserService
   *
   * @description
   * Update process includes:
   * 1. Database update with validation
   * 2. Admin notification via Kafka messaging
   * 3. Real-time socket notification to user
   * 4. Audit trail maintenance
   *
   */
  updateUserDetails = async (data: IUpdateUserModel, filter: FilterQuery<IUserModel>): Promise<PromiseResolve> => {
    try {
      const updateUserResp = await userSchema.findOneAndUpdate(filter, data, { new: true, runValidators: true });
      if (updateUserResp) {
        const finalData = JSON.parse(JSON.stringify(data));
        await kafkaService.sendMessageToAdmin({ value: { ...finalData, type: queueMessageTypeEnum.USER, ...filter, _id: updateUserResp._id } });

        // emit onchain
        socketHelper.sendMessageToSingleUser(updateUserResp._id.toString(), 'kyc', updateUserResp);

        return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.USER.USER_UPDATION_SUCCESS, data: updateUserResp };
      }

      return { status: RESPONSES.BAD_REQUEST, error: true, message: RES_MSG.ERROR_MSG.USER_UPDATION_ERROR };
    } catch (error) {
      logger.error(error, 'updateUserDetails error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * Updates user's KYC (Know Your Customer) verification details and compliance status.
   * Manages identity verification data with comprehensive error handling for regulatory compliance.
   *
   * @async
   * @method updateUserKycDetails
   * @param {IUserDetails} data - Complete KYC verification data including identity documents and compliance information
   * @param {FilterQuery<IUserDetails>} filter - MongoDB filter to identify the user's KYC record
   * @returns {Promise<PromiseResolve>} Promise resolving to updated KYC status and verification details
   * @throws {Error} Throws specific errors for duplicate national ID or general update failures
   * @memberof UserService
   *
   * @description
   * KYC update features:
   * - Upsert functionality (creates record if not exists)
   * - National ID uniqueness validation
   * - Comprehensive error handling with specific error messages
   * - Regulatory compliance tracking
   * - Audit trail maintenance
   *
   * Special error handling:
   * - Detects and handles duplicate national ID numbers
   * - Provides specific error messages for compliance violations
   * - Maintains data integrity for regulatory requirements
   *
   */
  updateUserKycDetails = async (data: IUserDetails, filter: FilterQuery<IUserDetails>): Promise<PromiseResolve> => {
    try {
      const updateUserResp = await UserDetailsSchema.findOneAndUpdate(filter, data, { new: true, runValidators: true, upsert: true });
      if (updateUserResp) {
        return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.USER.USER_UPDATION_SUCCESS, data: updateUserResp };
      }

      return { status: RESPONSES.BAD_REQUEST, error: true, message: RES_MSG.ERROR_MSG.USER_UPDATION_ERROR };
    } catch (error) {
      logger.error(error, 'updateUserDetails error');
      if (error.keyPattern?.['mainInformation.nationalIdNumber'] || JSON.stringify(error)?.includes('nationalIdNumber')) {
        return { status: RESPONSES.BAD_REQUEST, error: true, message: RES_MSG.ERROR_MSG.ID_ERR };
      }
      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * Creates a new user account through administrative or system processes.
   * Alternative user creation method with Kafka notification integration.
   *
   * @async
   * @method createUser
   * @param {IUpdateUserModel} body - User account data for creation including profile and settings
   * @returns {Promise<PromiseResolve>} Promise resolving to new user account data and creation status
   * @throws {Error} Throws error if user creation fails or validation errors occur
   * @memberof UserService
   *
   * @description
   * Creation process includes:
   * - User account creation with validation
   * - Admin notification via Kafka messaging
   * - Data serialization and audit trail
   * - Comprehensive error handling
   *
   * Used for:
   * - Administrative user creation
   * - Bulk user imports
   * - System-generated accounts
   * - Integration scenarios
   *
   */
  createUser = async (body: IUpdateUserModel): Promise<PromiseResolve> => {
    try {
      const createQuery: IUserModel = await userSchema.create(body);
      if (createQuery) {
        const finalData = JSON.parse(JSON.stringify(createQuery));
        await kafkaService.sendMessageToAdmin({ value: { ...finalData, type: queueMessageTypeEnum.USER } });

        return { status: RESPONSES.CREATED, error: false, message: RES_MSG.SUCCESS_MSG.REGISTER_SUCCESS, data: createQuery };
      }

      return { status: RESPONSES.BAD_REQUEST, error: true, message: RES_MSG.COMMON.SOMETHING_WRONG };
    } catch (error) {
      logger.error(error, 'signUp error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * Adds a new password to user's password history for security tracking and reuse prevention.
   * Maintains configurable password history length and validates against password reuse policies.
   *
   * @async
   * @method addPassword
   * @param {string} _id - User's unique identifier in the database
   * @param {string} password - Hashed password to add to user's password history
   * @returns {Promise<PromiseResolve>} Promise resolving to password addition confirmation
   * @throws {Error} Throws error if password was recently used or operation fails
   * @memberof UserService
   *
   * @description
   * Password history management:
   * - Prevents password reuse within configured history limit
   * - Maintains FIFO queue of recent passwords
   * - Creates initial password history entry if none exists
   * - Validates password uniqueness within history
   * - Automatically removes oldest passwords when limit exceeded
   *
   * Security features:
   * - Password reuse prevention
   * - Configurable history length (maxPasswordHistory)
   * - Audit trail of password changes
   * - Compliance with security policies
   *
   * if (result.error) {
   *   console.log('Password recently used, choose different password');
   * }
   * ```
   */
  addPassword = async (_id: string, password: string): Promise<PromiseResolve> => {
    try {
      const userPasswords = await passwordSchema.findOne({ _id });
      if (userPasswords) {
        // Check if the new password already exists in the array
        if (userPasswords.passwords.includes(password)) {
          return { status: RESPONSES.BAD_REQUEST, error: true, message: RES_MSG.ERROR_MSG.PASSWORD_RECENTLY_USED };
        }

        userPasswords.passwords.push(password);
        if (userPasswords.passwords.length > maxPasswordHistory) {
          userPasswords.passwords.shift(); // Remove the oldest password (index 0)
        }

        const updatedQuery = await userPasswords.save();

        return { status: RESPONSES.CREATED, error: false, message: RES_MSG.SUCCESS_MSG.REGISTER_SUCCESS, data: updatedQuery };
      }
      // eslint-disable-next-line new-cap
      const newPasswordEntry = new passwordSchema({ _id, passwords: [password] });

      const createQuery = await newPasswordEntry.save();

      return { status: RESPONSES.CREATED, error: false, message: RES_MSG.SUCCESS_MSG.REGISTER_SUCCESS, data: createQuery };
    } catch (error: any) {
      logger.error(error, 'addPassword error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * Retrieves user's recent password history for validation and security policy enforcement.
   * Used for password change validation, security auditing, and compliance requirements.
   *
   * @async
   * @method fetchRecentPasswords
   * @param {string} _id - User's unique identifier to fetch password history for
   * @returns {Promise<PromiseResolve>} Promise resolving to user's password history data
   * @throws {Error} Throws error if user not found or password history retrieval fails
   * @memberof UserService
   *
   * @description
   * Password history retrieval:
   * - Fetches complete password history for user
   * - Used for password reuse validation
   * - Supports security policy enforcement
   * - Enables password strength analysis
   * - Provides audit trail for security reviews
   *
   * Use cases:
   * - Password change validation
   * - Security policy compliance checking
   * - Account security auditing
   * - Password strength analysis
   * - Regulatory compliance reporting
   *
   * if (result.data) {
   *   const passwords = result.data.passwords;
   *   console.log(`User has ${passwords.length} passwords in history`);
   * }
   * ```
   */
  fetchRecentPasswords = async (_id: string): Promise<PromiseResolve> => {
    try {
      const recentPasswords: PasswordModel[] = await passwordSchema.findOne({ _id });

      if (recentPasswords) {
        return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS, data: recentPasswords };
      }

      return { status: RESPONSES.NOTFOUND, error: true, message: RES_MSG.COMMON.NO_USER };
    } catch (error) {
      logger.error(error, 'fetchRecentPasswords error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };
}

/**
 * Singleton instance of the UserService class.
 * Provides global access to user management functionality throughout the application.
 *
 * @exports {UserService} UserService - Configured user service instance
 */
export default new UserService();
