/* eslint-disable no-await-in-loop */
/* eslint-disable no-restricted-syntax */
import { Request, Response } from 'express';
import * as bcrypt from 'bcrypt';
import { authenticator } from 'otplib';
import { toDataURL } from 'qrcode';
import { Types } from 'mongoose';
import { KycStatusEnum, PromiseResolve, IssuerStatusEnum, DocumentTypesEnum, otpMethodsEnum, queueMessageTypeEnum, UserTypeEnum, DocumentFolderTypesEnum, otpTypeEnum } from '../../utils/common.interface';
import UserService from './service';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import CustomError from '../../helpers/customError.helper';
import RedisHelper from '../../helpers/redis.helper';
import { ResponseHandler } from '../../helpers/response.helper';
import CommonHelper from '../../helpers/common.helper';
import CONFIG from '../../config/env';
import logger from '../../helpers/logging/logger.helper';
import CloudHelper from '../../helpers/cloud.helper';
import kafkaService from '../../service/kafkaService';
import sumsubHelper from '../../helpers/sumsub.helper';
import emailHelper from '../../helpers/email.helper';
import { maxKycAttempt } from '../../utils/constant';
import OfferingService from '../offerings/service';
import adminClient from '../../_grpc/clients/admin.client';
import { IUpdateUserModel, IUserModel } from './models/user.model';
import { socketHelper } from '../../helpers/socket.helper';
// import { socketHelper } from '../../helpers/socket.helper';
import { service as representativeService } from '../representative/service';

const { LOGIN_MAX_ATTEMPT, LOGIN_BLOCK_TIME } = CONFIG.REDIS;
const baseurl = process.env.BASEURL;

/**
 * UserAuthenticationController handles user authentication-related operations.
 */
class UserAuthenticationController {
  /**
   * Handles the user sign-up process.
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise<PromiseResolve>}
   */
  public signUp = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { email, mobile, otpMethods } = req.body;

      const { error: emailError, message: emailMessage, status: emailStatus } = await CommonHelper.isValidEmail(email);
      if (emailError) {
        throw new CustomError(emailMessage || RES_MSG?.ERROR_MSG.INVALID_EMAIL, emailStatus || RESPONSES.BAD_REQUEST);
      }

      const { error: userDetailsError } = await UserService.fetchUserDetails({ email }, ['email', 'mobile']);
      if (!userDetailsError) {
        throw new CustomError(RES_MSG.USER.USER_ALREADY_EXIST, RESPONSES.CONFLICT);
      }

      const { error: mobileError } = await UserService.fetchUserDetails({ mobile }, ['email', 'mobile']);
      if (!mobileError) {
        throw new CustomError(RES_MSG.ERROR_MSG.MOBILE_NO_EXIST, RESPONSES.CONFLICT);
      }
      const { error: isUserExist } = await this.getExistingUser(email);
      if (isUserExist) {
        throw new CustomError(RES_MSG.USER.USER_ALREADY_EXIST, RESPONSES.CONFLICT);
      }
      const { error: createUserError, message: createUserMessage, status: createUserStatus, data: user } = await UserService.signUp(req.body);
      if (createUserError) {
        throw new CustomError(createUserMessage, createUserStatus);
      }
      // update user details for representative
      if (user && user.email) {
        const { data } = await representativeService.getRepresentative(1, 10, false, { search: user.email });
        if (!!data && Object.keys(data).length > 0) {
          const invitedBy = data?.representatives?.reduce((acc: any, item: { issuer_id: string }) => {
            if (item?.issuer_id) acc.push(item.issuer_id.toString());
            return acc;
          }, []);

          if (invitedBy?.length) {
            await UserService.updateUserDetails({ isRepresentatives: true, invitedBy }, { _id: user._id });
          }
        }
      }

      await kafkaService.sendMessageToAdmin({ value: JSON.stringify({ ...user, type: queueMessageTypeEnum.USER }) });

      const { error: otpError, message: otpMessage, status: otpStatus } = await CommonHelper.sendOTP(user, otpTypeEnum.SIGN_UP, otpMethods);
      if (otpError) {
        throw new CustomError(otpMessage, otpStatus);
      }

      return ResponseHandler.success(res, {
        message: createUserMessage || RES_MSG?.SUCCESS_MSG.REGISTER_SUCCESS,
        status: createUserStatus || RESPONSES.CREATED,
        error: createUserError || false,
        data: { isOtpActive: true },
      });
    } catch (error) {
      logger.error(error, 'signUp Error');

      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };

  /**
   * Handles the user login process.
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise<PromiseResolve>}
   */
  public login = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      let tokenResp;
      let otpMethods;
      const { userName, password, platform } = req.body;

      const userDetails: PromiseResolve = await UserService.fetchUserDetails({ userName });
      const user: IUserModel = userDetails?.data;
      if (userDetails.error || !user.password) {
        throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.UN_AUTHORIZED);
      }

      // Platform-specific validation
      // if (platform?.toLowerCase() === 'investor') {
      //   // For investor platform, only allow investor and institution users
      //   if (user.issuerStatus === IssuerStatusEnum.APPROVED && user?.isIssuer) {
      //     throw new CustomError(RES_MSG.ERROR_MSG.INVESTOR_PLATFORM_ACCESS_DENIED, RESPONSES.FORBIDDEN);
      //   }
      // }
      if (user?.isRepresentatives && user?.kycStatus !== KycStatusEnum.APPROVED) {
        throw new CustomError(RES_MSG.ERROR_MSG.REPRESENTATIVE_KYC_PENDING, RESPONSES.FORBIDDEN);
      }
      if (platform?.toLowerCase() === 'issuer' && !user?.isRepresentatives) {
        // For issuer platform, user must be an approved issuer
        if (user.issuerStatus === IssuerStatusEnum.NOT_APPLIED) {
          throw new CustomError(RES_MSG.ERROR_MSG.ISSUER_NOT_APPLIED, RESPONSES.FORBIDDEN);
        }
        if (user.issuerStatus !== IssuerStatusEnum.APPROVED) {
          throw new CustomError(RES_MSG.ERROR_MSG.ISSUER_NOT_APPROVED, RESPONSES.FORBIDDEN);
        }
      }
      if (user.email.toLowerCase() === userName.toLowerCase()) {
        otpMethods = otpMethodsEnum.EMAIL;
      }
      if (user.mobile === userName) {
        otpMethods = otpMethodsEnum.MOBILE;
      }

      const verifyPassResp: PromiseResolve = await CommonHelper.verifyPassword(password, user.password);
      if (verifyPassResp.error) {
        const userLockResp: PromiseResolve = await CommonHelper.userLock(userName);
        if (userLockResp.error) throw new CustomError(userLockResp.message, userLockResp.status);
        if (userLockResp.data.totalAttemptsCounts >= LOGIN_MAX_ATTEMPT) {
          emailHelper.sendEmailTemplate(user.email, 'multipleLogin', user);
        }
        if (userLockResp.error) throw new CustomError(userLockResp.message, userLockResp.status);
        // Include the count (userLockResp.data) in the error message
        const lockedTimes = await CommonHelper.convertSecondsToHMS(LOGIN_BLOCK_TIME);
        const errorMessage = `Invalid credentials. Please check and try again. you have ${LOGIN_MAX_ATTEMPT - (1 + +userLockResp.data.totalAttemptsCounts)} more attempt before your account is blocked for ${userLockResp.data.lockedTimes ? userLockResp.data.lockedTimes : lockedTimes}`;
        throw new CustomError(errorMessage, verifyPassResp.status);
      }
      // check user is blocked by admin
      if (!user.isActive) {
        throw new CustomError(RES_MSG.COMMON.ADMIN_BLOCK_USER, RESPONSES.FORBIDDEN);
      } else if (!user.isEmailVerify && otpMethods === otpMethodsEnum.EMAIL) {
        // check user email is verify

        const otpResponse = await CommonHelper.sendOTP(user, user.isEmailVerify ? otpTypeEnum.LOGIN : otpTypeEnum.SIGN_UP, otpMethods);
        if (otpResponse.error) throw new CustomError(otpResponse.message, otpResponse.status);

        return ResponseHandler.success(res, {
          status: userDetails.status || RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.USER.USER_NOT_VERIFIED.replace('{methods}', otpMethods),
          data: {
            isOtpActive: user.isOtpActive,
            isEmailVerify: user.isEmailVerify,
            otpMethods,
          },
        });
      } else if (!user.isMobileVerify && otpMethods === otpMethodsEnum.MOBILE) {
        const otpResponse = await CommonHelper.sendOTP(user, user.isMobileVerify ? otpTypeEnum.LOGIN : otpTypeEnum.SIGN_UP, otpMethods);
        if (otpResponse.error) throw new CustomError(otpResponse.message, otpResponse.status);

        return ResponseHandler.success(res, {
          status: userDetails.status || RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.USER.USER_NOT_VERIFIED.replace('{methods}', otpMethods),
          data: {
            isOtpActive: user.isOtpActive,
            isMobileVerify: user.isMobileVerify,
            otpMethods,
          },
        });
      } else if (user.isOtpActive) {
        // check login otp is active
        const otpResponse = await CommonHelper.sendOTP(user, otpTypeEnum.LOGIN, otpMethods);
        if (otpResponse.error) throw new CustomError(otpResponse.message, otpResponse.status);

        return ResponseHandler.success(res, {
          status: userDetails.status || RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.SUCCESS_MSG.OTP_SENT_SUCCESS,
          data: {
            isOtpActive: user.isOtpActive,
            otpMethods,
          },
        });
      } else if (user.is2FAActive) {
        // token for twofa verification
        tokenResp = await CommonHelper.createJWTAuth({ userId: user._id, email: user.email }, false, 'twoFA', CONFIG.JWT_AUTH.JWT_2FA_EXPIRE);
        if (tokenResp.error) throw new CustomError(tokenResp.message, tokenResp.status);
        await CommonHelper.userUnLock(user.email);

        return ResponseHandler.success(res, {
          status: userDetails.status || RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.SUCCESS_MSG.OTP_SENT_SUCCESS,
          data: {
            kycStatus: user.kycStatus,
            userType: user.userType,
            is2FAActive: user.is2FAActive,
            isKyc: user.isKyc,
            isIssuer: user.isIssuer,
            issuerStatus: user.issuerStatus,
            ...tokenResp.data,
          },
        });
      }

      // default login if otp and twofa is deactive
      tokenResp = await CommonHelper.createJWTAuth({ userId: user._id, email: user.email, userType: user.userType }, false, 'accessToken', CONFIG.JWT_AUTH.AUTH_EXPIRE_TIME);
      if (tokenResp.error) throw new CustomError(tokenResp.message, tokenResp.status);
      await CommonHelper.userUnLock(user.email);

      return ResponseHandler.success(res, {
        status: userDetails.status || RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.LOGIN_SUCCESS,
        data: {
          ...tokenResp.data,
          kycStatus: user.kycStatus,
          userType: user.userType,
          isKyc: user.isKyc,
          isIssuer: user.isIssuer,
          issuerStatus: user.issuerStatus,
          // isOtpActive: user.isOtpActive,
          is2FAActive: user.is2FAActive,
        },
      });
    } catch (error: any) {
      logger.error(error, 'loginController Error');

      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Handles the social Login process, specifically for Google login.
   * @param {Request} req - The request object containing the login details (social token and user type).
   * @param {Response} res - The response object to send the result.
   * @returns {Promise<PromiseResolve>} - A promise that resolves with the login result.
   */
  public socialLogin = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const provider = 'google';
      const { token, userType, platform } = req.body;
      const socialUserResp: PromiseResolve = await CommonHelper.verifySocialToken(provider, token);
      if (socialUserResp.error) throw new CustomError(socialUserResp.message, socialUserResp.status);
      const socialUser = socialUserResp.data;
      let userDetails: PromiseResolve = await UserService.fetchUserDetails({ email: socialUser.email });
      if (!userDetails.error && !userDetails.data.isActive) {
        throw new CustomError(RES_MSG.COMMON.ADMIN_BLOCK_USER, RESPONSES.FORBIDDEN);
      }
      if (userDetails.error) {
        userDetails = await UserService.createUser({
          email: socialUser.email,
          name: socialUser.name || '',
          isSocialMedia: true,
          isEmailVerify: true,
          isOtpActive: false,
          userType,
        });
        if (userDetails.error) throw new CustomError(userDetails.message, userDetails.status);
      }

      const user = userDetails.data;

      // Platform-specific validation for social login
      if (platform === 'investor') {
        // For investor platform, only allow investor and institution users
        if (user.issuerStatus === IssuerStatusEnum.APPROVED && user?.isIssuer) {
          throw new CustomError(RES_MSG.ERROR_MSG.INVESTOR_PLATFORM_ACCESS_DENIED, RESPONSES.FORBIDDEN);
        }
      } else if (platform === 'issuer') {
        // For issuer platform, user must be an approved issuer
        if (user.issuerStatus === IssuerStatusEnum.NOT_APPLIED) {
          throw new CustomError(RES_MSG.ERROR_MSG.ISSUER_NOT_APPLIED, RESPONSES.FORBIDDEN);
        }
        if (user.issuerStatus !== IssuerStatusEnum.APPROVED) {
          throw new CustomError(RES_MSG.ERROR_MSG.ISSUER_NOT_APPROVED, RESPONSES.FORBIDDEN);
        }
      }
      const userLockResp: PromiseResolve = await CommonHelper.userLock(user.email);
      if (userLockResp.error) throw new CustomError(userLockResp.message, userLockResp.status);
      const tokenResp = await CommonHelper.createJWTAuth({ userId: user._id, email: user.email, userType: user.userType }, false, 'accessToken', CONFIG.JWT_AUTH.AUTH_EXPIRE_TIME);
      if (tokenResp.error) throw new CustomError(tokenResp.message, tokenResp.status);

      return ResponseHandler.success(res, {
        status: userDetails.status || RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.LOGIN_SUCCESS,
        data: {
          ...tokenResp.data,
          kycStatus: user.kycStatus,
          userType: user.userType,
          is2FAActive: user.is2FAActive,
          isKyc: user.isKyc,
          isIssuer: user.isIssuer,
          issuerStatus: user.issuerStatus,
        },
      });
    } catch (error) {
      logger.error(error, 'socialLoginController Error');

      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Handles the verification process, including OTP validation and user actions (e.g., login, signup, forgot password, etc.).
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise<PromiseResolve>}
   */
  public verify = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { userName, otp, otpMethods } = req.body;
      let { type } = req.body;
      let tokenResp;
      let updateUserResp;
      const userDetails: PromiseResolve = await UserService.fetchUserDetails({ userName });
      if (userDetails.error) {
        throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.UN_AUTHORIZED);
      }
      let user = userDetails.data;
      const { email } = user;
      const isUserLockResp: PromiseResolve = await CommonHelper.isUserLocked(email);
      if (isUserLockResp.error) throw new CustomError(isUserLockResp.message, isUserLockResp.status);
      if (!user.isActive) {
        throw new CustomError(RES_MSG.COMMON.ADMIN_BLOCK_USER, RESPONSES.FORBIDDEN);
      }
      if (!userDetails.data.isMobileVerify && type === otpTypeEnum.LOGIN && otpMethods === otpMethodsEnum.MOBILE) {
        type = otpTypeEnum.SIGN_UP;
      }
      if (!userDetails.data.isEmailVerify && type === otpTypeEnum.LOGIN && otpMethods === otpMethodsEnum.EMAIL) {
        type = otpTypeEnum.SIGN_UP;
      }

      const storedOTP: PromiseResolve = await CommonHelper.getOTP(user._id, type, otpMethods);
      if (storedOTP.error) {
        throw new CustomError(storedOTP.message || RES_MSG.ERROR_MSG.INVALID_OTP, storedOTP.status || RESPONSES.BAD_REQUEST);
      } else if (storedOTP.data.otp === null || storedOTP.data.otp === '') {
        throw new CustomError(RES_MSG.ERROR_MSG.EXPIRE_OTP, RESPONSES.BAD_REQUEST);
      } else if (Number(storedOTP.data.otp) !== Number(otp)) {
        const userLockResp: PromiseResolve = await CommonHelper.userLock(email);
        if (userLockResp.error) throw new CustomError(userLockResp.message, userLockResp.status);
        const lockedTimes = await CommonHelper.convertSecondsToHMS(LOGIN_BLOCK_TIME);
        const errorMessage = `${RES_MSG.ERROR_MSG.INVALID_OTP} You have ${LOGIN_MAX_ATTEMPT - (1 + +userLockResp.data.totalAttemptsCounts)} more attempts before your account is locked for ${userLockResp.data.lockedTimes ? userLockResp.data.lockedTimes : lockedTimes}.`;
        throw new CustomError(errorMessage, type === otpTypeEnum.VERIFICATION_OTP ? RESPONSES.BAD_REQUEST : RESPONSES.UN_AUTHORIZED);
      }
      switch (type) {
        // if verify otp during singup
        case otpTypeEnum.SIGN_UP:
          tokenResp = await CommonHelper.createJWTAuth({ userId: user._id, email: user.email, userType: user.userType }, false, 'accessToken', CONFIG.JWT_AUTH.AUTH_EXPIRE_TIME);
          if (tokenResp.error) throw new CustomError(tokenResp.message, tokenResp.status);
          if (!user.isEmailVerify && otpMethods === otpMethodsEnum.EMAIL) {
            updateUserResp = await UserService.updateUserDetails({ isEmailVerify: true }, { email });
            if (updateUserResp.error) {
              throw new CustomError(updateUserResp.message, updateUserResp.status);
            }
            user = {
              ...user,
              isActive: true,
              isEmailVerify: true,
              isMobileVerify: user.isMobileVerify,
            };
          }
          if (!user.isMobileVerify && otpMethods === otpMethodsEnum.MOBILE) {
            updateUserResp = await UserService.updateUserDetails({ isMobileVerify: true }, { email });
            if (updateUserResp.error) {
              throw new CustomError(updateUserResp.message, updateUserResp.status);
            }
            user = {
              ...user,
              isActive: true,
              isMobileVerify: true,
              isEmailVerify: user.isEmailVerify,
            };
          }

          await RedisHelper.deleteKey(storedOTP.data.key);

          await emailHelper.sendEmailTemplate(email, 'signup', {
            name: user.name,
            login: process.env.LOGIN_URL,
            baseurl,
          });

          return ResponseHandler.success(res, {
            status: RESPONSES.SUCCESS,
            error: false,
            message: RES_MSG.USER.LOGIN_SUCCESS,
            data: {
              ...tokenResp.data,
              kycStatus: user.kycStatus,
              userType: user.userType,
              is2FAActive: false,
              isKyc: user.isKyc,
              isIssuer: user.isIssuer,
              issuerStatus: user.issuerStatus,
              isRepresentatives: user?.isRepresentatives || false,
            },
          });
        case otpTypeEnum.LOGIN: {
          if (user.is2FAActive) {
            tokenResp = await CommonHelper.createJWTAuth({ userId: user._id, email: user.email }, false, 'twoFA', CONFIG.JWT_AUTH.JWT_2FA_EXPIRE);
            if (tokenResp.error) throw new CustomError(tokenResp.message, tokenResp.status);
            await RedisHelper.deleteKey(storedOTP.data.key);

            return ResponseHandler.success(res, {
              status: userDetails.status || RESPONSES.SUCCESS,
              error: false,
              message: RES_MSG.TWO_FA.PENDING,
              data: {
                is2FAActive: user.is2FAActive,
                isKyc: user.isKyc,
                isIssuer: user.isIssuer,
                issuerStatus: user.issuerStatus,
                ...tokenResp.data,
                isRepresentatives: user?.isRepresentatives || false,
              },
            });
          }

          tokenResp = await CommonHelper.createJWTAuth({ userId: user._id, email: user.email, userType: user.userType }, false, 'accessToken', CONFIG.JWT_AUTH.AUTH_EXPIRE_TIME);
          if (tokenResp.error) throw new CustomError(tokenResp.message, tokenResp.status);
          await RedisHelper.deleteKey(storedOTP.data.key);
          const emailDetail = { name: user.name };
          emailHelper.sendEmailTemplate(email, 'login', emailDetail);

          return ResponseHandler.success(res, {
            status: userDetails.status || RESPONSES.SUCCESS,
            error: false,
            message: RES_MSG.USER.LOGIN_SUCCESS,
            data: {
              ...tokenResp.data,
              kycStatus: user.kycStatus,
              userType: user.userType,
              isKyc: user.isKyc,
              isIssuer: user.isIssuer,
              issuerStatus: user.issuerStatus,
              // isOtpActive: user.isOtpActive,
              is2FAActive: user.is2FAActive,
              isRepresentatives: user?.isRepresentatives || false,
            },
          });
        }
        case otpTypeEnum.FORGOT:
          tokenResp = await CommonHelper.createJWTAuth({ userId: user._id, email: user.email }, false, 'forgetToken', CONFIG.JWT_AUTH.FORGOT_EXPIRE_TIME);
          if (tokenResp.error) throw new CustomError(tokenResp.message, tokenResp.status);
          await RedisHelper.deleteKey(storedOTP.data.key);

          return ResponseHandler.success(res, {
            status: userDetails.status || RESPONSES.SUCCESS,
            error: false,
            message: RES_MSG.USER.OTP_SUCCESS,
            data: { token: tokenResp.data.accessToken },
          });
        case otpTypeEnum.DISABLE_OTP:
          updateUserResp = await UserService.updateUserDetails({ isOtpActive: false }, { email });
          if (updateUserResp.error) {
            throw new CustomError(updateUserResp.message, updateUserResp.status);
          }

          return ResponseHandler.success(res, {
            status: userDetails.status || RESPONSES.SUCCESS,
            error: false,
            message: RES_MSG.USER.OTP_DISABLE_SUCCESS,
          });
        case otpTypeEnum.DISABLE_2FA:
          updateUserResp = await UserService.updateUserDetails({ is2FAActive: false, twoFASecret: '' }, { email });
          if (updateUserResp.error) {
            throw new CustomError(updateUserResp.message, updateUserResp.status);
          }

          return ResponseHandler.success(res, {
            status: userDetails.status || RESPONSES.SUCCESS,
            error: false,
            message: RES_MSG.USER.TFA_DISABLE_SUCCESS,
          });
        case otpTypeEnum.VERIFICATION_OTP:
          if (!user.isEmailVerify && otpMethods === otpMethodsEnum.EMAIL) {
            updateUserResp = await UserService.updateUserDetails({ isEmailVerify: true }, { email });
            if (updateUserResp.error) {
              throw new CustomError(updateUserResp.message, updateUserResp.status);
            }
            user = { isEmailVerify: true, isMobileVerify: user.isMobileVerify };
          }
          if (!user.isMobileVerify && otpMethods === otpMethodsEnum.MOBILE) {
            updateUserResp = await UserService.updateUserDetails({ isMobileVerify: true }, { email });
            if (updateUserResp.error) {
              throw new CustomError(updateUserResp.message, updateUserResp.status);
            }
            user = { isEmailVerify: user.isEmailVerify, isMobileVerify: true };
          }

          return ResponseHandler.success(res, {
            status: userDetails.status || RESPONSES.SUCCESS,
            error: false,
            message: RES_MSG.USER.VERIFICATION_SUCCESS,
            data: { ...user },
          });
        default:
          throw new CustomError(RES_MSG.COMMON.BAD_REQUEST, RESPONSES.BAD_REQUEST);
      }
    } catch (error) {
      logger.error(error, 'verifyController Error');

      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Handles the resend otp process.
   * @param {Request} req - The request object containing the user input.
   * @param {Response} res - The response object to send back the result.
   * @returns {Promise<PromiseResolve>} - A promise that resolves with the response.
   */
  public reSendOtp = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { userName, otpMethods } = req.body;
      let { type } = req.body;
      const isUserLockResp: PromiseResolve = await CommonHelper.isUserLocked(userName);
      if (isUserLockResp.error) throw new CustomError(isUserLockResp.message, isUserLockResp.status);
      const userDetails: PromiseResolve = await UserService.fetchUserDetails({ userName });
      if (userDetails.error) {
        throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.UN_AUTHORIZED);
      } else if (userDetails.data.isEmailVerify && type === otpTypeEnum.SIGN_UP && otpMethods === otpMethodsEnum.EMAIL) {
        throw new CustomError(RES_MSG.ERROR_MSG.ALREADY_EMAIL_VERIFIED, RESPONSES.UN_AUTHORIZED);
      } else if (userDetails.data.isMobileVerify && type === otpTypeEnum.SIGN_UP && otpMethods === otpMethodsEnum.MOBILE) {
        throw new CustomError(RES_MSG.ERROR_MSG.ALREADY_EMAIL_VERIFIED, RESPONSES.UN_AUTHORIZED);
      } else if ((!userDetails.data.isMobileVerify && type === otpTypeEnum.LOGIN && otpMethods === otpMethodsEnum.MOBILE) || (!userDetails.data.isEmailVerify && type === otpTypeEnum.LOGIN && otpMethods === otpMethodsEnum.EMAIL)) {
        type = otpTypeEnum.SIGN_UP;
      }
      const otpResponse = await CommonHelper.sendOTP(userDetails.data, type, otpMethods);
      if (otpResponse.error) throw new CustomError(otpResponse.message, otpResponse.status);
      const userLockResp: PromiseResolve = await CommonHelper.userLock(userName);
      if (userLockResp.error) throw new CustomError(userLockResp.message, userLockResp.status);
      const maskEmail = CommonHelper.maskSensitiveInfo(userDetails?.data?.email, 'email');
      const maskMobile = CommonHelper.maskSensitiveInfo(userDetails?.data?.mobile, 'mobile');
      return ResponseHandler.success(res, {
        status: userDetails.status || RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.OTP_SENT_SUCCESS,
        data: { email: maskEmail, mobile: maskMobile },
      });
    } catch (error) {
      logger.error(error, 'reSendOtpController Error');

      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Get user profile information.
   * @param {Request} req - The request object containing the user's information (email, KYC status).
   * @param {Response} res - The response object to send back the result.
   * @returns {Promise<PromiseResolve>} - A promise that resolves with the response.
   */
  public getUserProfile = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { kyc } = req.body;
      const { email: userEmail } = req.userInfo;
      const searchQuery = { email: userEmail };
      const { error, message, status = RESPONSES.SUCCESS, data: user } = await UserService.fetchUserDetails(searchQuery, [], [], kyc);
      if (error) throw new CustomError(message, status);

      const {
        _id,
        name,
        legalFullName,
        mobile,
        countryCode,
        email,
        isEmailVerify,
        isMobileVerify,
        is2FAActive,
        isKyc,
        kycStatus,
        sumSubKycStatus,
        userImage,
        dob,
        userType,
        kycDetails,
        isIssuer,
        issuerStatus,
        isActive,
        createdAt,
        kycCount,
        isRepresentatives,
        multisigAddress,
      } = user;

      const profileUser = {
        _id,
        name,
        legalFullName,
        mobile: `${countryCode}${mobile}`,
        countryCode,
        email,
        isEmailVerify,
        isMobileVerify,
        is2FAActive,
        isKyc,
        kycStatus,
        sumSubKycStatus,
        userImage,
        dob,
        userType,
        kycDetails,
        isIssuer,
        issuerStatus,
        isActive,
        createdAt,
        kycCount,
        isRepresentatives,
        multisigAddress,
      };

      return ResponseHandler.success(res, {
        status,
        error: false,
        message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
        data: profileUser,
      });
    } catch (error: any) {
      logger.error(error, 'getUserProfileController Error');

      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };

  /**
   * Get user portfolio information.
   * @param {Request} req - The request object containing query parameters (page, limit, searchQuery).
   * @param {Response} res - The response object to send back the result.
   * @returns {Promise<PromiseResolve>} - A promise that resolves with the response.
   */
  public getUserPortfolio = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { page, limit, searchQuery, isCsv } = req.query;
      const { error, data, message, status } = await UserService.fetchUserPortfolio(req.userInfo.userId, isCsv, {
        page: Number(page),
        limit: Number(limit),
        search: String(searchQuery),
      });
      if (error) {
        throw new CustomError(message, status);
      }

      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
        data,
      });
    } catch (error: any) {
      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };

  /**
   * Forgot password controller.
   * @param {Request} req - The request object containing the email.
   * @param {Response} res - The response object to send back the result.
   * @returns {Promise<PromiseResolve>} - A promise that resolves with the response.
   */
  public forgotPassword = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { email } = req.body;
      const isUserLockResp: PromiseResolve = await CommonHelper.isUserLocked(email);
      if (isUserLockResp.error) throw new CustomError(isUserLockResp.message, isUserLockResp.status);

      const userDetails: PromiseResolve = await UserService.fetchUserDetails({ email });
      if (userDetails.error) {
        return ResponseHandler.success(res, {
          status: RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.ERROR_MSG.FORGOT_CRED,
        });
      }

      const otpResponse = await CommonHelper.sendOTP(userDetails.data, otpTypeEnum.FORGOT, otpMethodsEnum.EMAIL);
      if (otpResponse.error) throw new CustomError(otpResponse.message, otpResponse.status);

      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.ERROR_MSG.FORGOT_CRED,
      });
    } catch (error: any) {
      logger.error(error, 'forgotPasswordController error');

      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Disables the OTP for the user.
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise<PromiseResolve>}
   */
  public disableOTP = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { email } = req.userInfo;
      const userDetails: PromiseResolve = await UserService.fetchUserDetails({ email });
      const otpResponse = await CommonHelper.sendOTP(userDetails.data, otpTypeEnum.DISABLE_OTP, otpMethodsEnum.EMAIL);
      if (otpResponse.error) throw new CustomError(otpResponse.message, otpResponse.status);

      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.OTP_SUCCESS,
      });
    } catch (error: any) {
      logger.error(error, 'Error in disableOTPController');

      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Handles the otp verification process.
   *
   * Case 1: Send OTP to mobile if email is verified but mobile is not verified
   * Case 2: Send OTP to email if mobile is verified but email is not verified
   * Case 3: If both email and mobile are verified, return an unauthorized error
   * Case 4: If neither is verified, default send on email
   *
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise<PromiseResolve>}
   */
  public verification = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { email } = req.userInfo;

      const userDetails: PromiseResolve = await UserService.fetchUserDetails({ email });
      const { isEmailVerify, isMobileVerify } = userDetails.data;

      let otpResponse;

      // Case 1: Send OTP to mobile if email is verified but mobile is not verified
      if (isEmailVerify && !isMobileVerify) {
        otpResponse = await CommonHelper.sendOTP(userDetails.data, otpTypeEnum.VERIFICATION_OTP, otpMethodsEnum.MOBILE);

        // Case 2: Send OTP to email if mobile is verified but email is not verified
      } else if (isMobileVerify && !isEmailVerify) {
        otpResponse = await CommonHelper.sendOTP(userDetails.data, otpTypeEnum.VERIFICATION_OTP, otpMethodsEnum.EMAIL);

        // Case 3: If both email and mobile are verified, return an unauthorized error
      } else if (isEmailVerify && isMobileVerify) {
        throw new CustomError('Unauthorized access for sending OTP', RESPONSES.FORBIDDEN);

        // Case 4: If neither is verified, default send on email
      } else {
        otpResponse = await CommonHelper.sendOTP(userDetails.data, otpTypeEnum.VERIFICATION_OTP, otpMethodsEnum.EMAIL);
      }

      // If OTP sending failed, handle the error
      if (otpResponse.error) throw new CustomError(otpResponse.message, otpResponse.status);

      // OTP sent successfully
      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.OTP_SENT_SUCCESS,
      });
    } catch (error: any) {
      logger.error(error, 'Error in verificationController');

      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Handles the change Password process.
   *
   * The following process is performed:
   * 1. Fetch user details and recent passwords concurrently.
   * 2. Validate user existence.
   * 3. Validate the old password.
   * 4. Check if the new password matches any recent passwords.
   * 5. Update user password.
   * 6. Add new password to password history.
   * 7. Delete old access token from Redis.
   * 8. Respond with success.
   *
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise<PromiseResolve>}
   */
  public changePassword = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { email } = req.userInfo;
      const { password, oldPassword } = req.body;

      // Fetch user details and recent passwords concurrently
      const [userDetails, recentPasswordsResp] = await Promise.all([UserService.fetchUserDetails({ email }, ['email', '_id', 'password'], []), UserService.fetchRecentPasswords(req.userInfo.userId)]);

      // Validate user existence
      if (userDetails.error) {
        throw new CustomError(RES_MSG.COMMON.NO_USER, RESPONSES.UN_AUTHORIZED);
      }

      // Validate old password
      const verifyPassResp: PromiseResolve = await CommonHelper.verifyPassword(oldPassword, userDetails.data.password);
      if (verifyPassResp.error) {
        throw new CustomError(RES_MSG.ERROR_MSG.INCORRECT_CURRENT_PASSWORD, RESPONSES.BAD_REQUEST);
      }

      // Check if the new password matches any recent passwords
      if (!recentPasswordsResp.error) {
        for (const recentPassword of recentPasswordsResp.data.passwords) {
          // eslint-disable-next-line no-await-in-loop
          const isMatch = await bcrypt.compare(password, recentPassword);
          if (isMatch) {
            throw new CustomError(RES_MSG.ERROR_MSG.PASSWORD_RECENTLY_USED, RESPONSES.BAD_REQUEST);
          }
        }
      }

      // Update user password
      const updateUserResp: PromiseResolve = await UserService.updateUserDetails({ password }, { email });
      if (!updateUserResp.error) {
        // Add new password to password history
        await UserService.addPassword(userDetails.data._id, updateUserResp.data.password);

        // Delete old access token from Redis
        await RedisHelper.deleteKey(`accessToken:${email}`);

        // Respond with success
        return ResponseHandler.success(res, {
          status: RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.SUCCESS_MSG.PASSWORD_RESET_SUCCESS,
        });
      }
    } catch (error) {
      logger.error(error, 'changePasswordController Error');

      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Handles the resetPassword process.
   *
   * This function takes the request body object and returns a PromiseResolve.
   *
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise<PromiseResolve>}
   */
  public resetPassword = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { token, newPassword } = req.body;
      const isTokenValid: PromiseResolve = await CommonHelper.isValidToken(token);
      if (isTokenValid.error) {
        throw new CustomError(RES_MSG.TWO_FA.TOKEN_INVALID, RESPONSES.UN_AUTHORIZED);
      }
      const { email } = isTokenValid.data;
      const forgetToken: string | null = await RedisHelper.getString(`forgetToken:${email}`);
      if (!forgetToken) {
        throw new CustomError(RES_MSG.TWO_FA.TOKEN_INVALID, RESPONSES.UN_AUTHORIZED);
      }

      if (forgetToken !== token) {
        throw new CustomError(RES_MSG.TWO_FA.TOKEN_INVALID, RESPONSES.UN_AUTHORIZED);
      }
      const userDetails: PromiseResolve = await UserService.fetchUserDetails({ email }, ['email', '_id'], []);
      if (userDetails.error) throw new CustomError(RES_MSG.COMMON.NO_USER, RESPONSES.UN_AUTHORIZED);
      const recentPasswordsResp: PromiseResolve = await UserService.fetchRecentPasswords(userDetails.data._id);
      if (!recentPasswordsResp.error) {
        for (const recentPassword of recentPasswordsResp.data.passwords) {
          const isMatch = await bcrypt.compare(newPassword, recentPassword);
          if (isMatch) {
            throw new CustomError(RES_MSG.ERROR_MSG.PASSWORD_RECENTLY_USED, RESPONSES.BAD_REQUEST);
          }
        }
      }
      const updateUserResp = await UserService.updateUserDetails({ password: newPassword }, { email });
      if (updateUserResp.error) throw new CustomError(updateUserResp.message, updateUserResp.status);
      await RedisHelper.deleteKey(`forgetToken:${email}`);

      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.PASSWORD_RESET_SUCCESS,
      });
    } catch (error: any) {
      logger.error(error, 'Error in resetPassword');

      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Handles the updateProfile process.
   *
   * This function takes the request body object and returns a PromiseResolve.
   *
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise<PromiseResolve>}
   */
  public updateProfile = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { file } = req;
      let userImage: string;
      let otpResponse: PromiseResolve;
      const { userId } = req.userInfo;
      const { name, dob, countryCode, mobile, email, multisigAddress } = req.body;
      let formattedDob;
      if (dob) {
        const formattedDate = await CommonHelper.formatDates(dob);
        formattedDob = formattedDate.toString();
      }
      const userDetails: PromiseResolve = await UserService.fetchUserDetails({ _id: userId });
      if (userDetails.error) throw new CustomError(userDetails.message, userDetails.status);
      const { isEmailVerify, isMobileVerify, issuerStatus } = userDetails.data;
      if (file) {
        const uploadFileRes: PromiseResolve = await CloudHelper.uploadFiles(userId, file, DocumentTypesEnum.PROFILE, DocumentFolderTypesEnum.USER);
        if (uploadFileRes.error) {
          throw new CustomError(uploadFileRes.message || RES_MSG.COMMON.BAD_REQUEST, uploadFileRes.status || RESPONSES.BAD_GATEWAY);
        }

        userImage = uploadFileRes?.data?.url;
      }
      let multisigAddressToUpdate;
      if (multisigAddress) {
        if (!!issuerStatus && issuerStatus.toUpperCase() === IssuerStatusEnum.APPROVED) {
          multisigAddressToUpdate = multisigAddress;
        } else {
          throw new CustomError(RES_MSG.COMMON.ISSUER_STATUS_NOT_APPROVED, RESPONSES.CONFLICT);
        }
      }
      let updateProfile: IUpdateUserModel = {
        name,
        userImage,
        dob: formattedDob,
        multisigAddress: multisigAddressToUpdate,
      };
      if (!isEmailVerify && email) {
        const isEmailExist: PromiseResolve = await UserService.fetchUserDetails({ email });
        if (!isEmailExist.error) {
          throw new CustomError(RES_MSG.COMMON.EMAIL_ALREADY_EXIST, RESPONSES.CONFLICT);
        }
        updateProfile = {
          ...updateProfile,
          email,
          _id: userId,
          name: name || userDetails.data.name,
        };
        otpResponse = await CommonHelper.sendOTP(updateProfile, otpTypeEnum.VERIFICATION_OTP, otpMethodsEnum.EMAIL);
      }
      if (!isMobileVerify && mobile) {
        const isMobileExist: PromiseResolve = await UserService.fetchUserDetails({ mobile });
        if (!isMobileExist.error) {
          throw new CustomError(RES_MSG.COMMON.MOBILE_ALREADY_EXIST, RESPONSES.CONFLICT);
        }
        updateProfile = {
          ...updateProfile,
          mobile,
          countryCode,
          _id: userId,
          name: name || userDetails.data.name,
        };
        otpResponse = await CommonHelper.sendOTP(updateProfile, otpTypeEnum.VERIFICATION_OTP, otpMethodsEnum.MOBILE);
      }
      // If OTP sending failed, handle the error
      if (otpResponse?.error) throw new CustomError(otpResponse.message, otpResponse.status);
      const updateUserResp: PromiseResolve = await UserService.updateUserDetails(updateProfile, { _id: userId });
      if (updateUserResp.error) throw new CustomError(updateUserResp.message, updateUserResp.status);

      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: (!isEmailVerify && email) || (!isMobileVerify && mobile) ? 'Verification otp send successfully' : RES_MSG.USER.USER_UPDATION_SUCCESS,
        data: null,
      });
    } catch (error) {
      logger.error(error, 'updateProfileController Error');

      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };
  /**
   * Handles the updateKyc process.
   * The function first fetches the user details from the database, verifies if the user has already submitted the KYC or not.
   * If the user has already submitted the KYC, it throws an error.
   * If not, it updates the user details in the database with the new values.
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise<PromiseResolve>}
   */
  public updateKyc = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { body: requestedkycDetails } = req;
      const {
        userInfo: { userId },
      } = req;
      const userDetails: PromiseResolve = await UserService.fetchUserDetails({ _id: userId }, [], [], true);
      const { data: user, error: userDetailsError, message: userDetailsMessage, status: userDetailsStatus } = userDetails;

      if (userDetailsError) throw new CustomError(userDetailsMessage, userDetailsStatus);

      const { kycCount, kycDetails: userKycDetails, kycStatus } = user;
      if (kycCount === maxKycAttempt) {
        throw new CustomError(RES_MSG.ERROR_MSG.USER_BLOCKED, RESPONSES.BAD_REQUEST);
      }

      if (kycStatus === KycStatusEnum.PENDING) {
        throw new CustomError(RES_MSG.ERROR_MSG.ALREADY_KYC_SUBMITTED, RESPONSES.BAD_REQUEST);
      }

      if (kycStatus === KycStatusEnum.APPROVED) {
        throw new CustomError(RES_MSG.ERROR_MSG.ALREADY_KYC_APPROVED, RESPONSES.BAD_REQUEST);
      }
      if (requestedkycDetails?.wallets) {
        const { error: isUserExist } = await this.getExistingUser(requestedkycDetails.wallets[0].address);
        if (isUserExist) {
          throw new CustomError(RES_MSG.USER.USER_ALREADY_EXIST, RESPONSES.CONFLICT);
        }
      }
      const currentKycSteps = userKycDetails?.kycSteps || 0;
      const { kycSteps: requestedKycSteps, primaryContactInfo, mainInformation, beneficialOwners, managementInfo } = requestedkycDetails;

      if (primaryContactInfo?.personalInformation?.dob || mainInformation?.dob) {
        const formattedDate = await CommonHelper.formatDates(primaryContactInfo?.personalInformation?.dob || mainInformation?.dob);

        if (primaryContactInfo?.personalInformation?.dob) primaryContactInfo.personalInformation.dob = formattedDate;

        if (mainInformation?.dob) mainInformation.dob = formattedDate;

        await UserService.updateUserDetails({ dob: formattedDate.toString() }, { _id: userId });
      }

      // Merge the data to avoid duplicates
      // if (requestedkycDetails.wallets) {
      //   requestedkycDetails.wallets = CommonHelper.mergeWithoutDuplicates(requestedkycDetails.wallets, userKycDetails.wallets, ['address', 'type']);
      // }
      if (beneficialOwners) {
        requestedkycDetails.beneficialOwners = CommonHelper.mergeWithoutDuplicates(beneficialOwners, userKycDetails.beneficialOwners, ['personalInformation.socialSecurityNumber']);
      }

      if (managementInfo) {
        requestedkycDetails.managementInfo = CommonHelper.mergeWithoutDuplicates(managementInfo, userKycDetails.managementInfo, ['personalInformation.email']);
      }

      const updateData = { ...requestedkycDetails };

      if (requestedKycSteps === currentKycSteps + 1) {
        updateData.kycSteps = requestedKycSteps;
      } else if (requestedKycSteps <= currentKycSteps) {
        delete updateData.kycSteps;
      } else {
        return ResponseHandler.error(res, {
          status: RESPONSES.BAD_REQUEST,
          error: true,
          message: currentKycSteps === 0 ? 'You can only proceed to step 1' : `Invalid KYC step progression. Current step: ${currentKycSteps}. You can only proceed to step ${currentKycSteps + 1}.`,
        });
      }

      const updateUserResp = await UserService.updateUserKycDetails(updateData, { _id: userId });
      const { error: updateUserError, message: updateUserMessage } = updateUserResp;

      if (updateUserError) {
        throw new Error(updateUserMessage);
      }

      if (kycStatus !== KycStatusEnum.PENDING && requestedKycSteps === 1) {
        await UserService.updateUserDetails({ kycStatus: KycStatusEnum.IN_PROGRESS }, { _id: userId });
      } else if (updateData.isFinalSubmission) {
        if (kycCount >= maxKycAttempt) {
          await UserService.updateUserDetails(
            {
              // kycStatus: KycStatusEnum.PENDING,
              kycCount: kycCount + 1,
              isActive: false,
            },
            { _id: userId },
          );
        } else {
          await UserService.updateUserDetails({ kycStatus: KycStatusEnum.PENDING, kycCount: kycCount + 1 }, { _id: userId });
        }

        await kafkaService.sendMessageToAdmin({
          value: JSON.stringify({
            ...updateData,
            kycStatus: KycStatusEnum.PENDING,
            kycCount,
            _id: userId,
            type: queueMessageTypeEnum.USER,
          }),
        });

        // send kyc details to notification topic
        await kafkaService.sendMessageToNotification({
          value: JSON.stringify({
            type: user?.userType === UserTypeEnum.Investor ? 'kyc' : 'kyb',
            details: {
              ...updateData,
              _id: userId,
            },
          }),
        });

        //emit socket
        const type = user?.userType === UserTypeEnum.Investor ? 'kyc' : 'kyb';
        socketHelper.sendMessageToSingleUser(userId.toString(), type, updateData);

        const emailDetail = { name: user.name };
        const emailTemplate = user?.userType === UserTypeEnum.Investor ? 'kycreceived' : 'kybreceived';
        emailHelper.sendEmailTemplate(user.email, emailTemplate, emailDetail);
      }

      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        data: kycCount + 1,
        message: RES_MSG.USER.USER_KYC_UPDATE_SUCCESS,
      });
    } catch (error) {
      console.log('error', error);
      logger.error(error, 'updateKycController Error');
      if (error.keyPattern?.['mainInformation.nationalIdNumber'] || JSON.stringify(error)?.includes('nationalIdNumber')) {
        return { status: RESPONSES.BAD_REQUEST, error: true, message: RES_MSG.ERROR_MSG.ID_ERR };
      }
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * logout user.
   * @param {Request} req - The request object containing the email in the userInfo property.
   * @param {Response} res - The response object used to send back the result.
   * @returns {Promise<PromiseResolve>} - A promise that resolves with the response result.
   *
   * This function will logout the user by deleting the access token from Redis.
   * @throws {CustomError} If there is an error while deleting the access token or any other error.
   */
  public logOut = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { email } = req.userInfo;
      await RedisHelper.deleteKey(`accessToken:${email}`);

      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.LOGOUT,
      });
    } catch (error: any) {
      logger.error(error, 'logOut Error');

      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Handles the 2FA process.
   * @param {Request} req - The request object containing the token in the body.
   * @param {Response} res - The response object used to send back the result.
   * @returns {Promise<PromiseResolve>} - A promise that resolves with the response result.
   *
   * This function will generate a 2FA secret and generate a QR code based on the secret.
   * It will then store the secret in Redis and update the user's details in the database.
   * If the user already has 2FA enabled, it will throw a bad request error.
   */
  public enable2FA = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { userId, email } = req.userInfo;
      const secret = authenticator.generateSecret();
      const otpAuth = authenticator.keyuri(email, CONFIG.PROJECT.NAME, secret);
      const qrCode = await toDataURL(otpAuth);
      // await RedisHelper.setString(twoFactorSecretKey, secret, CONFIG.REDIS.OTP_EXPIRY)
      const {
        data: { is2FAActive },
      } = await UserService.fetchUserDetails({ email });

      if (is2FAActive) {
        throw new CustomError(RES_MSG.COMMON.BAD_REQUEST, RESPONSES.BAD_REQUEST);
      }
      const updateUserResp = await UserService.updateUserDetails({ twoFASecret: secret }, { _id: userId });
      if (updateUserResp.error) throw new CustomError(updateUserResp.message, updateUserResp.status);

      return ResponseHandler.success(res, {
        status: RESPONSES.CREATED,
        error: false,
        message: RES_MSG.TWO_FA.CREATED,
        data: { qrCode, secret },
      });
    } catch (error) {
      logger.error(error, 'enable2FAController Error');

      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Handles the forgot2FA process.
   * This function validates the provided token and sends an OTP to the user to disable 2FA.
   * @param {Request} req - The request object containing the token in the body.
   * @param {Response} res - The response object used to send back the result.
   * @returns {Promise<PromiseResolve>} - A promise that resolves with the response result.
   *
   * This function validates the provided token and sends an OTP to the user to disable 2FA.
   * The token is validated using CommonHelper.isValidToken.
   * If the token is invalid or expired, an error is thrown.
   * If the token is valid, the user details are fetched using UserService.fetchUserDetails.
   * If the user details are invalid, an error is thrown.
   * Then, an OTP is sent to the user using CommonHelper.sendOTP.
   * If the OTP sending fails, an error is thrown.
   * If the OTP is sent successfully, a success response is returned.
   */
  public forgot2FA = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { token } = req.body;
      const isTokenValid: PromiseResolve = await CommonHelper.isValidToken(token);
      if (isTokenValid.error) {
        throw new CustomError(RES_MSG.TWO_FA.TOKEN_EXPIRE, RESPONSES.UN_AUTHORIZED);
      }
      const { userId } = isTokenValid.data;
      const userDetails: PromiseResolve = await UserService.fetchUserDetails({ _id: userId });
      if (userDetails.error) {
        throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.UN_AUTHORIZED);
      }
      const otpResponse = await CommonHelper.sendOTP(userDetails.data, otpTypeEnum.DISABLE_2FA, otpMethodsEnum.EMAIL);

      // If OTP sending failed, handle the error
      if (otpResponse.error) throw new CustomError(otpResponse.message, otpResponse.status);

      // OTP sent successfully
      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.OTP_SENT_SUCCESS,
      });
    } catch (error) {
      logger.error(error, 'forgot2FAController Error');

      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Handles 2FA verify process.
   * Verifies the 2FA token provided and updates the user's details if the token is valid.
   * @param {Request} req - The request object containing the token in the body.
   * @param {Response} res - The response object used to send back the result.
   * @returns {Promise<PromiseResolve>} - A promise that resolves with the response result.
   */
  public verify2FA = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { token } = req.body;
      const { userId } = req.userInfo;
      const {
        error,
        data: { twoFASecret },
      } = await UserService.fetchUserDetails({ _id: userId }, [], ['kycDetails', 'wallets', 'isIssuer', 'issuerStatus', 'createdAt', 'updatedAt']);
      if (error) {
        throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.UN_AUTHORIZED);
      }
      const isValidTwoFAToken = authenticator.check(token, twoFASecret);
      if (!isValidTwoFAToken) {
        throw new CustomError(RES_MSG.TWO_FA.TOKEN_INVALID, RESPONSES.BAD_REQUEST);
      }
      const updateUserResp = await UserService.updateUserDetails({ is2FAActive: true }, { _id: userId });
      if (updateUserResp.error) throw new CustomError(updateUserResp.message, updateUserResp.status);
      await RedisHelper.setString(`2FA_${userId}`, 'true', CommonHelper.convertToMilliseconds(CONFIG.JWT_AUTH.AUTH_EXPIRE_TIME));

      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.TWO_FA.VERIFIED_SUCCESS,
        data: null,
      });
    } catch (error) {
      logger.error(error, 'verify2FAController Error');

      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Handles kycWebhook process.
   * Receives a webhook from SumSub and updates the user's kyc status.
   * @param {Request} req - The request object containing the webhook data in the body.
   * @param {Response} res - The response object used to send back the result.
   * @returns {Promise<PromiseResolve>} - A promise that resolves with the response result.
   */
  public kycWebhook = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const kycdata = req?.body;
      const levelName = req?.body?.levelName;
      const webhookType = kycdata.type || 'applicantCreated';
      const kycApplicationId = kycdata.applicantId || '66d597cd8d99ee1334801cd8';
      const { externalUserId } = kycdata;

      const userResponse: PromiseResolve = await UserService.fetchUserDetails({ _id: externalUserId });
      if (userResponse.error) {
        return ResponseHandler.success(res, {
          status: RESPONSES.SUCCESS,
          error: false,
          message: userResponse.message,
        });
      }
      const ans: any = await sumsubHelper.kycWebhookService(kycdata, webhookType, kycApplicationId, externalUserId, levelName);
      if (ans.error) {
        throw new Error(ans.message);
      }
      let result;
      if (req?.body?.levelName === 'kyb-level') {
        result = {
          userId: ans.data._id,
          sumSubKycStatus: ans.data.status,
        };
      } else {
        result = {
          userId: ans.data._id,
          sumsubKycStatus: ans.data.status,
        };
      }

      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: 'Success',
        data: result,
      });
    } catch (error) {
      logger.error(error, 'kycWebhook Error:');

      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || 'Failed',
      });
    }
  };
  /**
   * Handles disable 2FA process.
   * Verifies the 2FA token and disables 2FA for the user if the token is valid.
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise<PromiseResolve>}
   */
  public disable2FA = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { token } = req.body;
      const { userId, email } = req.userInfo;
      const {
        error,
        data: { twoFASecret },
      } = await UserService.fetchUserDetails({ email });
      if (error) {
        throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.UN_AUTHORIZED);
      }
      const isValidTwoFAToken = authenticator.check(token, twoFASecret);
      if (!isValidTwoFAToken) {
        throw new CustomError(RES_MSG.TWO_FA.TOKEN_INVALID, RESPONSES.BAD_REQUEST);
      }
      const updateUserResp = await UserService.updateUserDetails({ is2FAActive: false }, { _id: userId });
      if (updateUserResp.error) throw new CustomError(updateUserResp.message, updateUserResp.status);

      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.TWO_FA.DISABLE_SUCCESS,
        data: null,
      });
    } catch (error) {
      logger.error(error, 'disable2FAController Error');

      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Handles 2FA verify process.
   * It verifies the 2FA token and updates the user to mark 2FA as verified.
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise<PromiseResolve>}
   */
  public verifyLogin2FA = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { token, otp } = req.body;

      const isTokenValid: PromiseResolve = await CommonHelper.isValidToken(token);
      if (isTokenValid.error) {
        throw new CustomError(RES_MSG.TWO_FA.TOKEN_INVALID, RESPONSES.UN_AUTHORIZED);
      }
      const { userId } = isTokenValid.data;
      const userDetails: PromiseResolve = await UserService.fetchUserDetails({ _id: userId });
      const user = userDetails.data;
      if (userDetails.error) {
        throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.UN_AUTHORIZED);
      }
      const isValidTwoFAToken = authenticator.check(otp, user.twoFASecret);
      if (!isValidTwoFAToken) {
        throw new CustomError(RES_MSG.ERROR_MSG.INVALID_OTP, RESPONSES.BAD_REQUEST);
      }
      const updateUserResp = await UserService.updateUserDetails({ is2FAActive: true }, { _id: userId });
      if (updateUserResp.error) throw new CustomError(updateUserResp.message, updateUserResp.status);

      const tokenResp = await CommonHelper.createJWTAuth({ userId: user._id, email: user.email, userType: user.userType }, false, 'accessToken', CONFIG.JWT_AUTH.AUTH_EXPIRE_TIME);
      if (tokenResp.error) throw new CustomError(tokenResp.message, tokenResp.status);
      await RedisHelper.setString(`2FA_${userId}`, 'true', CommonHelper.convertToMilliseconds(CONFIG.JWT_AUTH.AUTH_EXPIRE_TIME));

      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.TWO_FA.VERIFIED_SUCCESS,
        data: {
          ...tokenResp.data,
          kycStatus: user.kycStatus,
          userType: user.userType,
          isKyc: user.isKyc,
          isIssuer: user.isIssuer,
          issuerStatus: user.issuerStatus,
        },
      });
    } catch (error) {
      logger.error(error, 'verifyLogin2FAController Error');

      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Handles the upload of documents.
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise<PromiseResolve>}
   */
  public uploadDocs = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { file } = req;

      const { userId: rowUserId } = req.userInfo;
      const { documentType, offeringId: rowOfferingId } = req.body;
      const offeringId = rowOfferingId ? new Types.ObjectId(rowOfferingId) : null;
      const userId = new Types.ObjectId(rowUserId);
      if (offeringId) {
        // Fetch current offering if offeringId exists
        const currentOffering = await OfferingService.fetchOfferingDetails({
          _id: new Types.ObjectId(offeringId),
          userId,
        });
        if (currentOffering.error) {
          throw new CustomError(currentOffering.message, currentOffering.status);
        }
      }
      if (!file) {
        return ResponseHandler.error(res, {
          status: RESPONSES.BAD_REQUEST,
          error: true,
          message: RES_MSG.ERROR_MSG.INVALID_FILE,
        });
      }
      let uploadFileRes: PromiseResolve;
      if (offeringId) {
        uploadFileRes = await CloudHelper.uploadFiles(userId.toString(), file, documentType, DocumentFolderTypesEnum.OFFERING, offeringId.toString());
      } else {
        uploadFileRes = await CloudHelper.uploadFiles(userId.toString(), file, documentType, DocumentFolderTypesEnum.USER);
      }

      if (uploadFileRes.error) {
        throw new CustomError(uploadFileRes.message || RES_MSG.COMMON.BAD_REQUEST, uploadFileRes.status || RESPONSES.BAD_GATEWAY);
      }

      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.DOCS_UPLOADED_SUCCESS,
        data: uploadFileRes.data,
      });
    } catch (error: any) {
      logger.error(error, 'uploadDocsController Error');

      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };
  /**
   * Handles the Reject Kyc process.
   * Rejects the user's kyc if user's kyc is in pending state, and sends an email to the user with the reason of rejection.
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise<PromiseResolve>}
   */
  public approveKyc = async (req: IUserModel): Promise<PromiseResolve> => {
    try {
      const { email, kycStatus, kycReason } = req;
      const userDetails: PromiseResolve = await UserService.fetchUserDetails({ email });
      if (userDetails.error) throw new CustomError(userDetails.message, userDetails.status);

      // eslint-disable-next-line eqeqeq
      if (userDetails.data.kycCount == process.env.BLOCK_USER) {
        const emailDetails = { name: userDetails?.data?.name };

        await emailHelper.sendEmailTemplate(email, 'accountBlocked', emailDetails);

        const updateProfile: IUpdateUserModel =
          KycStatusEnum.APPROVED === kycStatus
            ? {
                kycStatus,
                kycReason,
                isKyc: true,
                isActive: false,
              }
            : { kycStatus, kycReason, isActive: false };

        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const updateUserResp: PromiseResolve = await UserService.updateUserDetails(updateProfile, { email });
      }
      const updateProfile: IUpdateUserModel = KycStatusEnum.APPROVED === kycStatus ? { kycStatus, kycReason, isKyc: true } : { kycStatus, kycReason };
      const updateUserResp: PromiseResolve = await UserService.updateUserDetails(updateProfile, { email });
      if (updateUserResp.error) throw new CustomError(updateUserResp.message, updateUserResp.status);

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USER_UPDATION_SUCCESS,
      };
    } catch (error) {
      logger.error(error, 'approveKycController Error');

      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  };

  /**
   * Handles unblock user Handler.
   * @param {Request} req - Request object
   * @returns {Promise<PromiseResolve>} - Promise with Response object
   */
  public unblockHandler = async (req: IUserModel): Promise<PromiseResolve> => {
    try {
      const { email, isActive } = req;

      const userDetails: PromiseResolve = await UserService.fetchUserDetails({ email });
      if (userDetails.error) throw new CustomError(userDetails.message, userDetails.status);
      // const updateProfile: IUpdateUserModel = KycStatus.APPROVED == kycStatus ? { kycStatus, kycReason, isKyc: true } : { kycStatus, kycReason };
      const updateProfile: IUpdateUserModel = isActive ? { isActive, kycReason: '', kycCount: 0 } : { isActive };
      const updateUserResp: PromiseResolve = await UserService.updateUserDetails(updateProfile, { email });
      if (updateUserResp.error) throw new CustomError(updateUserResp.message, updateUserResp.status);

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USER_UPDATION_SUCCESS,
      };
    } catch (error) {
      logger.error(error, 'unblockHandler Error');

      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  };

  /**
   * Handles Become Issuer status.
   * @param {Request} req - Request object
   * @param {Response} res - Response object
   * @returns {Promise<PromiseResolve>} - Promise with Response object
   */
  public approveBecomeIssuer = async (req: IUserModel): Promise<PromiseResolve> => {
    try {
      const { email, issuerStatus, issuerReason } = req;
      const userDetails: PromiseResolve = await UserService.fetchUserDetails({ email });
      // Check if user exists and KYC is completed
      if (userDetails.error) throw new CustomError(userDetails.message, userDetails.status);
      if (userDetails.error || !userDetails.data.isKyc) {
        throw new CustomError(RES_MSG.USER.USER_KYC_PENDING, RESPONSES.BAD_REQUEST);
      }
      const updateProfile: IUpdateUserModel = IssuerStatusEnum.APPROVED === issuerStatus ? { issuerStatus, isIssuer: true, issuerReason } : { issuerStatus, issuerReason };
      const updateUserResp: PromiseResolve = await UserService.updateUserDetails(updateProfile, { email });
      if (updateUserResp.error) throw new CustomError(updateUserResp.message, updateUserResp.status);

      //emit socket to issuer to Become an issuer
      const { data } = updateUserResp;
      socketHelper.sendMessageToSingleUser(data._id.toString(), 'approve-issuer', updateUserResp);

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USER_UPDATION_SUCCESS,
        data: null,
      };
    } catch (error) {
      logger.error(error, 'becomeIssuerController Error');

      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  };

  /**
   * Handles become Issuer process.
   * @description This API is used to request to become an issuer. KYC should be completed before calling this API.
   * @param {Request} req - Request object
   * @param {Response} res - Response object
   * @returns {Promise<PromiseResolve>} - Promise with Response object
   */
  public becomeIssuer = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { email } = req.userInfo;
      const {
        error,
        status,
        message,
        data: { isKyc, issuerStatus, isRepresentatives },
      } = await UserService.fetchUserDetails({ email });
      if (error) throw new CustomError(message, status);
      if (!isKyc) {
        throw new CustomError(RES_MSG.USER.USER_KYC_PENDING, RESPONSES.BAD_REQUEST);
      }
      if (issuerStatus !== IssuerStatusEnum.NOT_APPLIED || isRepresentatives) {
        throw new CustomError(RES_MSG.COMMON.BAD_REQUEST, RESPONSES.BAD_REQUEST);
      }
      const updateUserResp: PromiseResolve = await UserService.updateUserDetails({ issuerStatus: IssuerStatusEnum.PENDING }, { email });
      if (updateUserResp.error) throw new CustomError(updateUserResp.message, updateUserResp.status);

      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.COMMON.REQ_SENT,
        data: null,
      });
    } catch (error) {
      logger.error(error, 'becomeIssuerController Error');

      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Handles the process of retrieving a token.
   * Validates the token provided in the request body and fetches user details.
   * Utilizes SumSub helper to generate a token.
   *
   * @param {Request} req - The request object containing the token and type.
   * @param {Response} res - The response object to return the result.
   * @returns {Promise<PromiseResolve>} - Returns a promise that resolves with the response.
   */
  public getToken = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { token, type } = req.body;
      const isTokenValid: PromiseResolve = await CommonHelper.isValidToken(token);
      if (isTokenValid.error) {
        throw new CustomError(RES_MSG.TWO_FA.TOKEN_EXPIRE, RESPONSES.UN_AUTHORIZED);
      }
      const { userId, email } = isTokenValid.data;
      const userDetails: PromiseResolve = await UserService.fetchUserDetails({ email });
      if (userDetails.error) {
        throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.UN_AUTHORIZED);
      }
      const { name } = userDetails.data;
      const resp = await sumsubHelper.getToken(userId, type, name);

      return ResponseHandler.success(res, {
        message: RES_MSG?.SUCCESS_MSG.REGISTER_SUCCESS,
        status: RESPONSES.CREATED,
        error: false,
        data: resp,
      });
    } catch (error) {
      logger.error(error, 'getTokenController Error');

      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Handles getToken process.
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise<PromiseResolve>}
   * @description This method is used to handle the getToken process.
   * It calls the getTransferAgent method of the admin grpc client
   * and returns the response.
   *
   * The method first calls the getTransferAgent method of the admin grpc client
   * and then parses the response data if it is a string.
   * If the grpc call is successful, it returns a success response with the response data.
   * If the grpc call fails, it returns an error response with the error message.
   */
  public getTransferAgent = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      adminClient.client.getTransferAgent(req.query, async (error: any, response: any) => {
        if (error || response.error) {
          logger.error(error, 'gRPC Error:');

          return ResponseHandler.error(res, {
            status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
            error: true,
            message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
          });
        }
        const data = typeof response.data === 'string' ? JSON.parse(response.data) : response.data;

        return ResponseHandler.success(res, {
          message: response.message || RES_MSG?.SUCCESS_MSG.REGISTER_SUCCESS,
          status: response.status || RESPONSES.SUCCESS,
          error: false,
          data,
        });
      });
    } catch (error) {
      logger.error(error, 'getTokenController Error');

      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };
  /**
   * Handles getTransferAgentDetails process.
   * @param {Request} req - The request object containing parameters.
   * @param {Response} res - The response object to return the result.
   * @returns {Promise<PromiseResolve>} - Returns a promise that resolves with the response.
   * @description This method is used to handle the getTransferAgentDetails process.
   * It calls the getTransferAgentDetails method of the admin grpc client
   * and returns the response.
   */
  public getTransferAgentDetails = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      adminClient.client.getTransferAgentDetails({ taId: req.params.transferAgentId }, async (error: any, response: any) => {
        if (error || response.error) {
          logger.error(error, 'gRPC Error:');

          return ResponseHandler.error(res, {
            status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
            error: true,
            message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
          });
        }
        const data = typeof response.data === 'string' ? JSON.parse(response.data) : response.data;

        return ResponseHandler.success(res, {
          message: response.message || RES_MSG?.SUCCESS_MSG.REGISTER_SUCCESS,
          status: response.status || RESPONSES.SUCCESS,
          error: false,
          data,
        });
      });
    } catch (error) {
      logger.error(error, 'getTokenController Error');

      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };
  /**
   * Fetches the user profile details.
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise<PromiseResolve>}
   * @description This method fetches the user profile details.
   * It calls the fetchUserDetails method of the UserService
   * and returns the response.
   *
   * The method takes the user ID from the request body.
   * It then fetches the user details using the UserService.
   * If there is an error, it throws a CustomError with the error message and status.
   * If the user details are successfully fetched, it creates a new object with the required fields.
   * Finally, it returns a success response with the user profile details.
   */
  public getUserProfileDetails = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { id, kyc } = req.body;
      const userId = id ? new Types.ObjectId(id) : null;
      const searchQuery = { _id: userId };

      const { error, message, status = RESPONSES.SUCCESS, data: user } = await UserService.fetchUserDetails(searchQuery, [], [], kyc);

      if (error) throw new CustomError(message, status);

      const { _id, name, legalFullName, mobile, countryCode, email, isEmailVerify, isMobileVerify, isKyc, kycStatus, sumSubKycStatus, userImage, dob, userType, kycDetails, isIssuer, issuerStatus, isActive, createdAt } = user;

      const profileUser = {
        _id,
        name,
        legalFullName,
        mobile: `${countryCode}${mobile}`,
        countryCode,
        email,
        isEmailVerify,
        isMobileVerify,
        isKyc,
        kycStatus,
        sumSubKycStatus,
        userImage,
        dob,
        userType,
        kycDetails,
        isIssuer,
        issuerStatus,
        isActive,
        createdAt,
      };

      return ResponseHandler.success(res, {
        status,
        error: false,
        message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
        data: profileUser,
      });
    } catch (error: any) {
      logger.error(error, 'getUserProfileDetailsController Error');

      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };

  /**
   * Checking user email in Transfer Agent and Subadmin list fetching using gRPC.
   * @param {Request} data
   */
  public getExistingUser = async (data: string): Promise<PromiseResolve> => {
    return new Promise((resolve, reject) => {
      try {
        adminClient.client.getExistingUser({ search: data }, (error: any, response: any) => {
          if (error) {
            logger.error(error, 'gRPC Error:');
            return reject({
              status: error?.status || RESPONSES.INTERNAL_SERVER_ERROR,
              error: true,
              message: error?.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
            });
          }

          const parsedData = typeof response.data === 'string' ? JSON.parse(response.data) : response.data;

          resolve({
            message: response.message || RES_MSG?.SUCCESS_MSG.REGISTER_SUCCESS,
            status: response.status || RESPONSES.SUCCESS,
            error: response.error || false,
            data: parsedData,
          });
        });
      } catch (error) {
        logger.error(error, 'getExistingUser Error');

        reject({
          status: error?.status || RESPONSES.INTERNAL_SERVER_ERROR,
          error: true,
          message: error?.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        });
      }
    });
  };
}

export default new UserAuthenticationController();
