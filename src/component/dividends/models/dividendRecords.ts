import mongoose, { Schema, Document } from 'mongoose';

interface IUserDetail {
  name: string;
  email: string;
  images?: string | null;
  address: string;
  dividendAmount: string;
  dividendFees: string;
  dividendPerToken: string;
  dividendPerTokenWithoutFees: string;
  yield: number;
  status: string;
  transactionHash?: string;
}

export interface IDividendData extends Document {
  offeringId: mongoose.Types.ObjectId;
  dividendId: mongoose.Types.ObjectId;
  offeringName: string;
  dividendAmount: number;
  dividendType: string;
  paymentMethod: string;
  recordDate: Date;
  declarationDate: Date;
  status: string;
  userDetails: IUserDetail[];
  totalInvestor: number;
  totalTokenHeld: string;
  dividendPerToken: string;
  yield: string;
  navPrice: string;
}

const userDetailSchema = new Schema<IUserDetail>(
  {
    name: { type: String, required: true },
    email: { type: String, required: true },
    images: { type: String, default: null },
    address: { type: String, required: true },
    dividendAmount: { type: String, required: true },
    dividendFees: { type: String, required: true },
    dividendPerToken: { type: String, required: true },
    dividendPerTokenWithoutFees: { type: String, required: true },
    yield: { type: Number, required: true },
    status: { type: String, enum: ['PENDING', 'SUCCESS', 'FAILED'], default: 'PENDING' },
    transactionHash: { type: String },
  },
  { _id: false },
);

const dividendDataSchema = new Schema<IDividendData>(
  {
    offeringId: { type: Schema.Types.ObjectId, ref: 'Offering', required: true },
    dividendId: { type: Schema.Types.ObjectId, required: true },
    offeringName: { type: String, required: true },
    status: { type: String, default: 'ACTIVE', enum: ['ACTIVE', 'COMPLETED'], required: false },
    dividendAmount: { type: Number, required: true },
    dividendType: { type: String, required: true },
    paymentMethod: { type: String, required: true },
    recordDate: { type: Date, required: true },
    declarationDate: { type: Date, required: true },
    userDetails: { type: [userDetailSchema], default: [] },
    totalInvestor: { type: Number, required: true },
    totalTokenHeld: { type: String, required: false, default: '0' },
    dividendPerToken: { type: String, required: false, default: '0' },
    yield: { type: String, required: false, default: '0' },
    navPrice: { type: String, required: false, default: '0' },
  },
  {
    timestamps: true, // adds createdAt and updatedAt
    versionKey: false,
  },
);

dividendDataSchema.index({ dividendId: 1 });

export default mongoose.model<IDividendData>('DividendRecord', dividendDataSchema);
