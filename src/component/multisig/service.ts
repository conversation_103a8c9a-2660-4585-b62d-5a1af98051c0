import { multisigModel } from './models';
import { MultisigInput, MultisigModelItf } from './interface';
import { PromiseResolve } from '../../utils/common.interface';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import logger from '../../helpers/logging/logger.helper';
import { Types } from 'mongoose';
import UserService from '../userAuthentications/service';
import { userSchema } from '../userAuthentications/models/user.model';
import { OrderSchema } from '../order/models/order.model';
import { whitelistSchema } from '../offerings/models/whitelist.model';
import { offeringSchema } from '../offerings/models/offerings.model';
import TransferRequest from '../transfer/models/transfer';

export const service = {
  // get duplicates array by sorting
  findDuplicates<T extends Record<string, any>>(array: T[], keys: (keyof T)[]): string | null {
    for (const key of keys) {
      const sorted = [...array].sort((a, b) => String(a[key]).localeCompare(String(b[key])));

      for (let i = 1; i < sorted.length; i++) {
        if (String(sorted[i][key]) === String(sorted[i - 1][key])) {
          return `Duplicate ${String(key)}: ${sorted[i][key]}`;
        }
      }
    }

    return null;
  },
  async signersValidate(dataToCheck: { signers: { userId: string; address?: string; walletAddress?: string }[]; checkAddresss?: boolean; issuerId?: string }): Promise<PromiseResolve> {
    try {
      const signerFinalData = [];
      for (const signer of dataToCheck.signers) {
        const { error: userError, message: userMessage, data: userData } = await UserService.fetchUserDetails({ _id: signer.userId }, [], [], true);
        if (userError) {
          throw new Error(userMessage || 'Failed to fetch user details');
        }
        if (dataToCheck.issuerId) {
          if (!userData.invitedBy.includes(dataToCheck.issuerId)) {
            throw new Error(`User ${signer.userId} is not invited by ${dataToCheck.issuerId}`);
          }
        }
        if (dataToCheck.checkAddresss) {
          const walletAddress = userData?.kycDetails?.wallets?.[0]?.address;
          if (!walletAddress) {
            throw new Error(`Wallet address not found for userId: ${signer.userId}`);
          } else if (walletAddress !== signer.address) {
            throw new Error(`Wallet address mismatch for userId: ${signer.userId}`);
          }
        }
        if (userData?.kycDetails?.wallets?.[0]?.address) {
          signer.walletAddress = userData?.kycDetails?.wallets?.[0]?.address;
        }
        signerFinalData.push(signer);
      }
      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
        data: signerFinalData,
      };
    } catch (error: any) {
      logger.error('signersValidate error', error);
      console.error('signersValidate error --->', error.message);
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        data: [],
      };
    }
  },

  async create(data: MultisigInput): Promise<PromiseResolve> {
    try {
      const { error: issuerError, message: issuerMessage, data: issuerDetais } = await UserService.fetchUserDetails({ _id: data.issuerId }, [], [], true);
      if (issuerError) throw new Error(issuerMessage);
      if (issuerDetais?.multisigAddress) {
        data['multisigAddress'] = issuerDetais?.multisigAddress;
      }
      if (issuerDetais?.kycDetails?.wallets[0]?.address) {
        data['walletAddress'] = issuerDetais?.kycDetails?.wallets[0]?.address;
      }
      if (issuerDetais?.kycDetails?.wallets[0]?.signerAddress) {
        data['signers'].push({
          userId: new Types.ObjectId(data.issuerId),
          address: issuerDetais?.kycDetails?.wallets[0]?.signerAddress,
          walletAddress: issuerDetais?.kycDetails?.wallets[0]?.address || '',
        });
      }

      const update = { $set: data };

      const result: MultisigModelItf = await multisigModel.findOneAndUpdate(
        {
          issuerId: new Types.ObjectId(data.issuerId),
        },
        update,
        { new: true, upsert: true, setDefaultsOnInsert: true, runValidators: true },
      );

      // const result: MultisigModelItf = await multisigModel.create(data);
      if (result) {
        const resultObject = result.toObject();
        return { status: RESPONSES.CREATED, error: false, message: RES_MSG.SUCCESS_MSG.REPRESENTATIVE_INVITE_SUCCESS, data: resultObject };
      }
      return { status: RESPONSES.BAD_REQUEST, error: true, message: RES_MSG.COMMON.SOMETHING_WRONG, data: {} };
    } catch (error) {
      logger.error(error, 'add Multisig error');
      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, data: {} };
    }
  },

  /**
   * Get Multisig with optional filters and pagination.
   *
   * @param {number} [page=1] - The page number for pagination.
   * @param {number} [limit=10] - The number of records per page.
   * @param {boolean} [isCsv=false] - If true, returns all data without pagination (e.g., for CSV export).
   * @param {Object} [filter={}] - Optional filters.
   * @param {string} [filter.userId] - Optional issuer/user ID to filter Multisig.
   * @param {string} [filter.search] - Optional search query to filter by email.
   * @returns {Promise<PromiseResolve>} - The Multisig list and pagination metadata.
   */

  async getMultisig(page = 1, limit = 10, isCsv: boolean = false, filter: { issuerId?: string; search?: string } = {}): Promise<PromiseResolve> {
    try {
      const skip = (page - 1) * limit;
      const { issuerId, search = '' } = filter;

      const pipeline: any[] = [];

      // Filter by issuerId if provided
      if (issuerId) {
        pipeline.push({
          $match: { issuerId: new Types.ObjectId(issuerId) },
        });
      }

      // Lookup issuer details
      pipeline.push(
        {
          $lookup: {
            from: 'users',
            localField: 'issuerId',
            foreignField: '_id',
            as: 'issuerDetails',
          },
        },
        {
          $unwind: {
            path: '$issuerDetails',
            preserveNullAndEmptyArrays: true,
          },
        },

        // Unwind signers
        {
          $unwind: {
            path: '$signers',
            preserveNullAndEmptyArrays: true,
          },
        },

        // Lookup each signer’s user details
        {
          $lookup: {
            from: 'users',
            localField: 'signers.userId',
            foreignField: '_id',
            as: 'signerUser',
          },
        },
        {
          $unwind: {
            path: '$signerUser',
            preserveNullAndEmptyArrays: true,
          },
        },

        // Add signer fields
        {
          $addFields: {
            'signers.name': '$signerUser.name',
            'signers.email': '$signerUser.email',
          },
        },

        // Group back the signers array
        {
          $group: {
            _id: '$_id',
            issuerId: { $first: '$issuerId' },
            name: { $first: '$issuerDetails.name' },
            email: { $first: '$issuerDetails.email' },
            multisigAddress: { $first: '$multisigAddress' },
            walletAddress: { $first: '$walletAddress' },
            threshold: { $first: '$threshold' },
            expireTime: { $first: '$expireTime' },
            isExpired: { $first: '$isExpired' },
            createdAt: { $first: '$createdAt' },
            updatedAt: { $first: '$updatedAt' },
            signers: {
              $push: {
                userId: '$signers.userId',
                address: '$signers.address',
                walletAddress: '$signers.walletAddress',
                name: '$signers.name',
                email: '$signers.email',
              },
            },
          },
        },
      );

      // Search by issuer email (after grouping)
      if (search) {
        pipeline.push({
          $match: { email: { $regex: search, $options: 'i' } },
        });
      }

      // Sort and paginate
      pipeline.push(
        { $sort: { createdAt: -1 } },
        {
          $facet: {
            multisig: isCsv ? [] : [{ $skip: skip }, { $limit: limit }],
            totalCount: [{ $count: 'count' }],
          },
        },
      );

      // Execute aggregation
      const [result] = await multisigModel.aggregate(pipeline).exec();
      const totalCount = result?.totalCount?.[0]?.count || 0;
      const multisig = result?.multisig || [];
      const totalPages = Math.ceil(totalCount / limit);

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
        data: {
          multisig,
          currentPage: page,
          totalPages,
          totalCount,
          nextPage: page < totalPages ? page + 1 : null,
          previousPage: page > 1 ? page - 1 : null,
        },
      };
    } catch (error: any) {
      logger.error(error, 'getMultisig');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        data: {},
      };
    }
  },
  async findAll(filter = {}): Promise<MultisigModelItf[]> {
    return await multisigModel.find(filter).lean();
  },

  async findById(id: string): Promise<MultisigModelItf | null> {
    return await multisigModel.findById(id).lean();
  },

  async update(id: string, data: Partial<MultisigInput>): Promise<MultisigModelItf | null> {
    return await multisigModel
      .findByIdAndUpdate(id, data, {
        new: true,
        runValidators: true,
      })
      .lean();
  },

  async remove(id: string): Promise<MultisigModelItf | null> {
    return await multisigModel.findByIdAndDelete(id).lean();
  },

  /**
   * Adds a signature to the multisig array of a document, validating user, signature, and threshold.
   * @param {object} payload - An object containing the document id, type, user id, wallet address, and signature
   * @returns {Promise<PromiseResolve>} - A promise containing the response
   */
  async addMultisig(payload: any): Promise<PromiseResolve> {
    console.log('addMultisig ~ payload:', payload);

    try {
      const { userId, id, type, walletAddress, signature, status = '', multisigsId = '' } = payload;

      // Validate user
      const user = await userSchema.findById(userId).lean();
      if (!user) {
        return this.errorResponse(RESPONSES.BAD_REQUEST, RES_MSG.ERROR_MSG.USER_NOT_FOUND);
      }

      // Resolve schema based on type
      const schemaMap: Record<string, any> = {
        ORDER: OrderSchema,
        REDEEM: OrderSchema,
        ORDER_APPROVAL: OrderSchema,
        TRANSFERREQUESTS: TransferRequest,
        OFFERING: offeringSchema,
        WHITELIST: whitelistSchema,
        FREEZ: whitelistSchema,
        UNFREEZ: whitelistSchema,
      };

      const schema = schemaMap[type.toUpperCase()];
      if (!schema) {
        return this.errorResponse(RESPONSES.BAD_REQUEST, RES_MSG.ERROR_MSG.INVALID_TYPE);
      }

      const udpateParams: any = {};
      let multiSigColName = 'multisig';
      const defaultMultisig = ['ORDER', 'WHITELIST', 'TRANSFERREQUESTS', 'REDEEM', 'OFFERING'];
      if (!defaultMultisig.includes(type.toUpperCase())) {
        switch (type.toUpperCase()) {
          case 'ORDER_APPROVAL':
            multiSigColName = 'multisigApproval';
            break;
          case 'FREEZ':
            multiSigColName = 'freezUnfreez';
            break;
          case 'UNFREEZ':
            multiSigColName = 'freezUnfreez';
            break;

          default:
            break;
        }
      }

      // Fetch document
      const document = await schema.findById(id).select(`userId ${multiSigColName}`).lean();
      if (!document) {
        return this.errorResponse(RESPONSES.BAD_REQUEST, `No ${type.toLowerCase()} found with this id`);
      }

      // multisig validate
      if (multisigsId) {
        const multisigDoc = await multisigModel.findById(multisigsId).lean();
        // check signer
        const hasValidUser = multisigDoc?.signers?.some((entry: any) => entry.userId?.toString() === userId.toString());
        if (!hasValidUser) {
          return this.errorResponse(RESPONSES.BAD_REQUEST, RES_MSG.ERROR_MSG.USER_NOT_FOUND);
        }
        // check active tx with type and id
        const txValidate = !!multisigDoc?.activeTx && multisigDoc?.activeTx.length > 0 ? multisigDoc?.activeTx?.some((entry: any) => entry.txTypeId?.toString() === id.toString() && entry.txType.toUpperCase() === type.toUpperCase()) : true;
        if (!txValidate) {
          return this.errorResponse(RESPONSES.BAD_REQUEST, RES_MSG.ERROR_MSG.TRANSACTION_INPROGRESS);
        }
        /* else {
          const updatedMultisig = await multisigModel.findByIdAndUpdate(multisigsId, { $set: { activeTx: [{ txTypeId: id, txType: type.toUpperCase() }] } }, { new: true, runValidators: true }).lean();
          if (!updatedMultisig) {
            return this.errorResponse(RESPONSES.BAD_REQUEST, RES_MSG.ERROR_MSG.VALIDATION_FAILED);
          }
        } */
      }

      // Empty multisig if status is done
      if (!!status && status.toLowerCase() === 'done') {
        const needToEmpty = ['FREEZ', 'UNFREEZ'];
        if (needToEmpty.includes(type.toUpperCase())) {
          const updatedDoc = await schema.findByIdAndUpdate(id, { $set: { [multiSigColName]: [] } }, { new: true, runValidators: true }).lean();
          if (!updatedDoc) {
            return this.errorResponse(RESPONSES.BAD_REQUEST, `Failed to update ${type.toLowerCase()} with ${multiSigColName}`);
          }
        }

        // udpate multisigs tx status
        if (multisigsId) {
          const updateTxStatus = await multisigModel.findByIdAndUpdate(multisigsId, { $set: { activeTx: [] } }, { new: true, runValidators: true }).lean();
          if (!updateTxStatus) {
            return this.errorResponse(RESPONSES.BAD_REQUEST, RES_MSG.ERROR_MSG.VALIDATION_FAILED);
          }
        }
        return { status: RESPONSES.SUCCESS, error: false, message: 'Signature updated successfully', data: {} };
      }

      udpateParams[multiSigColName] = { userId, walletAddress, sig: signature };

      // Check if signature already exists
      const hasSigned = document[multiSigColName]?.some((entry: any) => entry.userId?.toString() === userId.toString());
      if (hasSigned) {
        return this.errorResponse(RESPONSES.BAD_REQUEST, 'Signature from this user already exists in ' + multiSigColName);
      }
      const issuerId = document.userId?.toString();

      // Check if current user is issuer and threshold already met
      if (userId?.toString() === issuerId) {
        // const thresholdData = await multisigModel.findOne({ issuerId }).select('threshold').lean();
        const thresholdData = await multisigModel
          .findOne({ issuerId: new Types.ObjectId(issuerId) })
          .select('threshold')
          .lean();
        if (!thresholdData) {
          return this.errorResponse(RESPONSES.NOTFOUND, 'Threshold configuration not found');
        }

        if ((document[multiSigColName]?.length || 0) >= thresholdData.threshold) {
          return this.errorResponse(RESPONSES.BAD_REQUEST, `All signatures are already collected for this ${type.toLowerCase()}`);
        }
      }

      // Check if user is authorized signer and threshold met
      const signerThreshold = await multisigModel
        .findOne({
          issuerId,
          'signers.userId': userId,
        })
        .select('threshold')
        .lean();

      if (signerThreshold && (document[multiSigColName]?.length || 0) >= signerThreshold.threshold) {
        return this.errorResponse(RESPONSES.BAD_REQUEST, `All signatures are already collected for this ${type.toLowerCase()}`);
      }

      // Push signature
      const updatedDoc = await schema.findByIdAndUpdate(id, { $push: udpateParams }, { new: true, runValidators: true }).lean();
      // const updatedDoc = await schema.findByIdAndUpdate(id, { $push: { multisig: { userId, walletAddress, sig: signature } } }, { new: true, runValidators: true }).lean();

      if (!updatedDoc) {
        return this.errorResponse(RESPONSES.BAD_REQUEST, `Failed to update ${type.toLowerCase()} with ${multiSigColName}`);
      }
      // update activeTx
      if (multisigsId) {
        const updatedMultisig = await multisigModel.findByIdAndUpdate(multisigsId, { $set: { activeTx: [{ txTypeId: id, txType: type.toUpperCase() }] } }, { new: true, runValidators: true }).lean();
        if (!updatedMultisig) {
          return this.errorResponse(RESPONSES.BAD_REQUEST, RES_MSG.ERROR_MSG.VALIDATION_FAILED);
        }
      }
      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: 'Signature added successfully',
        data: updatedDoc,
      };
    } catch (error: any) {
      logger.error(`addMultisig Service Error: ${error?.message}`, error);
      return this.errorResponse(RESPONSES.INTERNAL_SERVER_ERROR, error?.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR);
    }
  },

  // Reusable error response helper
  async errorResponse(status: number, message: string): Promise<PromiseResolve> {
    return {
      status,
      error: true,
      message,
      data: {},
    };
  },
};
