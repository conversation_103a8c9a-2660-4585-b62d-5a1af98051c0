import { Document, Types } from 'mongoose';

export interface MultisigInput {
  issuerId: Types.ObjectId; // issuer id
  multisigAddress?: string; // multisig address
  walletAddress?: string; // wallet address
  threshold?: number;
  expireTime?: number;
  isExpired?: boolean;
  signers?: Array<{
    userId: Types.ObjectId; // repersentativ user id
    address: string; // wallet address
    walletAddress?: string;
  }>;
  activeTx?: Array<{
    txTypeId: Types.ObjectId;
    txType: string;
  }>;
}

export interface MultisigModelItf extends MultisigInput, Document {}
