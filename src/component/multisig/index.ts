import { Request, Response } from 'express';
import CustomError from '../../helpers/customError.helper';
import { PromiseResolve } from '../../utils/common.interface';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import { ResponseHandler } from '../../helpers/response.helper';
import logger from '../../helpers/logging/logger.helper';
import { service } from './service';
// import UserService from '../userAuthentications/service';
import { Types } from 'mongoose';
class MultisigController {
  /**
   * Handles the user Multisign add process.
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise<PromiseResolve>}
   */
  public addOrUpdateMultisig = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { isExpired = false, threshold = '', expireTime = '', signers = [] } = req.body;
      const { userId } = req?.userInfo || {};
      let issuerId = '';
      if (userId) {
        issuerId = userId;
      } else {
        issuerId = req.body.issuerId;
      }
      const dataToUpdate: any = { issuerId: new Types.ObjectId(issuerId) };

      if (threshold) {
        dataToUpdate['threshold'] = threshold;
      }
      if (expireTime) {
        dataToUpdate['expireTime'] = expireTime;
      }
      if (signers.length > 0) {
        // dataToUpdate['signers'] = signers;
        const errorDuplicat = service.findDuplicates(signers, ['userId', 'address']);
        if (errorDuplicat) throw new Error(errorDuplicat);

        // check address is same as user wallet addresss then checkAddresss== true
        // check issuerId then issuerId
        // const { error, status, message, data: signerData } = await service.signersValidate({ signers, issuerId, checkAddresss: true });
        const { error, message, data: signerData } = await service.signersValidate({ signers, issuerId });
        // const { error, status, message, data: signerData } = await service.signersValidate({ signers });
        if (error) throw new Error(message);
        dataToUpdate['signers'] = signerData;
      }
      if (isExpired) {
        dataToUpdate['isExpired'] = isExpired;
      }

      const { error: representativeError, message, status, data } = await service.create(dataToUpdate);
      if (representativeError) {
        throw new CustomError(message || RES_MSG?.ERROR_MSG.INVALID_EMAIL, status || RESPONSES.BAD_REQUEST);
      }

      return ResponseHandler.success(res, {
        message: RES_MSG?.SUCCESS_MSG.REPRESENTATIVE_INVITE_SUCCESS,
        status: RESPONSES.CREATED,
        error: false,
        data: data,
      });
    } catch (error) {
      logger.error(error, 'invite Multisign Error');

      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };

  /**
   * Get Multisign for the current user with optional search, CSV export, and pagination.
   *
   * @param {Request} req - Express request object containing query parameters:
   *   - page {string | number} [optional] - Page number for pagination (default: 1).
   *   - limit {string | number} [optional] - Number of records per page (default: 10).
   *   - search {string} [optional] - Search term to filter Multisign by email.
   *   - isCsv {string} [optional] - If 'true', disables pagination for CSV export.
   * @param {Response} res - Express response object.
   * @returns {Promise<PromiseResolve>} - API response with Multisign and pagination data.
   */

  public getRepresentative = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { page = '1', limit = '10', search = '', isCsv = 'false' } = req.query || {};

      const pageNo = page ? parseInt(page as string, 10) : 1;
      const limitNo = limit ? parseInt(limit as string, 10) : 10;
      const isExport = isCsv === 'true';

      const filter = {
        issuerId: req?.userInfo?.userId || '',
        search: search as string,
      };

      const { error, data, message } = await service.getMultisig(pageNo, limitNo, isExport, filter);

      if (error) {
        throw new CustomError(message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, RESPONSES.INTERNAL_SERVER_ERROR);
      }

      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
        data,
      });
    } catch (error: any) {
      logger.error(error, 'Error while fetching Multisign by user');
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Adds a multisig signature to an order or whitelist.
   * @param {Request} req - Express request object containing user and multisig data.
   * @param {Response} res - Express response object.
   * @returns {Promise<PromiseResolve>} - A promise resolving to the response handler result.
   */
  public addMultisig = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      // Extract the user ID and convert it to ObjectId type
      const userId = new Types.ObjectId(req.userInfo?.userId);

      // Destructure and normalize request body parameters
      const { type: rawType, walletAddress, signature, status = '', multisigId = '' } = req.body;
      const type = rawType ? rawType.toUpperCase() : null;

      // Extract ID from request parameters and convert to ObjectId type
      const idParam = req.params?.id;
      const id = idParam ? new Types.ObjectId(idParam) : null;
      const multisigsId = multisigId ? new Types.ObjectId(multisigId) : null;

      // Validate the 'type' field
      if (!type) {
        return ResponseHandler.error(res, {
          status: RESPONSES.BAD_REQUEST,
          error: true,
          message: 'Type is required',
        });
      }

      // Validate the 'id' field
      if (!id) {
        return ResponseHandler.error(res, { status: RESPONSES.BAD_REQUEST, error: true, message: 'ID is required' });
      }
      if (!multisigsId) {
        return ResponseHandler.error(res, { status: RESPONSES.BAD_REQUEST, error: true, message: 'Multisigs Id is required' });
      }

      // Define allowed types and check if the provided type is valid
      const allowedTypes = ['ORDER', 'WHITELIST', 'TRANSFERREQUESTS', 'REDEEM', 'ORDER_APPROVAL', 'OFFERING', 'FREEZ', 'UNFREEZ'];
      if (!allowedTypes.includes(type)) {
        return ResponseHandler.error(res, {
          status: RESPONSES.BAD_REQUEST,
          error: true,
          message: RES_MSG.ERROR_MSG.INVALID_TYPE,
        });
      }

      // Call the service to add the multisig signature
      const { error, data, message } = await service.addMultisig({
        userId,
        id,
        type,
        walletAddress,
        signature,
        status,
        multisigsId,
      });

      // Handle errors from the service
      if (error) {
        logger.error(`addMultisig Service Error: ${message}`);
        return ResponseHandler.error(res, {
          status: RESPONSES.INTERNAL_SERVER_ERROR,
          error: true,
          message: message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        });
      }

      // Return success response with data
      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: message || 'Signature added successfully',
        data,
      });
    } catch (err: any) {
      // Log and handle any unexpected errors
      logger.error(`addMultisig Unhandled Error: ${err?.message}`, err);
      return ResponseHandler.error(res, {
        status: err?.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: err?.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };
}

export default new MultisigController();
