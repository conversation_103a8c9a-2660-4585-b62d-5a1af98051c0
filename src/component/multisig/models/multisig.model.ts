import mongoose, { Schema } from 'mongoose';
import { MultisigModelItf } from '../interface';

const MultisigSchema: Schema<MultisigModelItf> = new Schema(
  {
    issuerId: { type: Schema.Types.ObjectId, ref: 'users', required: true },
    multisigAddress: { type: String, required: false },
    walletAddress: { type: String, required: false },
    threshold: { type: Number, required: false },
    expireTime: { type: Number, required: false, default: 12 }, // expire time in HRS
    isExpired: { type: Boolean, required: false, default: false },
    signers: {
      type: [
        new Schema(
          {
            userId: { type: Schema.Types.ObjectId, ref: 'users', required: true },
            address: { type: String, required: true },
            walletAddress: { type: String, required: false, default: '' },
          },
          { _id: false },
        ),
      ],
      required: false,
      default: undefined,
    },
    activeTx: {
      type: [
        new Schema(
          {
            txType: { type: String, required: false },
            txTypeId: { type: Schema.Types.ObjectId, required: false },
            timestamp: { type: Date, default: Date.now }, // ✅ auto-add current timestamp
          },
          { _id: false },
        ),
      ],
    },
  },
  { timestamps: true, versionKey: false },
);

// Compound index: Unique combination of issuerId
MultisigSchema.index({ issuerId: 1 }, { unique: true });

// Error handling
MultisigSchema.post('save', (error: any, doc: any, next: any) => {
  if (error.code === 11000) {
    if (error.keyPattern?.issuerId) {
      next(new Error('Multisig already exists with this issuer.'));
    } else {
      next(new Error('Duplicate key error.'));
    }
  } else {
    next(error);
  }
});
export const multisigModel = mongoose.model<MultisigModelItf>('Multisig', MultisigSchema);
