import * as Jo<PERSON> from 'joi';
import { JoiValidationResult } from '../../utils/common.interface';
import logger from '../../helpers/logging/logger.helper';

interface IInvite extends Document {
  multisigAddress?: string; // multisig address
  walletAddress?: string; // wallet address
  threshold?: number;
  expireTime?: Date;
  isExpired?: boolean;
  signers?: Array<{
    userId: string; // repersentativ user id
    address: string; // wallet address
  }>;
}

export class MultisigRequestValidation {
  /**
   * Validate the multisig request creation request
   * @returns {Promise<JoiValidationResult>}
   */

  static async addUpdateRequestValidation(inviteData: IInvite): Promise<JoiValidationResult> {
    try {
      const schema = Joi.object({
        expireTime: Joi.date().optional().label('Date of expiration').messages({ 'date.base': '{#label} must be a valid date' }),
        threshold: Joi.number().messages({ 'any.required': '{#label} is required' }),
        isExpired: Joi.boolean().messages({ 'any.required': '{#label} is required' }),
        signers: Joi.array()
          .items(
            Joi.object({
              userId: Joi.string().required(),
              address: Joi.string()
                .pattern(/^0x[a-fA-F0-9]{40}$/)
                .required(),
            }),
          )
          .messages({
            'any.required': '{#label} is required',
            'string.pattern.base': '{#label} must be a valid address',
          }),
        multisigAddress: Joi.string()
          .pattern(/^0x[a-fA-F0-9]{40}$/)
          .allow(null, '')
          .messages({
            'string.pattern.base': 'multisigAddress must be a valid address',
          }),
        walletAddress: Joi.string()
          .pattern(/^0x[a-fA-F0-9]{40}$/)
          .allow(null, '')
          .messages({
            'string.pattern.base': 'walletAddress must be a valid address',
          }),
      }).custom((value) => {
        return value;
      });

      const { error, value } = schema.validate(inviteData);
      if (error) {
        // return { error: true, value: '', message: error.details[0]?.context.message, status: 400 };
        return { error: true, value: '', message: error.details[0].message, status: 400 };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'multisigRequestValidation error');
      return {
        error: true,
        value: '',
        message: 'Internal Server Error',
        status: 500, // Internal Server Error
      };
    }
  }
}
