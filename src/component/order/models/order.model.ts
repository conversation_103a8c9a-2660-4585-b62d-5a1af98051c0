import mongoose, { Schema, Document, Types } from 'mongoose';
import { orderStatusEnum, paymentTypeEnum } from '../../../utils/common.interface';

/**
 * Complete order interface for tokenized asset trading and investment operations.
 * Defines the comprehensive data structure for orders including token transactions,
 * payment processing, fees, multi-signature support, and status tracking.
 * Extends Mongoose Document for database operations and type safety.
 *
 * @interface IOrder
 * @extends {Document}
 * @description Complete order data model for tokenized asset trading platform
 *
 */
export interface IOrder extends Document {
  /**
   * User ID who created the order.
   * References the user making the investment or trading request.
   *
   * @type {Types.ObjectId | string}
   */
  userId?: Types.ObjectId | string;

  /**
   * Offering ID that the order is placed for.
   * References the tokenized asset offering being invested in.
   *
   * @type {Types.ObjectId | string}
   */
  offeringId?: Types.ObjectId | string;

  /**
   * Wallet address for token delivery and transactions.
   * Destination address for minted tokens or transaction source.
   *
   * @type {string}
   * @format Ethereum address (0x...)
   */
  walletAddress?: string;

  /**
   * Net investment amount after fees and calculations.
   * Final amount that will be processed for the transaction.
   *
   * @type {string}
   * @format Decimal string for precision
   */
  amount?: string;

  /**
   * Gross investment amount before fee deductions.
   * Original amount specified by the investor before fees.
   *
   * @type {string}
   * @format Decimal string for precision
   */
  amountBeforeFee?: string;

  /**
   * Current status of the order in the processing workflow.
   * Tracks order lifecycle from pending to completion.
   *
   * @type {orderStatusEnum}
   * @values "PENDING", "APPROVED", "REJECTED", "MINTED", "CANCELLED"
   */
  status?: orderStatusEnum;

  /**
   * Flag indicating if tokens have been minted and delivered.
   * Confirms successful completion of token creation process.
   *
   * @type {boolean}
   * @default false
   */
  isMinted?: boolean;

  /**
   * Fee percentage applied to the order transaction.
   * Platform fee charged for processing the order.
   *
   * @type {string}
   * @format Percentage as decimal string
   */
  feesInPercentage?: string;

  /**
   * Flag indicating if the order has been fully settled.
   * Confirms all payments and token transfers are complete.
   *
   * @type {boolean}
   * @default false
   */
  isSettled?: boolean;

  /**
   * Flag indicating if associated tokens are currently frozen.
   * Used for compliance and regulatory holds.
   *
   * @type {boolean}
   * @default false
   */
  isFreezed?: boolean;

  /**
   * Token price at the time of order placement.
   * Historical price for order execution and reporting.
   *
   * @type {string}
   * @format Decimal string for precision
   */
  price?: string;

  /**
   * Current market price of the token.
   * Real-time price for valuation and analytics.
   *
   * @type {string}
   * @format Decimal string for precision
   */
  currentPrice?: string;

  /**
   * Fee amount collected by platform administrators.
   * Portion of fees allocated to platform operations.
   *
   * @type {string}
   * @format Decimal string for precision
   */
  adminFeeAmount?: string;

  /**
   * Timestamp when the order was received by the system.
   * Tracks order submission time for processing SLA.
   *
   * @type {Date}
   */
  orderReceived?: Date;

  /**
   * Timestamp when tokens were successfully minted.
   * Records completion time for delivery tracking.
   *
   * @type {Date}
   */
  orderMinted?: Date;

  /**
   * Number of tokens to be minted or transferred.
   * Token quantity based on investment amount and price.
   *
   * @type {string}
   * @format Decimal string for precision
   */
  quantity?: string;

  /**
   * Transaction hash for the order on the blockchain.
   * Unique identifier for blockchain transaction tracking.
   *
   * @type {string}
   * @format Blockchain transaction hash
   */
  txHash?: string;

  /**
   * Transaction hash specifically for token minting operation.
   * Separate hash for the minting transaction if different from main tx.
   *
   * @type {string}
   * @format Blockchain transaction hash
   */
  mintTxHash?: string;

  /**
   * Flag indicating if order confirmation email has been sent.
   * Tracks communication status for user notifications.
   *
   * @type {boolean}
   * @default false
   */
  emailSent?: boolean;

  /**
   * Flag indicating if minting completion email has been sent.
   * Tracks delivery notification status.
   *
   * @type {boolean}
   * @default false
   */
  mintEmailSent?: boolean;

  /**
   * Payment method used for the order transaction.
   * Specifies how the investment payment will be processed.
   *
   * @type {paymentTypeEnum}
   * @values "USDT", "USDC", "ETH", "BANK_TRANSFER", "WIRE"
   */
  paymentMethod?: paymentTypeEnum;

  /**
   * Type of order operation being performed.
   * Defines the specific action for the order.
   *
   * @type {string}
   * @values "MINTED", "FREEZE", "UNFREEZE", "CONVERT", "TRANSFER"
   */
  orderType?: string;

  /**
   * Principal amount for financial calculations.
   * Base amount used for interest and return calculations.
   *
   * @type {string}
   * @format Decimal string for precision
   */
  principleAmount?: string;

  /**
   * Weighted Average Price for multiple transactions.
   * Average price when order involves multiple executions.
   *
   * @type {string}
   * @format Decimal string for precision
   */
  wap?: string;

  /**
   * Profit or loss amount for the order.
   * Calculated gains or losses from the transaction.
   *
   * @type {string}
   * @format Decimal string (positive/negative)
   */
  profit?: string;

  /**
   * Reason for order status changes or rejections.
   * Administrative notes for order processing decisions.
   *
   * @type {string}
   */
  reason?: string;

  /**
   * Multi-signature wallet information for enhanced security.
   * Array of signatures required for high-value transactions.
   *
   * @type {Array<Object>}
   */
  multisig?: {
    /**
     * User ID of the signature provider.
     * @type {Types.ObjectId | string}
     */
    userId: Types.ObjectId | string;
    /**
     * Wallet address of the signer.
     * @type {string}
     */
    walletAddress: string;
    /**
     * Cryptographic signature data.
     * @type {string}
     */
    sig: string;
  }[];
  multisigApproval?: {
    userId: Types.ObjectId | string;
    walletAddress: string;
    sig: string;
  }[];
}

/**
 * Order update interface for partial order modifications.
 * Provides type-safe structure for order updates with all optional fields.
 * Used for status changes, administrative operations, and order processing.
 *
 * @interface IUpdateOrder
 * @description Flexible order update model with optional fields for partial updates
 *
 */
export interface IUpdateOrder {
  /**
   * Order identifier for update operations.
   * @type {string}
   */
  _id?: string;

  /**
   * Updated user ID.
   * @type {Types.ObjectId | string}
   */
  userId?: Types.ObjectId | string;

  /**
   * Updated offering ID.
   * @type {Types.ObjectId | string}
   */
  offeringId?: Types.ObjectId | string;

  /**
   * Updated wallet address.
   * @type {string}
   */
  walletAddress?: string;

  /**
   * Updated net amount.
   * @type {string}
   */
  amount?: string;

  /**
   * Updated gross amount.
   * @type {string}
   */
  amountBeforeFee?: string;

  /**
   * Updated order status.
   * @type {orderStatusEnum}
   */
  status?: orderStatusEnum;

  /**
   * Updated minting status.
   * @type {boolean}
   */
  isMinted?: boolean;

  /**
   * Updated fee percentage.
   * @type {string}
   */
  feesInPercentage?: string;

  /**
   * Updated settlement status.
   * @type {boolean}
   */
  isSettled?: boolean;

  /**
   * Updated freeze status.
   * @type {boolean}
   */
  isFreezed?: boolean;

  /**
   * Updated token price.
   * @type {string}
   */
  price?: string;

  /**
   * Updated current price.
   * @type {string}
   */
  currentPrice?: string;

  /**
   * Updated admin fee amount.
   * @type {string}
   */
  adminFeeAmount?: string;

  /**
   * Updated order received timestamp.
   * @type {Date}
   */
  orderReceived?: Date;

  /**
   * Updated order minted timestamp.
   * @type {Date}
   */
  orderMinted?: Date;

  /**
   * Updated token quantity.
   * @type {string}
   */
  quantity?: string;

  /**
   * Updated transaction hash.
   * @type {string}
   */
  txHash?: string;

  /**
   * Updated minting transaction hash.
   * @type {string}
   */
  mintTxHash?: string;

  /**
   * Updated email sent status.
   * @type {boolean}
   */
  emailSent?: boolean;

  /**
   * Updated minting email sent status.
   * @type {boolean}
   */
  mintEmailSent?: boolean;

  /**
   * Updated payment method.
   * @type {paymentTypeEnum}
   */
  paymentMethod?: paymentTypeEnum;

  /**
   * Updated order type.
   * @type {string}
   */
  orderType?: string;

  /**
   * Updated principal amount.
   * @type {string}
   */
  principleAmount?: string;

  /**
   * Updated weighted average price.
   * @type {string}
   */
  wap?: string;

  /**
   * Updated profit amount.
   * @type {string}
   */
  profit?: string;

  /**
   * Updated reason.
   * @type {string}
   */
  reason?: string;

  /**
   * Updated multi-signature data.
   * @type {Array<Object>}
   */
  multisig?: {
    userId: Types.ObjectId | string;
    walletAddress: string;
    sig: string;
  }[];
  multisigApproval?: {
    userId: Types.ObjectId | string;
    walletAddress: string;
    sig: string;
  }[];
}

/**
 * Mongoose schema definition for order data structure with comprehensive validation and indexing.
 * Defines database structure, validation rules, and relationships for order management.
 * Includes enum constraints, referential integrity, and performance optimization.
 *
 * @constant {Schema<IOrder>} order - Configured Mongoose schema for order data
 *
 * @description
 * Schema features:
 * - Foreign key references to users and offerings
 * - Enum validation for status and payment types
 * - Unique constraints for transaction hashes
 * - Default values for boolean and status fields
 * - Sparse indexing for optional unique fields
 * - Timestamp tracking for order lifecycle
 
 */
const order: Schema<IOrder> = new Schema(
  {
    userId: { type: Schema.Types.ObjectId, ref: 'users', required: true },
    offeringId: { type: Schema.Types.ObjectId, ref: 'offerings', required: true },
    walletAddress: { type: String, required: false },
    amount: { type: String, required: false },
    amountBeforeFee: { type: String, required: false },
    isMinted: { type: Boolean, required: false, default: false },
    isSettled: { type: Boolean, required: false, default: false },
    isFreezed: { type: Boolean, required: false, default: false },
    feesInPercentage: { type: String, required: false },
    quantity: { type: String, required: false },
    price: { type: String, required: false },
    currentPrice: { type: String, required: false },
    adminFeeAmount: { type: String, required: false },
    status: { type: String, enum: Object.values(orderStatusEnum), default: orderStatusEnum.PENDING },
    emailSent: { type: Boolean, default: false },
    mintEmailSent: { type: Boolean, default: false },
    orderReceived: { type: Date, required: false },
    orderMinted: { type: Date, required: false },
    paymentMethod: { type: String, enum: Object.values(paymentTypeEnum), default: paymentTypeEnum.USDT },
    txHash: { type: String, required: false, unique: true, sparse: true },
    mintTxHash: { type: String, required: false, unique: false, sparse: true, default: null },
    orderType: { type: String, required: false, default: orderStatusEnum.MINTED },
    principleAmount: { type: String, required: false },
    wap: { type: String, required: false },
    profit: { type: String, required: false },
    reason: { type: String, required: false },
    multisig: {
      type: [
        new Schema(
          {
            userId: { type: Schema.Types.ObjectId, ref: 'users', required: true },
            walletAddress: { type: String, required: true },
            // sig: { type: String, required: true },
            sig: { type: Schema.Types.Mixed, required: false, default: undefined },
          },
          { _id: false },
        ),
      ],
      required: false,
      default: undefined,
    },
    multisigApproval: {
      type: [
        new Schema(
          {
            userId: { type: Schema.Types.ObjectId, ref: 'users', required: true },
            walletAddress: { type: String, required: true },
            // sig: { type: String, required: true },
            sig: { type: Schema.Types.Mixed, required: false, default: undefined },
          },
          { _id: false },
        ),
      ],
      required: false,
      default: undefined,
    },
  },

  { timestamps: true, versionKey: false },
);

order.pre<IOrder>('save', async function (next) {
  // If txHash is null, skip the uniqueness check
  if (!this.txHash) {
    return next();
  }

  // Check if a document with the same txHash exists (excluding current document)
  const existingOrder = await mongoose.models.order.findOne({ txHash: this.txHash });
  if (existingOrder) {
    // Prevent insertion if a duplicate txHash is found
    return next(new Error('Duplicate txHash: This transaction hash already exists.'));
  }

  // Proceed with saving the document
  next();
});

order.pre<IOrder>('insertMany', async function (next, docs) {
  for (const doc of docs) {
    if (!doc.txHash) continue;

    const existingOrder = await mongoose.models.order.findOne({ txHash: doc.txHash });
    if (existingOrder) {
      return next(new Error(`Duplicate txHash: ${doc.txHash} already exists.`));
    }
  }
  next();
});

const OrderSchema = mongoose.model<IOrder>('order', order);
export { OrderSchema };
