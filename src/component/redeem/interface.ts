import { FilterQuery } from 'mongoose';
import { IPagination, PromiseResolve } from '../../utils/common.interface';
import { IOrder, IUpdateOrder } from '../order/models/order.model';

export interface IRedeemService {
  /**
   * @param {IRedeem} data
   * @returns {Promise<PromiseResolve>}
   * @memberof RedeemService
   */
  createRedeem(data: IUpdateOrder): Promise<PromiseResolve>;

  /**
   * @param {IRedeem} data
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof RedeemService
   */
  getRedeemRequests(data: FilterQuery<IOrder>, pagination: IPagination, isCsv: boolean): Promise<PromiseResolve>;
}
