import * as Jo<PERSON> from 'joi';
import { JoiValidationResult } from '../../utils/common.interface';

class RedeemValidation {
  /**
   * Validate create redeem request.
   */
  public async validateCreateRedeem(params: Record<string, any>): Promise<JoiValidationResult> {
    const schema = Joi.object({
      token: Joi.number()
        .unsafe()
        .required()
        .min(1)
        .max(999999999999999)
        .label('Token')
        .messages({ 'any.required': '{#label} is required', 'number.min': '{#label} must be a positive number', 'number.max': '{#label} cannot exceed 15 digits.', 'number.base': '{#label} must be a number' }),
      offeringId: Joi.string().required(),
    });

    const { error, value } = schema.validate(params);
    if (error) {
      return { error: true, value: '', message: error.message, status: 400 };
    }

    return { error: false, value };
  }

  /**
   * Validate pagination and filters for redeem requests.
   */
  public async validateGetRedeem(params: Record<string, any>): Promise<JoiValidationResult> {
    const schema = Joi.object({ search: Joi.string().optional(), page: Joi.number().min(1).default(1), limit: Joi.number().min(1).max(100).default(10), isCsv: Joi.boolean().default(false).optional() });

    const { error, value } = schema.validate(params);
    if (error) {
      return { error: true, value: '', message: error.message, status: 400 };
    }

    return { error: false, value };
  }
}

export default new RedeemValidation();
