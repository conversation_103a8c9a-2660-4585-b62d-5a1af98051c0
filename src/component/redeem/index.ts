import { Request, Response } from 'express';
import { Types } from 'mongoose';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import { ResponseHandler } from '../../helpers/response.helper';
import logger from '../../helpers/logging/logger.helper';
import RedeemService from './service';
import { orderStatusEnum } from '../../utils/common.interface';
import CustomError from '../../helpers/customError.helper';
import OfferingService from '../offerings/service';
import UserService from '../userAuthentications/service';
import { OrderSchema } from '../order/models/order.model';
import { calculate } from '../../helpers/bigMath';

class RedeemController {
  /**
   * Create a redeem request.
   */
  public createRedeemRequest = async (req: Request, res: Response): Promise<void> => {
    try {
      const { token, offeringId: hexOfferingId } = req.body;
      const userId = new Types.ObjectId(req.userInfo.userId);
      const offeringId = hexOfferingId ? new Types.ObjectId(hexOfferingId) : null;

      const isRedeemPending = await OrderSchema.findOne({ userId, offeringId, status: orderStatusEnum.PENDING, orderType: orderStatusEnum.REDEEM });
      if (isRedeemPending) {
        throw new CustomError('Redeem Request already pending', RESPONSES.CONFLICT);
      }
      const { error, data, status, message } = await OfferingService.fetchOfferingDetails({ _id: offeringId });
      if (error) throw new CustomError(message, status);
      // Fetch user portfolio
      const { data: portfolioData } = await UserService.fetchUserPortfolio(userId.toString(), false, {}, hexOfferingId);
      const { currentPrice, investedAmount, currentValue, profit, currentQuantity } = portfolioData.portfolio[0];

      const profitPerToken = calculate('div', profit, currentQuantity);
      const principleAmount = investedAmount;
      const wap = calculate('div', principleAmount, currentQuantity);
      const wapAmount = calculate('mul', wap, token);
      const profitAmount = calculate('mul', token, profitPerToken);
      // Fetch fee percentage
      const feePercentage = calculate('div', data.fee.redemptionFee, 100);
      const adminFeeAmount = Number(profitAmount) > 0 ? calculate('div', calculate('mul', profitAmount, feePercentage), 100) : 0; // Fees apply only to gains
      // Final payment after deducting fees
      const profitAfterAdminFee = calculate('sub', profitAmount, adminFeeAmount);
      const finalPayment = calculate('add', profitAfterAdminFee, wapAmount);
      if (Number(finalPayment) > Number(currentValue)) {
        throw new CustomError('Insufficient balance to redeem tokens', RESPONSES.BAD_REQUEST);
      }

      // Create redemption request
      const result = await RedeemService.createRedeem({
        userId,
        quantity: token,
        offeringId,
        price: currentPrice,
        amount: finalPayment,
        wap: wapAmount,
        profit: profitAfterAdminFee,
        feesInPercentage: calculate('mul', feePercentage, 100),
        adminFeeAmount: adminFeeAmount.toString(),
        status: orderStatusEnum.PENDING,
        orderType: orderStatusEnum.REDEEM,
        principleAmount,
      });

      // Respond with success
      ResponseHandler.success(res, { status: result.status || RESPONSES.SUCCESS, error: false, message: result.message || 'Redemption request created successfully!', data: result.data || {} });
    } catch (error: any) {
      logger.error(error, 'createRedeemRequest Error');
      ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, message: error.message || 'Internal Server Error', error: true });
    }
  };

  /**
   * Get redeem requests with pagination and lookup.
   */
  public getUserRedeemRequests = async (req: Request, res: Response): Promise<void> => {
    try {
      const { page = 1, limit = 10, search = '', isCsv } = req.query;
      if (req.params.id && !Types.ObjectId.isValid(req.params.id as string)) {
        throw new CustomError(RES_MSG.COMMON.BAD_REQUEST, RESPONSES.BAD_REQUEST);
      }
      const userId = new Types.ObjectId(req.userInfo.userId);
      const offeringId = new Types.ObjectId(req.params.id);
      const { error, message, status } = await OfferingService.fetchOfferingDetails({ _id: offeringId });
      if (error) throw new CustomError(message, status);
      const result = await RedeemService.getRedeemRequests({ offeringId, userId }, { page: Number(page), limit: Number(limit), search }, isCsv);
      ResponseHandler.success(res, { status: result.status || RESPONSES.SUCCESS, error: result.error, message: result.message || RES_MSG.SUCCESS_MSG.DATA_SUCCESS, data: result.data });
    } catch (error: any) {
      logger.error(error, 'getRedeemRequests Error');
      ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, error: true });
    }
  };

  public getRedeemRequests = async (req: Request, res: Response): Promise<void> => {
    try {
      const { page = 1, limit = 10, search = '', isCsv } = req.query;

      if (req.params.id && !Types.ObjectId.isValid(req.params.id as string)) {
        throw new CustomError(RES_MSG.COMMON.BAD_REQUEST, RESPONSES.BAD_REQUEST);
      }
      const userId = new Types.ObjectId(req.userInfo.userId);
      const offeringId = new Types.ObjectId(req.params.id);

      const offeringValidate: any = { _id: offeringId };
      if (req.userInfo.isRepresentatives) {
        let invitedBy: any = [];
        const userDetails: any = await UserService.fetchUserDetails({ _id: req.userInfo.userId }, [], [], true);
        invitedBy = userDetails?.data?.invitedBy.map((id: string) => new Types.ObjectId(id)) || [];
        offeringValidate['userId'] = { $in: invitedBy };
      } else {
        offeringValidate['userId'] = userId;
      }
      // const { error, message, status } = await OfferingService.fetchOfferingDetails({ _id: offeringId, userId });
      const { error, message, status } = await OfferingService.fetchOfferingDetails(offeringValidate);
      if (error) throw new CustomError(message, status);

      const result = await RedeemService.getRedeemRequests({ offeringId: req.params.id }, { page: Number(page), limit: Number(limit), search }, isCsv);
      ResponseHandler.success(res, { status: result.status || RESPONSES.SUCCESS, error: result.error, message: result.message || RES_MSG.SUCCESS_MSG.DATA_SUCCESS, data: result.data });
    } catch (error: any) {
      logger.error(error, 'getRedeemRequests Error');
      ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, error: true });
    }
  };
}

export default new RedeemController();
