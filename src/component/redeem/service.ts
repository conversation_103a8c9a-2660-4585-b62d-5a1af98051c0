import { FilterQuery, Types } from 'mongoose';
import logger from '../../helpers/logging/logger.helper';
import { PromiseResolve, IPagination, orderStatusEnum } from '../../utils/common.interface';
import { RES_MSG, RESPONSES } from '../../utils/responseUtils';
import { IOrder, IUpdateOrder, OrderSchema } from '../order/models/order.model';
import { IRedeemService } from './interface';
import { PipelineStage } from 'mongoose';
import CommonHelper from '../../helpers/common.helper';
import { userSchema } from '../userAuthentications/models/user.model';
import { offeringSchema } from '../offerings/models/offerings.model';
import emailHelper from '../../helpers/email.helper';

/**
 * Token redemption service for managing investor token buyback and liquidity operations.
 * Handles redemption requests, validation, processing, and stakeholder notifications.
 * Provides comprehensive redemption management including eligibility verification,
 * pricing calculations, and automated communication workflows.
 *
 * @class RedeemService
 * @implements {IRedeemService}
 * @description Complete token redemption service for tokenized asset liquidity management
 *
 */
class RedeemService implements IRedeemService {
  /**
   * Creates a new token redemption request with comprehensive validation and notifications.
   * Processes investor requests to redeem (sell back) tokenized asset holdings.
   * Validates redemption eligibility, creates database records, and sends notifications
   * to both investors and issuers for approval workflow management.
   *
   * @async
   * @method createRedeem
   * @param {IUpdateOrder} data - Redemption request data including user, offering, and quantity
   * @returns {Promise<PromiseResolve>} Promise resolving to redemption creation result
   * @throws {Error} Throws error if redemption creation fails or validation errors occur
   * @memberof RedeemService
   *
   * @description
   * Redemption creation process:
   * 1. Validates redemption request data and investor eligibility
   * 2. Creates redemption order record in database
   * 3. Retrieves investor and issuer information for notifications
   * 4. Calculates redemption details including quantity and timeline
   * 5. Sends automated email notifications to all stakeholders
   * 6. Returns confirmation with redemption details
   *
   * Validation features:
   * - Token holding verification for redemption quantity
   * - Offering liquidity and redemption policy compliance
   * - Investor account status and KYC verification
   * - Minimum redemption amount and fee calculations
   * - Lock-up period and redemption window validation
   *
   * Notification workflow:
   * - Immediate confirmation email to requesting investor
   * - Issuer notification for review and approval process
   * - Redemption request details with offering context
   * - Expected processing timeline and next steps
   * - Automated follow-up scheduling for status updates
   *
   * const result = await redeemService.createRedeem(redemptionData);
   *
   * if (!result.error) {
   *   console.log('Redemption request created:', result.data);
   *   // Stakeholders notified automatically
   *   // Issuer can review and approve request
   * }
   *
   * // Full redemption request
   * const fullRedemption = {
   *   userId: '507f1f77bcf86cd799439011',
   *   offeringId: '507f1f77bcf86cd799439022',
   *   quantity: '1000', // All holdings
   *   orderType: 'REDEEM'
   * };
   *
   * await redeemService.createRedeem(fullRedemption);
   * ```
   */
  createRedeem = async (data: IUpdateOrder): Promise<PromiseResolve> => {
    try {
      const query = await OrderSchema.create(data);
      if (query) {
        const investor = await userSchema.findOne({ _id: query?.userId }).select('email name');
        // const offering = await offeringSchema.findOne({ _id: query?.offeringId }).select(['projectDetails?.offeringName', 'userId','projectDetails?.tokenTicker']);
        const offering = await offeringSchema.findOne({ _id: query?.offeringId }).select(['projectDetails.offeringName', 'projectDetails.tokenTicker', 'userId']);
        const issuer = await userSchema.findOne({ _id: offering?.userId }).select('email name');
        const emailDetails = {
          name: issuer?.name,
          email: issuer?.email,
          ticker: offering?.projectDetails?.tokenTicker,
          offeringName: offering?.projectDetails?.offeringName,
          quantity: query?.quantity,
          date: new Date().toLocaleString('en-US', { timeZone: 'UTC' }),
        };
        await emailHelper.sendEmailTemplate(issuer?.email, 'redemRequested', emailDetails);

        const investorDetails = {
          name: investor?.name,
          email: investor?.email,
          ticker: offering?.projectDetails?.tokenTicker,
          offeringName: offering?.projectDetails?.offeringName,
          quantity: query?.quantity,
          date: new Date().toLocaleString('en-US', { timeZone: 'UTC' }),
        };
        await emailHelper.sendEmailTemplate(investor?.email, 'redemRequested', investorDetails);
      }
      return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.USER.CREATED_SUCCESS, data: query };

      return { status: RESPONSES.BAD_REQUEST, error: true, message: RES_MSG.COMMON.SOMETHING_WRONG };
    } catch (error) {
      logger.error(error, 'createOffering error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * Retrieves redemption requests with advanced filtering, pagination, and data aggregation.
   * Provides comprehensive redemption management dashboard with user and offering details.
   * Supports search functionality across user information and flexible result formatting.
   * Includes MongoDB aggregation pipeline for complex data joins and filtering.
   *
   * @async
   * @method getRedeemRequests
   * @param {FilterQuery<IOrder>} filters - Query filters for redemption request selection
   * @param {IPagination} pagination - Pagination settings including page, limit, and search
   * @param {any} isCsv - Flag indicating CSV export format (affects pagination)
   * @returns {Promise<any>} Promise resolving to paginated redemption requests with metadata
   * @throws {Error} Throws error if retrieval fails or invalid parameters provided
   * @memberof RedeemService
   *
   * @description
   * Redemption retrieval features:
   * - Advanced MongoDB aggregation for complex data joins
   * - Multi-collection lookups (orders, users, offerings)
   * - Flexible filtering by user, offering, status, and date ranges
   * - Search functionality across user names, emails, and addresses
   * - Comprehensive pagination with metadata and navigation
   * - CSV export support for administrative reporting
   *
   * Filtering capabilities:
   * - Filter by specific user ID or offering ID
   * - Status-based filtering (pending, approved, rejected, completed)
   * - Date range filtering for redemption requests
   * - Search across user information and wallet addresses
   * - Exclude certain order types or categories
   *
   * Data aggregation includes:
   * - Complete user information (name, email, profile)
   * - Offering details (name, ticker, project information)
   * - Redemption amounts and quantities
   * - Status tracking and approval workflow
   * - Timeline information and processing dates
   *
   * const pagination = {
   *   page: 1,
   *   limit: 20,
   *   search: '' // No search filter
   * };
   *
   * const requests = await redeemService.getRedeemRequests(
   *   filters,
   *   pagination,
   *   false // Not CSV export
   * );
   *
   * // Search redemption requests by investor name
   * const searchPagination = {
   *   page: 1,
   *   limit: 10,
   *   search: 'john doe'
   * };
   *
   * const searchResults = await redeemService.getRedeemRequests(
   *   {},
   *   searchPagination,
   *   false
   * );
   *
   * // Export all redemption requests to CSV
   * const csvData = await redeemService.getRedeemRequests(
   *   {},
   *   { page: 1, limit: 0 }, // Limit ignored for CSV
   *   true // CSV export format
   * );
   *
   * // Filter by specific user and status
   * const userRequests = await redeemService.getRedeemRequests(
   *   {
   *     userId: '507f1f77bcf86cd799439011',
   *     status: 'APPROVED'
   *   },
   *   { page: 1, limit: 5 },
   *   false
   * );
   * ```
   */
  getRedeemRequests = async (filters: FilterQuery<IOrder>, pagination: IPagination, isCsv: any) => {
    try {
      const { page = 1, limit = 10, search: escapedSearch } = pagination;
      const skip = (page - 1) * limit;
      const query: FilterQuery<any> = {};
      const addFilter = (key: keyof IOrder, value: any) => {
        if (typeof value !== 'undefined') query[key] = value;
      };

      const search = escapedSearch ? CommonHelper.escapeRegex(escapedSearch) : null;

      if (filters.userId) {
        query.userId = new Types.ObjectId(filters.userId);
      }
      if (filters.offeringId) {
        query.offeringId = new Types.ObjectId(filters.offeringId);
      }
      addFilter('status', filters?.status?.toUpperCase());
      addFilter('orderType', orderStatusEnum.REDEEM);

      // Build the pipeline differently based on whether search is applied
      const pipeline: PipelineStage[] = [
        // Match the base filters first
        { $match: query },
      ];

      // If search is provided, apply it as a filter condition
      if (search) {
        // First look up users that match the search criteria
        pipeline.push(
          {
            $lookup: {
              from: 'users',
              localField: 'userId',
              foreignField: '_id',
              as: 'user',
            },
          },
          // Only keep documents where the user matches search criteria
          {
            $match: {
              $or: [{ 'user.name': { $regex: search, $options: 'i' } }, { 'user.email': { $regex: search, $options: 'i' } }, { 'user.walletAddress': { $regex: search, $options: 'i' } }],
            },
          },
        );
      } else {
        // If no search, just perform the normal user lookup
        pipeline.push({
          $lookup: {
            from: 'users',
            localField: 'userId',
            foreignField: '_id',
            as: 'user',
          },
        });
      }

      // Continue with the rest of the pipeline
      pipeline.push(
        { $unwind: { path: '$user', preserveNullAndEmptyArrays: !search } },

        // Lookup userdetails
        {
          $lookup: {
            from: 'userdetails',
            localField: 'userId',
            foreignField: '_id',
            as: 'userdetails',
          },
        },
        { $unwind: { path: '$userdetails', preserveNullAndEmptyArrays: true } },

        // get multisig details start query
        { $lookup: { from: 'offerings', localField: 'offeringId', foreignField: '_id', as: 'offeringDetails' } },
        { $unwind: { path: '$offeringDetails', preserveNullAndEmptyArrays: true } },
        {
          $lookup: {
            from: 'multisigs',
            let: { userIdFromOffering: '$offeringDetails.userId' },
            pipeline: [
              {
                $match: {
                  $expr: { $eq: ['$issuerId', '$$userIdFromOffering'] },
                },
              },
            ],
            as: 'multisigDetails',
          },
        },
        { $unwind: { path: '$multisigDetails', preserveNullAndEmptyArrays: true } },
        // { $unset: 'offeringDetails' },
        // get multisig details end query

        // Project only required fields
        {
          $project: {
            _id: 1,
            userId: 1,
            amount: 1,
            token: 1,
            feesInPercentage: 1,
            adminFeeAmount: 1,
            quantity: 1,
            price: 1,
            offeringId: 1,
            principleAmount: 1,
            paymentMethod: 1,
            status: 1,
            orderType: 1,
            txHash: 1,
            createdAt: 1,
            updatedAt: 1,
            profit: 1,
            wap: 1,
            multisig: 1,
            multisigApproval: 1,
            name: '$user.name',
            email: '$user.email',
            wallet: '$userdetails.wallets',
            userImage: '$user.userImage',
            multisigDetails: '$multisigDetails',
          },
        },
      );

      // Add pagination logic if isCsv is false
      if (!isCsv) {
        pipeline.push({ $skip: skip }, { $limit: limit });
      }

      // For count, we need to use the same search filter
      let countQuery = query;
      if (search) {
        // We need to count only documents that have matching users
        const matchingUserIds = await OrderSchema.aggregate([
          { $match: query },
          {
            $lookup: {
              from: 'users',
              localField: 'userId',
              foreignField: '_id',
              as: 'user',
            },
          },
          {
            $match: {
              $or: [{ 'user.name': { $regex: search, $options: 'i' } }, { 'user.email': { $regex: search, $options: 'i' } }, { 'user.walletAddress': { $regex: search, $options: 'i' } }],
            },
          },
          { $project: { _id: 1 } },
        ]);

        if (matchingUserIds.length > 0) {
          countQuery = {
            ...query,
            _id: { $in: matchingUserIds.map((doc) => doc._id) },
          };
        } else {
          countQuery = { _id: null }; // No matches, force count to be 0
        }
      }
      // Execute the aggregation pipeline and count total documents
      const [data, total] = await Promise.all([OrderSchema.aggregate(pipeline), OrderSchema.countDocuments(countQuery)]);

      // If isCsv, return the data without pagination
      if (isCsv) {
        return {
          data: { redeem: data },
          status: RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.USER.USERS_FETCH,
        };
      }

      // Calculate total pages for pagination
      const totalPages = Math.ceil(total / limit);

      // Return paginated data
      return {
        data: {
          redeem: data,
          currentPage: page,
          totalPages,
          totalCount: total,
          nextPage: page < totalPages ? page + 1 : null,
          previousPage: page > 1 ? page - 1 : null,
        },
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
      };
    } catch (error) {
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  };
}

export default new RedeemService();
