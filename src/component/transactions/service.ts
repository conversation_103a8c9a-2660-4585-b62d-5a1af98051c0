import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import { PromiseResolve, IPagination } from '../../utils/common.interface';
import { ITransactionsService } from './interface';
import logger from '../../helpers/logging/logger.helper';
import { OrderSchema } from '../order/models/order.model';
import { FilterQuery } from 'mongoose';

/**
 * Blockchain transaction service for comprehensive transaction tracking and analytics.
 * Manages transaction history retrieval, status monitoring, and reporting for tokenized assets.
 * Provides detailed transaction data for compliance, auditing, and operational oversight.
 * Supports advanced filtering, search, and export capabilities for administrative use.
 *
 * @class TransactionsService
 * @implements {ITransactionsService}
 * @description Complete blockchain transaction management service for tokenization platform
 *
 * // Fetch transactions for specific offering
 * const transactions = await transactionService.fetchTransactions(
 *   { offeringId: 'offering123', status: 'MINTED' },
 *
 * // Export transactions to CSV
 * const csvData = await transactionService.fetchTransactions(
 *   { offeringId: 'offering123' },
 *   { page: 1, limit: 0 },
 *   true
 * );
 * ```
 */
class TransactionsService implements ITransactionsService {
  /**
   * Retrieves comprehensive transaction data with advanced filtering and analytics.
   * Provides detailed blockchain transaction history for tokenized offerings including
   * token operations, user information, and status tracking. Supports complex filtering,
   * search functionality, and export capabilities for administrative and compliance use.
   *
   * @async
   * @method fetchTransactions
   * @param {FilterQuery<any>} filters - Query filters for transaction selection and refinement
   * @param {IPagination} pagination - Pagination settings including page size, search, and navigation
   * @param {any} isCsv - Flag indicating CSV export format (bypasses pagination for full dataset)
   * @returns {Promise<PromiseResolve>} Promise resolving to transaction data with pagination metadata
   * @throws {Error} Throws error if transaction retrieval fails or invalid parameters provided
   * @memberof TransactionsService
   *
   * @description
   * Transaction retrieval features:
   * - Advanced MongoDB aggregation pipeline for complex data joins
   * - Multi-collection lookups (orders, users, offerings)
   * - Comprehensive filtering by status, type, and date ranges
   * - Real-time search across user names and email addresses
   * - Flexible sorting by multiple criteria (date, amount, status)
   * - CSV export support for reporting and compliance
   *
   * Filtering capabilities:
   * - Filter by offering ID for offering-specific transactions
   * - Status-based filtering (MINTED, REJECTED, FREEZE, UNFREEZE, CONVERT)
   * - Order type filtering for specific transaction categories
   * - Exclude pending and cancelled transactions for clean reporting
   * - Date-based sorting for chronological analysis
   *
   * Data aggregation includes:
   * - Complete user information (name, email, profile image)
   * - Transaction details (amounts, quantities, prices)
   * - Blockchain information (transaction hashes, block data)
   * - Status and timestamp tracking for audit trails
   * - Order type and operation classification
   *
   * Search functionality:
   * - Real-time search across user names (case-insensitive)
   * - Email address search for user identification
   * - Partial matching with comprehensive result filtering
   * - Search result highlighting and relevance scoring
   *
   * Performance optimizations:
   * - Efficient aggregation pipeline design
   * - Strategic indexing for fast query execution
   * - Pagination optimization for large datasets
   * - Memory-efficient CSV export processing
   *
   * // Search transactions by user email
   * const userTransactions = await transactionService.fetchTransactions(
   *   { offeringId: '507f1f77bcf86cd799439011' },
   *   {
   *     page: 1,
   *     limit: 10,
   *
   * // Get freeze/unfreeze operations
   * const freezeTransactions = await transactionService.fetchTransactions(
   *   {
   *     offeringId: '507f1f77bcf86cd799439011',
   *     status: 'FREEZE'
   *   },
   *   { page: 1, limit: 50 },
   *   false
   * );
   *
   * // Export all transactions to CSV
   * const csvExport = await transactionService.fetchTransactions(
   *   { offeringId: '507f1f77bcf86cd799439011' },
   *   { page: 1, limit: 0 }, // Limit ignored for CSV
   *   true // CSV export format
   * );
   *
   * // Get rejected transactions for analysis
   * const rejectedTransactions = await transactionService.fetchTransactions(
   *   {
   *     offeringId: '507f1f77bcf86cd799439011',
   *     status: 'REJECTED'
   *   },
   *   { page: 1, limit: 25 },
   *   false
   * );
   * ```
   *
   * const result = await transactionService.fetchTransactions(
   *   complexFilter,
   *   {
   *     page: 2,
   *     limit: 15,
   *     search: 'investment' // Search in user names
   *   },
   *   false
   * );
   *
   * // Response structure:
   * {
   *   status: 200,
   *   error: false,
   *   message: "Records fetched successfully",
   *   data: {
   *     transactions: [
   *       {
   *         _id: "507f1f77bcf86cd799439012",
   *         offeringId: "507f1f77bcf86cd799439011",
   *         userId: "507f1f77bcf86cd799439013",
   *         name: "John Investment LLC",
   *         email: "<EMAIL>",
   *         userImage: "profile.jpg",
   *         status: "MINTED",
   *         orderType: "MINTED",
   *         quantity: "1000",
   *         amount: "50000.00",
   *         price: "50.00",
   *         txHash: "0xabc123...",
   *         mintTxHash: "0xdef456...",
   *         orderMinted: "2024-01-15T12:00:00.000Z",
   *         createdAt: "2024-01-10T10:00:00.000Z",
   *         updatedAt: "2024-01-15T12:05:00.000Z"
   *       }
   *     ],
   *     currentPage: 2,
   *     totalPages: 8,
   *     totalCount: 115,
   *     nextPage: 3,
   *     previousPage: 1
   *   }
   * }
   * ```
   */
  fetchTransactions = async (filters: FilterQuery<any>, pagination: IPagination, isCsv: any): Promise<PromiseResolve> => {
    try {
      const { page = 1, limit = 10, search } = pagination;
      const skip = (page - 1) * limit;
      const match: any = {
        offeringId: filters.offeringId,
        status: { $nin: ['PENDING', 'APPROVED', 'CANCELLED'] },
      };
      const sort = filters.status === 'MINTED' ? { orderMinted: -1 } : { updatedAt: -1 };
      // Handling orderType condition
      if (filters?.status === 'MINTED' || filters?.status === 'REJECTED') {
        match.status = { $eq: filters.status.toUpperCase() };
      } else if (filters?.status) {
        match.orderType = { $eq: filters.status.toUpperCase() };
      } else {
        match.orderType = { $in: ['MINTED', 'FREEZE', 'UNFREEZE', 'CONVERT'] };
      }

      const pipeline: any = [
        { $match: match },
        {
          $lookup: { from: 'users', localField: 'userId', foreignField: '_id', as: 'userDetails' },
        },
        {
          $unwind: { path: '$userDetails', preserveNullAndEmptyArrays: true },
        },
        {
          $addFields: { name: '$userDetails.name', email: '$userDetails.email', userImage: '$userDetails.userImage' },
        },
        {
          $project: { userDetails: 0 },
        },
        {
          $facet: {
            metadata: [
              {
                $count: 'totalCount',
              },
            ],
            orders: isCsv
              ? []
              : [
                  { $skip: skip },
                  { $limit: limit },
                  {
                    $sort: sort,
                  },
                ],
          },
        },
      ];
      const results = await OrderSchema.aggregate(pipeline);
      const metadata = results[0]?.metadata[0] || { totalCount: 0 };
      const orders = results[0]?.orders || [];
      const { totalCount } = metadata;
      const totalPages = Math.ceil(totalCount / limit);
      const nextPage = page < totalPages ? page + 1 : null;
      const previousPage = page > 1 ? page - 1 : null;

      if (search) {
        const filteredOrders = orders.filter((order: any) => {
          return order.name.toLowerCase().includes(search.toLowerCase()) || order.email.toLowerCase().includes(search.toLowerCase());
        });

        return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.COMMON.RECORD_FETCH, data: { transactions: filteredOrders, currentPage: page, totalPages, totalCount, nextPage, previousPage } };
      }

      return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.COMMON.RECORD_FETCH, data: { transactions: orders, currentPage: page, totalPages, totalCount, nextPage, previousPage } };
    } catch (error) {
      logger.error(error, 'fetchTransactions error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };
}

/**
 * Singleton instance of TransactionsService for application-wide use.
 * Provides a shared service instance for transaction management across the platform.
 * Automatically initialized and ready for immediate use upon import.
 *
 * @exports {TransactionsService} transactionService - Singleton transaction service instance
 *
 * // Service is ready to use immediately
 * const transactions = await transactionService.fetchTransactions(filters, pagination, false);
 * const csvData = await transactionService.fetchTransactions(filters, {}, true);
 * ```
 */
export default new TransactionsService();
