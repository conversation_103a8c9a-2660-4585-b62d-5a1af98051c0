import { JoiValidationResult } from '../../utils/common.interface';
import * as joiOptions from '../../helpers/joi.helper';
import { RES_MSG, RESPONSES } from '../../utils/responseUtils';
import logger from '../../helpers/logging/logger.helper';
import * as Jo<PERSON> from 'joi';

class TransactionsValidation {
  /**
   * Convert Transactions Validate .
   * @returns {Promise<JoiValidationResult>}
   */
  async convertValidation(params: Record<string, any>): Promise<JoiValidationResult> {
    try {
      const schema = joiOptions.paginationSchema;
      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'convertValidation Error');

      return { error: true, value: '', message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR };
    }
  }

  /**
   * Transactions list Validate .
   * @returns {Promise<JoiValidationResult>}
   */
  async transactionsValidation(params: Record<string, any>): Promise<JoiValidationResult> {
    try {
      const schema = joiOptions.paginationSchema.keys({ status: joiOptions.offeringStatusSchema, type: joiOptions.transactionsTypeSchema, isCsv: Joi.boolean().default(false).optional() }).unknown(false); // Disallow any keys not explicitly defined in the schema

      const { error, value } = schema.validate(params, joiOptions.options);

      if (error) {
        return {
          error: true,
          value: '',
          message: error.details[0].message, // Provide specific error message
          status: RESPONSES.BAD_REQUEST,
        };
      }

      return {
        error: false,
        value, // Return the validated value
      };
    } catch (error) {
      logger.error(error, 'transactionsValidation Error');

      return {
        error: true,
        value: '',
        message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, // General error message
        status: RESPONSES.INTERNAL_SERVER_ERROR,
      };
    }
  }
}

export default new TransactionsValidation();
