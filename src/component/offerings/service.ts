/* eslint-disable no-unsafe-optional-chaining */
/* eslint-disable guard-for-in */
/* eslint-disable no-restricted-syntax */
/* eslint-disable no-return-assign */
import { FilterQuery } from 'mongoose';
import * as moment from 'moment';
import { ethers } from 'ethers';
import mongoose from 'mongoose';
import { Types } from 'mongoose';
import { IOfferingsService, CountryData } from './interface';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import { AssetTypeEnum, offeringStatusEnum, orderStatusEnum, PromiseResolve, queueMessageTypeEnum, whiteListEnum, IPagination } from '../../utils/common.interface';
import logger from '../../helpers/logging/logger.helper';
import { IOffering, offeringSchema, IUpdateOffering } from './models/offerings.model';
import kafkaService from '../../service/kafkaService';
import { requestedOfferingSchema, IRequestedOffering } from './models/requestedOfferings.model';
import { IUp<PERSON><PERSON><PERSON><PERSON>st, IWhitelist, whitelistSchema } from './models/whitelist.model';
import emailHelper from '../../helpers/email.helper';
import { userSchema } from '../userAuthentications/models/user.model';
import CommonHelper from '../../helpers/common.helper';
import { OrderSchema } from '../order/models/order.model';
import CustomError from '../../helpers/customError.helper';
import CONFIG from '../../config/env';
import realStateAbi from '../../utils/abi/realStateFundImplementation';
import equityAbi from '../../utils/abi/equityFundImplementation';
import { navHistorySchema } from './models/navHistory.model';
import { calculate } from '../../helpers/bigMath';
import UserService from '../userAuthentications/service';
import { socketHelper } from '../../helpers/socket.helper';
const adminEmail = process?.env?.ADMIN_EMAIL;

/**
 * Service class managing comprehensive asset tokenization and offering operations.
 * Handles offering lifecycle management, investor whitelisting, order processing, NAV tracking,
 * and regulatory compliance for tokenized investment opportunities. Integrates with blockchain
 * contracts, external APIs, and notification systems for complete offering management.
 *
 * @class OfferingService
 * @implements {IOfferingsService}
 * @description Complete asset tokenization platform service with offering management capabilities
 */
class OfferingService implements IOfferingsService {
  /**
   * Rejects or updates whitelist status for investor wallet addresses with comprehensive notification.
   * Manages investor access control, compliance requirements, and stakeholder communications.
   * Prevents duplicate status updates and ensures proper audit trail for regulatory compliance.
   *
   * @async
   * @method rejectWalletWhiteList
   * @param {Types.ObjectId} whitelistId - Unique identifier for the whitelist entry to update
   * @param {string} status - New status to set ('REJECTED', 'APPROVED', 'PENDING', 'UNDER_REVIEW')
   * @returns {Promise<PromiseResolve>} Promise resolving to status update confirmation with audit data
   * @throws {CustomError} Throws error for duplicate status updates or validation failures
   * @memberof OfferingService
   *
   * @description
   * Whitelist management process:
   * 1. Validates current whitelist status to prevent duplicate updates
   * 2. Updates whitelist status with new approval state
   * 3. Retrieves user and offering details for notifications
   * 4. Sends automated email notifications to affected investors
   * 5. Publishes status change events to Kafka for system integration
   * 6. Triggers real-time socket notifications for immediate UI updates
   * 7. Maintains comprehensive audit trail for compliance
   *
   * Status validation rules:
   * - Cannot change already APPROVED status
   * - Cannot set duplicate status values
   * - Must maintain regulatory compliance requirements
   * - Tracks all status change history
   *
   * Notification features:
   * - Automated email notifications with offering context
   * - Real-time socket updates for immediate feedback
   * - Admin notifications via Kafka messaging
   * - User-friendly status communication
   */
  rejectWalletWhiteList = async (whitelistId: Types.ObjectId, status: string): Promise<PromiseResolve> => {
    try {
      // Find the document to check the current status
      const existingWhitelist = await whitelistSchema.findById(whitelistId);
      if (existingWhitelist.status === 'APPROVED' || (existingWhitelist && existingWhitelist.status === status)) {
        throw new CustomError(`Status is already set to ${status.toUpperCase()}`, RESPONSES.CONFLICT);
      }
      // Proceed with updating the status to "REJECTED"
      const updateUserResp = await whitelistSchema.findByIdAndUpdate(whitelistId, { $set: { status } }, { new: true });
      const userId = updateUserResp?.userId;
      const offeringId = updateUserResp?.offeringId;
      const userDetails = await userSchema.findById({ _id: userId });
      const { name, email } = userDetails;

      const offeringDetails = await offeringSchema.findById({ _id: offeringId });
      const offeringName = offeringDetails?.projectDetails?.offeringName;
      const capitalizeFirstLetter = (str: string): string => {
        return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
      };
      const emailDetail: any = { name: capitalizeFirstLetter(name), offeringName };
      emailHelper.sendEmailTemplate(email, 'whiterequested', emailDetail);
      const finalData = JSON.parse(JSON.stringify({ _id: whitelistId, status: status }));
      await kafkaService.sendMessageToAdmin({ value: { ...finalData, type: queueMessageTypeEnum.REQ_WHITELIST } });

      // emit socket

      socketHelper.sendMessageToUser(userId.toString(), 'reject-whitelist', updateUserResp);

      return { status: RESPONSES.SUCCESS, error: false, message: 'Status updated for the provided offering Id !', data: null };
    } catch (error) {
      logger.error('rejectWalletWhiteList', error);
      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * Retrieves paginated order data for specific offerings with comprehensive filtering and analytics.
   * Provides detailed order information including user details, token metrics, payment status,
   * and transaction history for offering management and regulatory reporting.
   *
   * @async
   * @method fetchOrdersFromOffering
   * @param {string} userId - ID of the offering owner (issuer) requesting order data
   * @param {string} offeringId - ID of the specific offering to retrieve orders for
   * @param {number} page - Page number for pagination (1-based indexing)
   * @param {number} limit - Number of orders to return per page
   * @param {string} status - Order status filter ('PENDING', 'APPROVED', 'REJECTED', 'MINTED')
   * @param {string} searchQuery - Search term for filtering by name, email, or wallet address
   * @param {any} isCsv - Flag indicating if data is for CSV export (affects pagination)
   * @returns {Promise<PromiseResolve>} Promise resolving to paginated order data with metadata
   * @throws {Error} Throws error if order retrieval fails or validation errors occur
   * @memberof OfferingService
   *
   * @description
   * Order retrieval features:
   * - Advanced MongoDB aggregation for complex data joins
   * - Multi-collection data aggregation (orders, users, whitelists)
   * - Flexible filtering by status, user information, and wallet address
   * - Comprehensive pagination with metadata
   * - CSV export support with full dataset
   * - Token calculation and pricing information
   * - Transaction history and minting status
   * - User profile and whitelist integration
   *
   * Data aggregation includes:
   * - Order details with token quantities and amounts
   * - User information (name, email, profile image)
   * - Whitelist status and wallet addresses
   * - Fee calculations and pricing data
   * - Transaction hashes and minting status
   * - Multi-signature wallet information
   * - Payment method and order type details
   *
   * Search capabilities:
   * - User name matching (case-insensitive)
   * - Email address search
   * - Wallet address lookup
   * - Partial string matching with regex
   *
   */
  fetchOrdersFromOffering = async (userId: string, offeringId: string, page: number, limit: number, status: string, searchQuery: string, isCsv: any, invitedBy: any = []): Promise<PromiseResolve> => {
    try {
      const matchFilter: any = { userId: new Types.ObjectId(userId), _id: new Types.ObjectId(offeringId) };
      if (invitedBy && invitedBy.length > 0) {
        matchFilter.userId = { $in: invitedBy };
      }
      const skip = (page - 1) * limit;
      const orders4Offering = await offeringSchema.aggregate([
        { $match: matchFilter },
        { $lookup: { from: 'orders', localField: '_id', foreignField: 'offeringId', as: 'orders' } },
        { $unwind: { path: '$orders' } },
        {
          $match: {
            ...(status ? { 'orders.status': status?.toUpperCase() } : { 'orders.status': { $ne: 'PENDING' } }), // Exclude 'PENDING' if no status is provided
            'orders.orderType': 'MINTED',
          },
        },
        { $lookup: { from: 'users', localField: 'orders.userId', foreignField: '_id', as: 'result' } },
        { $unwind: { path: '$result' } },

        { $lookup: { from: 'multisigs', localField: 'userId', foreignField: 'issuerId', as: 'multisigDetails' } },
        { $unwind: { path: '$multisigDetails', preserveNullAndEmptyArrays: true } },

        { $lookup: { from: 'whitelists', localField: 'userId', foreignField: 'userId', as: 'whitelists' } },
        {
          $project: {
            _id: '$orders._id',
            name: '$result.name',
            email: '$result.email',
            token_to_be_minted: '$orders.quantity',
            token_to_be_received: '$orders.amount',
            isMinted: '$orders.isMinted',
            userImage: '$result.userImage',
            feesInPercentage: '$orders.feesInPercentage',
            status: '$orders.status',
            createdAt: '$orders.createdAt',
            orderMinted: '$orders.orderMinted',
            txHash: '$orders.txHash',
            mintTxHash: '$orders.mintTxHash',
            multisig: '$orders.multisig',
            multisigApproval: '$orders.multisigApproval',
            price: {
              $let: {
                vars: { roundedPrice: { $round: [{ $toDouble: '$orders.price' }, 2] }, priceString: { $toString: { $round: [{ $toDouble: '$orders.price' }, 2] } } },
                in: { $cond: [{ $regexMatch: { input: '$$priceString', regex: /\.\d$/ } }, { $concat: ['$$priceString', '0'] }, '$$priceString'] },
              },
            },
            tokenAddress: 1,
            token_decimal: '$projectDetails.tokenDecimals',
            walletAddress: '$orders.walletAddress',
            paymentMethod: '$orders.paymentMethod',
            user_address: { $first: '$whitelists.address' },
            multisigDetails: '$multisigDetails',
          },
        },
        { $sort: { 'orders.createdAt': -1 } },
        // ...(searchQuery ? [{ $match: { name: { $regex: searchQuery, $options: 'i' } } }] : []),
        ...(searchQuery
          ? [
              {
                $match: {
                  $or: [{ name: { $regex: searchQuery, $options: 'i' } }, { email: { $regex: searchQuery, $options: 'i' } }, { walletAddress: { $regex: searchQuery, $options: 'i' } }],
                },
              },
            ]
          : []),
        {
          $facet: {
            orders: isCsv ? [] : [{ $skip: skip }, { $limit: limit }], // Pagination for orders
            totalCount: [{ $count: 'count' }], // Get total count of documents
          },
        },
      ]);
      const totalCount = orders4Offering[0]?.totalCount[0]?.count || 0;
      const orders = orders4Offering[0]?.orders || [];
      const totalPages = Math.ceil(totalCount / limit);
      const nextPage = page < totalPages ? page + 1 : null;
      const previousPage = page > 1 ? page - 1 : null;

      return { status: RESPONSES.SUCCESS, error: false, message: 'Order details found successfully for the provided offering Id !', data: { orders, currentPage: page, totalPages, totalCount, nextPage, previousPage } };
    } catch (error) {
      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  fetchOrdersFromOfferingCsv = async (userId: string, offeringId: string, status: string): Promise<PromiseResolve> => {
    try {
      const orders4Offering = await offeringSchema.aggregate([
        { $match: { userId: new Types.ObjectId(userId), _id: new Types.ObjectId(offeringId) } },
        { $lookup: { from: 'orders', localField: '_id', foreignField: 'offeringId', as: 'orders' } },
        { $unwind: { path: '$orders' } },
        {
          $match: {
            ...(status ? { 'orders.status': status?.toUpperCase() } : { 'orders.status': { $ne: 'PENDING' } }), // Exclude 'PENDING' if no status is provided
            'orders.orderType': 'MINTED',
          },
        },
        { $lookup: { from: 'users', localField: 'orders.userId', foreignField: '_id', as: 'result' } },
        { $unwind: { path: '$result' } },
        { $lookup: { from: 'whitelists', localField: 'userId', foreignField: 'userId', as: 'whitelists' } },
        {
          $project: {
            _id: '$orders._id',
            name: '$result.name',
            email: '$result.email',
            token_to_be_minted: '$orders.quantity',
            token_to_be_received: '$orders.amount',
            isMinted: '$orders.isMinted',
            userImage: '$result.userImage',
            feesInPercentage: '$orders.feesInPercentage',
            status: '$orders.status',
            createdAt: '$orders.createdAt',
            txHash: '$orders.txHash',
            mintTxHash: '$orders.mintTxHash',
            price: {
              $let: {
                vars: { roundedPrice: { $round: [{ $toDouble: '$orders.price' }, 2] }, priceString: { $toString: { $round: [{ $toDouble: '$orders.price' }, 2] } } },
                in: { $cond: [{ $regexMatch: { input: '$$priceString', regex: /\.\d$/ } }, { $concat: ['$$priceString', '0'] }, '$$priceString'] },
              },
            },
            tokenAddress: 1,
            token_decimal: '$projectDetails.tokenDecimals',
            walletAddress: '$orders.walletAddress',
            paymentMethod: '$orders.paymentMethod',
            user_address: { $first: '$whitelists.address' },
          },
        },
        { $sort: { 'orders.createdAt': -1 } },
      ]);

      // Returning all orders without pagination
      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: 'Order details found successfully for the provided offering Id!',
        data: { orders: orders4Offering },
      };
    } catch (error) {
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  };

  /**
   * @param {IUpdateOffering} searchDetails
   * @param {IPagination} fields
   * @param {IPagination} excludeFields
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  fetchOfferingDetails = async (searchDetails: FilterQuery<IOffering>, fields?: string[], excludeFields?: string[], userId?: string): Promise<PromiseResolve> => {
    try {
      const projection: Record<string, any> = {};

      // Include specific fields if provided
      if (fields && fields.length > 0) {
        fields.forEach((field) => (projection[field] = 1));
      }

      // Exclude specific fields if provided
      if (excludeFields && excludeFields.length > 0) {
        excludeFields.forEach((field) => (projection[field] = 0));
      }
      const pipeline: any[] = [
        { $match: searchDetails },
        {
          $lookup: {
            from: 'dividendreqs',
            localField: '_id',
            foreignField: 'offeringId',
            as: 'dividends',
          },
        },
        {
          $lookup: {
            from: 'whitelists', // Lookup from the `whitelists` collection
            let: { offeringId: '$_id', userId: new Types.ObjectId(userId) },
            pipeline: [{ $match: { $expr: { $and: [{ $eq: ['$offeringId', '$$offeringId'] }, { $eq: ['$userId', '$$userId'] }] } } }],
            as: 'subscriptions',
          },
        },
        { $addFields: { isSubscribe: { $gt: [{ $size: '$subscriptions' }, 0] } } },
        { $unwind: { path: '$subscriptions', preserveNullAndEmptyArrays: true } },
        { $addFields: { isWhitelisted: '$subscriptions.status' } },
      ];

      if (Object.keys(projection).length > 0) {
        pipeline.push({ $project: projection });
      }
      const offeringDetails: IOffering[] = await offeringSchema.aggregate(pipeline).exec();
      if (offeringDetails.length > 0) {
        return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS, data: offeringDetails[0] };
      }

      return { status: RESPONSES.NOTFOUND, error: true, message: RES_MSG.COMMON.NO_OFFERING };
    } catch (error: any) {
      logger.error(error, 'fetchOfferingDetails error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * @param {I} data
   * @param {IOffering} filter
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */

  updateOfferingDetails = async (data: any, filter: FilterQuery<IOffering>): Promise<PromiseResolve> => {
    try {
      let updateUserResp;
      const updateData: any = {};
      if (data?.status == offeringStatusEnum.REJECTED) {
        const id = new mongoose.Types.ObjectId(filter._id);
        const offeringDetails: any = await offeringSchema.findOne({ _id: id });
        const _id = offeringDetails?.userId;
        // const status = offeringDetails?.status;
        const offeringName = offeringDetails?.projectDetails?.offeringName;
        const user: any = await UserService.fetchUserDetails({ _id });
        const { email, name }: any = user?.data;
        const emailDetail = {
          email,
          name,
          offeringId: filter._id,
          date: new Date().toLocaleString('en-US', { timeZone: 'UTC' }),
          reason: data?.reason,
          offeringName,
        };
        emailHelper.sendEmailTemplate(email, 'offeringDeployedRejected', emailDetail);
      }
      // Set additional flags based on specific conditions
      if (data.tokenAddress?.length === 42) data.isTokenDeploy = true;
      if (data.fundAddress?.length === 42) {
        data.isFundDeploy = true;
        data.status = offeringStatusEnum.APPROVED;
        const id = new mongoose.Types.ObjectId(filter._id);
        const offeringDetails: any = await offeringSchema.findOne({ _id: id });
        const _id = offeringDetails?.userId;
        // const date = offeringDetails?.updatedAt;
        const status = offeringDetails?.status;
        const offeringName = offeringDetails?.projectDetails?.offeringName;
        const tokenAddres = offeringDetails?.tokenAddress;
        const user: any = await UserService.fetchUserDetails({ _id });
        const { email, name }: any = user?.data;
        if (status == offeringStatusEnum.PENDING) {
          const emailDetail = {
            email,
            name,
            offeringId: filter._id,
            date: new Date().toLocaleString('en-US', { timeZone: 'UTC' }),
            offeringName,
            fundAddress: data.fundAddress,
            tokenAddres,
          };
          emailHelper.sendEmailTemplate(email, 'offeringDeployed', emailDetail);
        }
      }

      if (data.event === 'WrapTokenCreated') {
        data.iserc20 = true;
        updateUserResp = await offeringSchema.findOneAndUpdate({ tokenAddress: data.tokenAddress }, data, { new: true, runValidators: true });

        // emit socket for wrap token created to investor or issuer
        socketHelper.sendMessageToUser(updateUserResp.userId.toString(), 'wrap-token-created', updateUserResp);
      } else {
        for (const key in data) {
          if (typeof data[key] === 'object' && !Array.isArray(data[key])) {
            for (const nestedKey in data[key]) {
              updateData[`${key}.${nestedKey}`] = data[key][nestedKey];
            }
          } else {
            updateData[key] = data[key];
          }
        }
        updateUserResp = await offeringSchema.findOneAndUpdate(filter, { $set: updateData }, { new: true });

        //emit socket for all users in case of VALUATION changed by issuer
        socketHelper.broadcast('valuation-updated', updateUserResp);
      }

      if (updateUserResp) {
        if (updateUserResp.isFinalSubmission) {
          const finalData = JSON.parse(JSON.stringify(updateUserResp));
          await kafkaService.sendMessageToAdmin({ value: { ...finalData, type: queueMessageTypeEnum.OFFERING } });
        }

        // emit socket to issuer for offering deployed
        socketHelper.sendMessageToUser(updateUserResp.userId.toString(), 'offering-minted', updateUserResp);

        return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.USER.USER_UPDATION_SUCCESS, data: updateUserResp };
      }
      if (data?.userId !== data?.createdBy) {
        this.requestOffering({ status: data?.status, _id: data?._id });
      }

      return { status: RESPONSES.BAD_REQUEST, error: true, message: RES_MSG.ERROR_MSG.DATA_FETCH_ERROR };
    } catch (error) {
      logger.error(error, 'updateOfferingDetails error');

      if (error.code === 11000) {
        if (error.keyPattern?.['projectDetails.CUSIP']) {
          return { status: RESPONSES.BAD_REQUEST, error: true, message: RES_MSG.ERROR_MSG.CUSIP_ERR };
        }
        if (error.keyPattern?.['projectDetails.tokenTicker']) {
          return { status: RESPONSES.BAD_REQUEST, error: true, message: RES_MSG.ERROR_MSG.TOKEN_TICKER_ERR };
        }
      }

      return { status: RESPONSES.BAD_REQUEST, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * @param {I} data
   * @param {IOffering} filter
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  updateWhitelistDetails = async (data: IUpdateWhitelist, filter: FilterQuery<IOffering>): Promise<PromiseResolve> => {
    try {
      data.status = whiteListEnum.APPROVED;

      const updateUserResp = await whitelistSchema.findOneAndUpdate(filter, data, {
        new: true,
        runValidators: true,
      });
      // emit socket for WHITELIST WALLET
      socketHelper.sendMessageToSingleUser(updateUserResp?.userId.toString(), 'wallet-whitelist', updateUserResp);

      if (!updateUserResp) {
        return {
          status: RESPONSES.BAD_REQUEST,
          error: true,
          message: RES_MSG.ERROR_MSG.USER_UPDATION_ERROR,
        };
      }

      const { userId, offeringId, address } = updateUserResp;
      const userDetails = await userSchema.findById(userId);
      const offeringDetails = await offeringSchema.findById(offeringId);

      // emit socket for issuer
      const issuerId = offeringDetails.userId;
      socketHelper.sendMessageToSingleUser(issuerId.toString(), 'wallet-whitelist-issuer', updateUserResp);

      if (!userDetails || !offeringDetails) {
        return {
          status: RESPONSES.BAD_REQUEST,
          error: true,
          message: RES_MSG.ERROR_MSG.USER_UPDATION_ERROR,
        };
      }

      const { name, email } = userDetails;
      const { projectDetails: { offeringName } = {} } = offeringDetails;

      const capitalizeFirstLetter = (str: string): string => str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();

      const finalData = JSON.parse(JSON.stringify(updateUserResp));
      await kafkaService.sendMessageToAdmin({
        value: JSON.stringify({ ...finalData, type: queueMessageTypeEnum.WHITELIST }),
      });

      emailHelper.sendEmailTemplate(email, 'walletWhitelist', {
        name: capitalizeFirstLetter(name),
        offeringName,
      });

      const initialOrder = await OrderSchema.create({
        userId,
        offeringId,
        price: '0',
        currentPrice: '0',
        quantity: '0',
        walletAddress: address?.toLowerCase(),
        orderType: orderStatusEnum.INITIAL_ORDER,
        status: orderStatusEnum.INITIAL_ORDER,
        txHash: `${offeringId}-${userId}`,
      });

      const finalInitialOrderData = JSON.parse(JSON.stringify(initialOrder));

      await kafkaService.sendMessageToAdmin({ value: { ...finalInitialOrderData, type: queueMessageTypeEnum.ORDER } });

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USER_UPDATION_SUCCESS,
        data: updateUserResp,
      };
    } catch (error) {
      logger.error(error, 'updateWhitelistDetails error');

      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  };

  /**
   * @param {I} data
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  createOffering = async (data: IUpdateOffering): Promise<PromiseResolve> => {
    try {
      const query = await offeringSchema.create(data);
      if (query) {
        // sending offering to notification service
        // kafkaService.sendMessageToAdmin()
        return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.USER.CREATED_SUCCESS, data: query };
      }

      return { status: RESPONSES.BAD_REQUEST, error: true, message: RES_MSG.ERROR_MSG.USER_UPDATION_ERROR };
    } catch (error) {
      logger.error(error, 'createOffering error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * @param {IUpdateOffering} filters
   * @param {any[]} projection
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  fetchOfferingList = async (filters: IUpdateOffering | any, projection: any[], pagination: IPagination, isCsv: any): Promise<PromiseResolve> => {
    try {
      const { page = 1, limit = 10, sort = { createdAt: -1 }, search } = pagination;
      const skip = (page - 1) * limit;
      const query: FilterQuery<any> = {};

      const addFilter = (key: keyof IUpdateOffering, value: any, operator: string = '$eq') => {
        if (typeof value !== 'undefined') {
          if (operator === '$in') {
            query[key] = { $in: value };
          } else {
            query[key] = value;
          }
        }
      };

      if (search) {
        query.$or = [
          { 'overview.title': { $regex: search, $options: 'i' } },
          { 'overview.subTitle': { $regex: search, $options: 'i' } },
          { 'projectDetails.CUSIP': { $regex: search, $options: 'i' } },
          { 'projectDetails.assetName': { $regex: search, $options: 'i' } },
          { 'projectDetails.offeringName': { $regex: search, $options: 'i' } },
          { 'projectDetails.tokenTicker': { $regex: search, $options: 'i' } },
        ];
      }

      addFilter('isActive', true);
      addFilter('status', filters?.status?.toUpperCase());
      // addFilter('userId', filters.userId);
      if (filters.invitedBy && filters.invitedBy.length > 0) {
        addFilter('userId', filters.invitedBy, '$in'); // 👈 invitedBy filter (Array)
      } else {
        addFilter('userId', filters.userId); // 👈 userId filter (Single ID)
      }
      addFilter('isDelete', filters?.isDelete ? filters?.isDelete : false);
      if (filters?.iserc20) addFilter('iserc20', true);

      /* const totalCount = await offeringSchema.countDocuments(query).exec();
      let users;
      if (isCsv) {
        users = await offeringSchema.find(query).select(projection).sort(sort).exec();
      } else {
        users = await offeringSchema.find(query).select(projection).sort(sort).skip(skip).limit(limit).exec();
      }
      const totalPages = Math.ceil(totalCount / limit); */

      const totalCount = await offeringSchema.countDocuments(query).exec();
      const totalPages = Math.ceil(totalCount / limit);

      // Aggregation pipeline
      const aggregationPipeline: any[] = [
        { $match: query },
        {
          $lookup: {
            from: 'multisigs', // Make sure this matches your actual collection name
            localField: 'userId',
            foreignField: 'issuerId',
            as: 'multisigDetails',
          },
        },
        { $unwind: { path: '$multisigDetails', preserveNullAndEmptyArrays: true } },
        { $sort: sort },
      ];
      // Apply projection (only specific fields to return)
      if (projection?.length && projection[0] != '') {
        console.log({ projection });
        aggregationPipeline.push({ $project: projection.reduce((acc, key) => ({ ...acc, [key]: 1 }), {}) });
      }
      // Apply pagination if not CSV
      if (!isCsv) {
        aggregationPipeline.push({ $skip: skip });
        aggregationPipeline.push({ $limit: limit });
      }
      // Execute aggregation
      const users = await offeringSchema.aggregate(aggregationPipeline).exec();

      return {
        data: { offering: users, currentPage: page, totalPages, totalCount, nextPage: page < totalPages ? page + 1 : null, previousPage: page > 1 ? page - 1 : null },
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
      };
    } catch (error) {
      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * @param {IOffering} filters
   * @param {any[]} projection
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  fetchNavHistory = async (filters: any, projection: any[], pagination: IPagination): Promise<PromiseResolve> => {
    try {
      const { page = 1, limit = 10, sort = { createdAt: -1 } } = pagination;
      const skip = (page - 1) * limit;
      const totalCount = await navHistorySchema.countDocuments(filters).exec();
      const users = await navHistorySchema.find(filters).sort(sort).skip(skip).limit(limit).exec();
      const totalPages = Math.ceil(totalCount / limit);

      return {
        data: { offering: users, currentPage: page, totalPages, totalCount, nextPage: page < totalPages ? page + 1 : null, previousPage: page > 1 ? page - 1 : null },
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
      };
    } catch (error) {
      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * @param {IUpdateOffering} searchDetails
   * @param {IPagination} pagination
   * @param {string} type
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  getTopOffering = async (searchDetails: IUpdateOffering, pagination: IPagination, type: string): Promise<PromiseResolve> => {
    try {
      const page = pagination.page || 1;
      const limit = pagination.limit || 5;
      const skip = (page - 1) * limit;

      const pipeline: any =
        type === 'AUM'
          ? [
              { $match: { userId: searchDetails.userId, status: 'APPROVED' } },
              {
                $lookup: {
                  from: 'orders',
                  localField: '_id',
                  foreignField: 'offeringId',
                  as: 'orders',
                  pipeline: [{ $match: { isMinted: true } }],
                },
              },
              {
                $addFields: {
                  totalAmount: {
                    $sum: {
                      $map: {
                        input: '$orders',
                        as: 'order',
                        in: { $toDecimal: '$$order.amount' },
                      },
                    },
                  },
                  totalQuantity: {
                    $sum: {
                      $map: {
                        input: '$orders',
                        as: 'order',
                        in: { $toDecimal: '$$order.quantity' },
                      },
                    },
                  },
                },
              },
              {
                $match: {
                  $or: [{ orders: { $not: { $size: 0 } } }, { orders: { $exists: false } }],
                },
              },
              {
                $project: {
                  totalQuantity: { $toString: '$totalQuantity' },
                  totalAmount: { $toString: '$totalAmount' },
                  title: '$overview.title',
                  subTitle: '$overview.subTitle',
                  icon: '$overview.icon',
                  offeringName: '$projectDetails.offeringName',
                  tokenSupply: '$projectDetails.tokenSupply',
                },
              },
              { $sort: { totalAmount: -1 } },
              { $skip: 0 },
              { $limit: 5 },
            ]
          : [
              { $match: { userId: searchDetails.userId, status: 'APPROVED' } },
              { $lookup: { from: 'orders', localField: '_id', foreignField: 'offeringId', as: 'orders', pipeline: [{ $match: { isMinted: true } }] } },
              {
                $project: {
                  _id: 1,
                  title: '$overview.title',
                  subTitle: '$overview.subTitle',
                  icon: '$overview.icon',
                  offeringName: '$projectDetails.offeringName',
                  tokenSupply: '$projectDetails.tokenSupply',
                  userIds: { $map: { input: '$orders', as: 'order', in: '$$order.userId' } },
                },
              },
              { $addFields: { investorCount: { $size: { $setUnion: '$userIds' } } } },
              {
                $group: {
                  _id: null,
                  totalInvestorCount: { $sum: '$investorCount' },
                  offerings: { $push: { _id: '$_id', title: '$title', subTitle: '$subTitle', icon: '$icon', offeringName: '$offeringName', tokenSupply: '$tokenSupply', investorCount: '$investorCount' } },
                },
              },
              { $unwind: '$offerings' },
              { $addFields: { 'offerings.investorPercentage': { $cond: { if: { $eq: ['$totalInvestorCount', 0] }, then: 0, else: { $multiply: [{ $divide: ['$offerings.investorCount', '$totalInvestorCount'] }, 100] } } } } },
              { $sort: { 'offerings.investorCount': -1 } },
              { $group: { _id: null, totalInvestorCount: { $first: '$totalInvestorCount' }, topInvestedOffering: { $push: '$offerings' } } },
              { $project: { _id: 0, totalInvestorCount: '$totalInvestorCount', topInvestedOffering: { $slice: ['$topInvestedOffering', skip, limit] } } },
            ];
      const result = await offeringSchema.aggregate(pipeline);
      const totalCount = await offeringSchema.countDocuments({ userId: searchDetails.userId, status: 'APPROVED' });
      const totalPages = Math.ceil(totalCount / limit);
      const currentPage = page;
      const nextPage = currentPage < totalPages ? currentPage + 1 : null;
      const previousPage = currentPage > 1 ? currentPage - 1 : null;

      return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.COMMON.RECORD_FETCH, data: { topOffering: result, currentPage, totalPages, totalCount, nextPage, previousPage } };
    } catch (error) {
      logger.error(error, 'getTopOffering');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * @param {IOffering} filters
   * @param {any[]} projection
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  offeringList = async (filters: any, projection: any[], pagination: IPagination): Promise<PromiseResolve> => {
    try {
      const { page = 1, limit = 10, sort = { deployedDate: -1 }, search: escapedSearch } = pagination;
      const skip = (page - 1) * limit;
      const query: FilterQuery<any> = {};
      const search = escapedSearch ? CommonHelper.escapeRegex(escapedSearch) : null;
      const addFilter = (key: keyof IOffering, value: any) => {
        if (typeof value !== 'undefined') query[key] = value;
      };

      query.userId = { $ne: filters.userId };
      addFilter('isActive', true);
      addFilter('isDelete', false);
      addFilter('status', offeringStatusEnum.APPROVED);

      if (search) {
        query.$or = [{ 'projectDetails.offeringName': { $regex: search, $options: 'i' } }, { 'projectDetails.assetName': { $regex: search, $options: 'i' } }, { 'projectDetails.tokenTicker': { $regex: search, $options: 'i' } }];
      }
      if (filters.assetType) {
        query.$and = [{ 'projectDetails.assetType': { $regex: filters.assetType, $options: 'i' } }];
      }
      if (filters.status) query.status = filters.status;
      if (filters.lineOfBusiness) query['overview.lineOfBusiness'] = filters.lineOfBusiness;
      if (filters.minInvestment) query['projectDetails.minInvestment'] = { $gte: filters.minInvestment };
      if (filters.maxInvestment) query['projectDetails.maxInvestment'] = { $lte: filters.maxInvestment };
      if (filters.teamMember) query['team.name'] = { $regex: filters.teamMember, $options: 'i' };

      if (filters.projectedYield) {
        sort['projectDetails.projectedYield'] = filters.projectedYield === 'asc' ? 1 : -1;
        delete sort.deployedDate;
      }
      const projectionObject = projection.reduce((acc: any, field: string) => ({ ...acc, [field]: 1 }), {});
      const pipeline = [
        { $match: query },
        { $lookup: { from: 'whitelists', let: { offeringId: '$_id', userId: filters.userId }, pipeline: [{ $match: { $expr: { $and: [{ $eq: ['$offeringId', '$$offeringId'] }, { $eq: ['$userId', '$$userId'] }] } } }], as: 'subscriptions' } },
        {
          $addFields: {
            isSubscribe: { $gt: [{ $size: '$subscriptions' }, 0] },
            isWhitelisted: { $anyElementTrue: { $map: { input: '$subscriptions', as: 'sub', in: { $eq: ['$$sub.status', offeringStatusEnum.APPROVED] } } } },
            isRejected: { $anyElementTrue: { $map: { input: '$subscriptions', as: 'sub', in: { $eq: ['$$sub.status', offeringStatusEnum.REJECTED] } } } },
          },
        },
        {
          $addFields: {
            emailInOfferingMembers: {
              $cond: [
                { $and: [{ $eq: ['$projectDetails.isPrivate', true] }] }, // Check if isPrivate is true
                { $in: [filters.email, '$projectDetails.offeringMembers'] }, // Check if email is in offeringMembers
                true, // Default for public offerings
              ],
            },
          },
        },
        {
          $match: {
            emailInOfferingMembers: true, // Match only those records where email exists in offeringMembers
          },
        },

        { $project: { ...projectionObject, createdAt: 1, subId: '$subscriptions._id', subStatus: '$subscriptions.status' } },
        // Conditionally unwind subId and subStatus only if they are arrays
        { $addFields: { subIdExists: { $isArray: '$subId' }, subStatusExists: { $isArray: '$subStatus' } } },
        {
          $project: {
            subId: { $cond: { if: { $eq: ['$subIdExists', true] }, then: '$subId', else: [] } },
            subStatus: { $cond: { if: { $eq: ['$subStatusExists', true] }, then: '$subStatus', else: [] } },
            ...projectionObject, // Keep your other fields intact
            createdAt: 1,
          },
        },
        {
          $unwind: {
            path: '$subId',
            preserveNullAndEmptyArrays: true, // Ensure null values are preserved if unwinding is not applicable
          },
        },
        {
          $unwind: {
            path: '$subStatus',
            preserveNullAndEmptyArrays: true, // Ensure null values are preserved if unwinding is not applicable
          },
        },
        { $sort: sort },
        { $skip: skip },
        { $limit: limit },
      ];
      const totalCount = await offeringSchema.countDocuments(query).exec();
      const users = await offeringSchema.aggregate(pipeline).exec();
      const totalPages = Math.ceil(totalCount / limit);

      return {
        data: { offering: users, currentPage: page, totalPages, totalCount, nextPage: page < totalPages ? page + 1 : null, previousPage: page > 1 ? page - 1 : null },
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
      };
    } catch (error) {
      logger.error(error, 'offeringList');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * @param {I} data
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  requestOffering = async (data: IRequestedOffering): Promise<PromiseResolve> => {
    try {
      const search = { _id: new Types.ObjectId(data?._id) };
      const isReqPending = await requestedOfferingSchema.findOne({ userId: data.userId, status: orderStatusEnum.PENDING });
      if (isReqPending) {
        throw new CustomError('Offering Request already pending', RESPONSES.CONFLICT);
      }
      const query = await requestedOfferingSchema.findOneAndUpdate(search, data, { new: true, runValidators: true, upsert: true });
      if (query) {
        const finalData = JSON.parse(JSON.stringify(query));
        const { name } = await userSchema.findOne({ _id: finalData.userId });
        const detail = {
          name,
          date: new Date().toLocaleString('en-US', { timeZone: 'UTC' }),
        };

        emailHelper.sendEmailTemplate(adminEmail, 'assignToAdmin', detail);
        await kafkaService.sendMessageToAdmin({ value: { ...finalData, type: queueMessageTypeEnum.REQ_OFFERING } });

        return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.USER.CREATED_SUCCESS, data: query };
      }

      return { status: RESPONSES.BAD_REQUEST, error: true, message: RES_MSG.ERROR_MSG.USER_UPDATION_ERROR };
    } catch (error) {
      logger.error(error, 'requestOffering error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * @param {I} data
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  requestedOfferings = async (filters: any, pagination: IPagination): Promise<PromiseResolve> => {
    try {
      const {
        page = 1,
        limit = 10,
        sort = { createdAt: -1 },
        // search,
      } = pagination;
      const skip = (page - 1) * limit;
      const query: FilterQuery<any> = {};

      // const addFilter = (key: keyof IRequestedOffering, value: any) => {
      //     if (typeof value !== 'undefined') query[key] = value;
      // };

      if (filters.status) query.status = filters.status;
      if (filters.userId) query.userId = filters.userId;
      const pipeline = [{ $match: query }, { $sort: sort }, { $skip: skip }, { $limit: limit }];
      const totalCount = await requestedOfferingSchema.countDocuments(query).exec();
      const users = await requestedOfferingSchema.aggregate(pipeline).exec();
      const totalPages = Math.ceil(totalCount / limit);

      return {
        data: { offering: users, currentPage: page, totalPages, totalCount, nextPage: page < totalPages ? page + 1 : null, previousPage: page > 1 ? page - 1 : null },
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
      };
    } catch (error) {
      logger.error(error, 'requestedOffering error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * @param {IUpdateWhitelist} data
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  createWhitelist = async (data: IUpdateWhitelist): Promise<PromiseResolve> => {
    try {
      const query = await whitelistSchema.create(data);
      if (query) {
        const finalData = JSON.parse(JSON.stringify(query));
        await kafkaService.sendMessageToAdmin({ value: { ...finalData, type: queueMessageTypeEnum.REQ_WHITELIST } });

        return { status: RESPONSES.CREATED, error: false, message: RES_MSG.USER.CREATED_SUCCESS, data: query };
      }

      return { status: RESPONSES.BAD_REQUEST, error: true, message: RES_MSG.ERROR_MSG.USER_UPDATION_ERROR };
    } catch (error) {
      logger.error(error, 'createWhitelist error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * @param {IWhitelist} searchDetails
   * @param {IWhitelist} fields
   * @param {IWhitelist} excludeFields
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  fetchSubscriberDetails = async (searchDetails: FilterQuery<IWhitelist>, fields?: string[], excludeFields?: string[]): Promise<PromiseResolve> => {
    try {
      const query = whitelistSchema.findOne(searchDetails);
      if (fields && fields.length > 0) {
        const fieldsString = fields.join(' ');
        query.select(fieldsString);
      }

      if (excludeFields && excludeFields.length > 0) {
        const excludeFieldsString = excludeFields.map((field) => `-${field}`).join(' ');
        query.select(excludeFieldsString);
      }

      const whitelist: IWhitelist | null = await query.exec();

      if (whitelist) {
        return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS, data: whitelist.toObject() };
      }

      return { status: RESPONSES.NOTFOUND, error: true, message: RES_MSG.COMMON.NO_RECORD };
    } catch (error) {
      logger.error(error, 'fetchSubscriberDetails error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**

     * @param {IOffering} filters
     * @param {IPagination} pagination
     * @returns {Promise<PromiseResolve>}
     * @memberof OfferingService
     */
  getOfferingSubscriberList = async (filters: any, pagination: IPagination, isCsv: any): Promise<PromiseResolve> => {
    try {
      const { page = 1, limit = 10, sort = { createdAt: -1 }, search: escapedSearch } = pagination;
      const skip = (page - 1) * limit;
      const search = escapedSearch ? CommonHelper.escapeRegex(escapedSearch) : null;
      const query: FilterQuery<any> = {};
      const isCsvBool = isCsv == 'true';

      // addFilter update for use in operator for invitedBy
      const addFilter = (key: keyof IOffering, value: any, operator: string = '$eq') => {
        if (typeof value !== 'undefined') {
          if (operator === '$in') {
            query[key] = { $in: value };
          } else {
            query[key] = value;
          }
        }
      };

      // Add standard filters
      addFilter('status', filters.status);
      // Conditionally apply userId or invitedBy as a `$in` clause
      if (filters.invitedBy && filters.invitedBy.length > 0) {
        addFilter('userId', filters.invitedBy, '$in'); // 👈 invitedBy filter (Array)
      } else {
        addFilter('userId', filters.userId); // 👈 userId filter (Single ID)
      }
      addFilter('_id', filters.offeringId);
      addFilter('isDelete', false);

      const aggregationPipeline = [
        { $match: query },
        { $lookup: { from: 'whitelists', localField: '_id', foreignField: 'offeringId', as: 'whitelistDetails' } },
        { $unwind: { path: '$whitelistDetails', preserveNullAndEmptyArrays: true } },
        { $lookup: { from: 'users', localField: 'whitelistDetails.userId', foreignField: '_id', as: 'userDetails' } },
        { $unwind: { path: '$userDetails', preserveNullAndEmptyArrays: true } },
        { $lookup: { from: 'multisigs', localField: 'userId', foreignField: 'issuerId', as: 'multisigDetails' } },
        { $unwind: { path: '$multisigDetails', preserveNullAndEmptyArrays: true } },
        // Add search functionality
        ...(search ? [{ $match: { $or: [{ 'whitelistDetails.address': { $regex: search, $options: 'i' } }, { 'userDetails.name': { $regex: search, $options: 'i' } }, { 'userDetails.email': { $regex: search, $options: 'i' } }] } }] : []),
        {
          $facet: {
            totalCount: [{ $count: 'count' }], // Calculates the total count
            paginatedResults: [
              { $sort: sort },
              ...(!isCsvBool ? [{ $skip: skip }, { $limit: limit }] : []),
              {
                $project: {
                  _id: 1,
                  whitelistId: '$whitelistDetails._id',
                  userId: '$whitelistDetails.userId',
                  txHash: '$whitelistDetails.txHash',
                  userDetails: { name: '$userDetails.name', email: '$userDetails.email', userImage: '$userDetails.userImage', onchainID: '$userDetails.onchainID', countryCode: '$userDetails.countryCode', userType: '$userDetails.userType' },
                  whitelistDetails: { address: '$whitelistDetails.address', status: '$whitelistDetails.status', txHash: '$whitelistDetails.txHash', multisig: '$whitelistDetails.multisig', freezUnfreez: '$whitelistDetails.freezUnfreez' },
                  projectDetails: { tokenTicker: 1 },
                  tokenAddress: 1,
                  fundAddress: 1,
                  erc20Address: 1,
                  identityRegistry: 1,
                  isFundDeploy: 1,
                  isTokenDeploy: 1,
                  createdAt: '$whitelistDetails.createdAt',
                  multisigDetails: '$multisigDetails',
                },
              },
            ],
          },
        },
      ];
      const result = await offeringSchema.aggregate(aggregationPipeline).exec();
      const users = result[0].paginatedResults;
      const totalCount = Object.keys(users[0].userDetails).length === 0 ? 0 : result[0].totalCount[0]?.count || 0;

      const totalPages = Math.ceil(totalCount / limit);

      return {
        data: { subscriber: users, currentPage: page, totalPages, totalCount, nextPage: page < totalPages ? page + 1 : null, previousPage: page > 1 ? page - 1 : null },
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
      };
    } catch (error) {
      logger.error(error, 'getOfferingSubscriberList');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  // getOfferingSubscriberList = async (filters: any, pagination: IPagination, isCsv: any): Promise<PromiseResolve> => {
  //   try {
  //     const { page = 1, limit = 10, sort = { createdAt: -1 }, search } = pagination;
  //     const skip = (page - 1) * limit;
  //     const query: FilterQuery<any> = {};

  //     const addFilter = (key: keyof IOffering, value: any) => {
  //       if (typeof value !== 'undefined') query[key] = value;
  //     };

  //     // Add standard filters
  //     addFilter('status', filters.status);
  //     addFilter('userId', filters.userId);
  //     addFilter('_id', filters.offeringId);
  //     addFilter('isDelete', false);

  //     const aggregationPipeline: any[] = [
  //       { $match: query },
  //       { $lookup: { from: 'whitelists', localField: '_id', foreignField: 'offeringId', as: 'whitelistDetails' } },
  //       { $unwind: { path: '$whitelistDetails', preserveNullAndEmptyArrays: true } },
  //       { $lookup: { from: 'users', localField: 'whitelistDetails.userId', foreignField: '_id', as: 'userDetails' } },
  //       { $unwind: { path: '$userDetails', preserveNullAndEmptyArrays: true } },
  //       // Add search functionality
  //       ...(search ? [{ $match: { $or: [{ 'whitelistDetails.address': { $regex: search, $options: 'i' } }, { 'userDetails.name': { $regex: search, $options: 'i' } }, { 'userDetails.email': { $regex: search, $options: 'i' } }] } }] : []),
  //     ];

  //     if (isCsv == 'false') {
  //       aggregationPipeline.push({
  //         $facet: {
  //           totalCount: [{ $count: 'count' }], // Calculates the total count
  //           paginatedResults: [
  //             { $sort: sort },
  //             { $skip: skip },
  //             { $limit: limit },
  //             {
  //               $project: {
  //                 _id: 1,
  //                 whitelistId: '$whitelistDetails._id',
  //                 userId: '$whitelistDetails.userId',
  //                 txHash: '$whitelistDetails.txHash',
  //                 userDetails: { name: '$userDetails.name', email: '$userDetails.email', userImage: '$userDetails.userImage', onchainID: '$userDetails.onchainID', countryCode: '$userDetails.countryCode', userType: '$userDetails.userType' },
  //                 whitelistDetails: { address: '$whitelistDetails.address', status: '$whitelistDetails.status', txHash: '$whitelistDetails.txHash' },
  //                 projectDetails: { tokenTicker: 1 },
  //                 tokenAddress: 1,
  //                 fundAddress: 1,
  //                 erc20Address: 1,
  //                 identityRegistry: 1,
  //                 isFundDeploy: 1,
  //                 isTokenDeploy: 1,
  //                 createdAt: '$whitelistDetails.createdAt',
  //               },
  //             },
  //           ],
  //         },
  //       });
  //     } else {
  //       aggregationPipeline.push({
  //         $project: {
  //           _id: 1,
  //           whitelistId: '$whitelistDetails._id',
  //           userId: '$whitelistDetails.userId',
  //           txHash: '$whitelistDetails.txHash',
  //           userDetails: { name: '$userDetails.name', email: '$userDetails.email', userImage: '$userDetails.userImage', onchainID: '$userDetails.onchainID', countryCode: '$userDetails.countryCode', userType: '$userDetails.userType' },
  //           whitelistDetails: { address: '$whitelistDetails.address', status: '$whitelistDetails.status', txHash: '$whitelistDetails.txHash' },
  //           projectDetails: { tokenTicker: 1 },
  //           tokenAddress: 1,
  //           fundAddress: 1,
  //           erc20Address: 1,
  //           identityRegistry: 1,
  //           isFundDeploy: 1,
  //           isTokenDeploy: 1,
  //           createdAt: '$whitelistDetails.createdAt',
  //         },
  //       });
  //     }

  //     const result = await offeringSchema.aggregate(aggregationPipeline).exec();

  //     let users = [];

  //     const totalCount = result[0].totalCount[0]?.count || 0;
  //     users = result[0].paginatedResults;
  //     const totalPages = Math.ceil(totalCount / limit);

  //     return {
  //       data: { subscriber: users, currentPage: page, totalPages, totalCount, nextPage: page < totalPages ? page + 1 : null, previousPage: page > 1 ? page - 1 : null },
  //       status: RESPONSES.SUCCESS,
  //       error: false,
  //       message: RES_MSG.USER.USERS_FETCH,
  //     };
  //   } catch (error) {
  //     logger.error(error, 'getOfferingSubscriberList');
  //     return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
  //   }
  // };

  /**
   * @param {IUpdateOffering} searchDetails
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  getTotalCount = async (searchDetails: IUpdateOffering): Promise<PromiseResolve> => {
    try {
      const pipeline: any = [
        { $match: { userId: searchDetails.userId, status: 'APPROVED' } },
        { $lookup: { from: 'orders', localField: '_id', foreignField: 'offeringId', as: 'orders' } },
        { $addFields: { hasOrders: { $gt: [{ $size: '$orders' }, 0] } } },
        { $unwind: { path: '$orders', preserveNullAndEmptyArrays: true } },
        { $match: { $or: [{ 'orders.status': 'MINTED' }, { orders: { $exists: false } }] } },
        {
          $group: {
            _id: '$_id',
            totalAmount: { $sum: { $cond: [{ $eq: ['$orders.status', 'MINTED'] }, { $ifNull: [{ $toDecimal: '$orders.amount' }, 0] }, 0] } },
            totalQuantity: { $sum: { $cond: [{ $eq: ['$orders.status', 'MINTED'] }, { $ifNull: [{ $toDecimal: '$orders.quantity' }, 0] }, 0] } },
            totalUsers: { $addToSet: '$orders.userId' },
          },
        },
        {
          $group: {
            _id: null,
            totalAmount: { $sum: '$totalAmount' },
            totalQuantity: { $sum: '$totalQuantity' },
            totalUsers: { $push: '$totalUsers' },
          },
        },
        {
          $project: {
            totalAmount: { $toString: '$totalAmount' },
            totalQuantity: { $toString: '$totalQuantity' },
            totalUsersCount: { $size: { $reduce: { input: '$totalUsers', initialValue: [], in: { $setUnion: ['$$value', '$$this'] } } } },
            _id: 0,
          },
        },
      ];

      const countPipeline: any = [
        { $match: { userId: searchDetails.userId } },
        {
          $group: {
            _id: null, // Group everything together
            offeringCount: { $sum: { $cond: [{ $eq: ['$status', 'APPROVED'] }, 1, 0] } },
            pendingOfferingCount: { $sum: { $cond: [{ $eq: ['$status', 'PENDING'] }, 1, 0] } },
            rejectedOfferingCount: { $sum: { $cond: [{ $eq: ['$status', 'REJECTED'] }, 1, 0] } },
            inProgressOfferingCount: { $sum: { $cond: [{ $eq: ['$status', 'IN_PROGRESS'] }, 1, 0] } },
          },
        },
        {
          $project: {
            offeringCount: 1,
            pendingOfferingCount: 1,
            rejectedOfferingCount: 1,
            inProgressOfferingCount: 1,
            _id: 0,
          },
        },
      ];
      const result = await offeringSchema.aggregate(pipeline);
      const countResult = await offeringSchema.aggregate(countPipeline);

      return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.COMMON.RECORD_FETCH, data: { ...result[0], ...countResult[0] } };
    } catch (error) {
      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * @param {IUpdateOffering} searchDetails
   * @param {IPagination} pagination
   * @param {string} type
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  investedOffering = async (searchDetails: IUpdateOffering, pagination: IPagination): Promise<PromiseResolve> => {
    try {
      const { page = 1, limit = 10, sort = { wrapperDeployedAt: -1 } } = pagination;

      const skip = (page - 1) * limit;

      const pipeline = [
        {
          $match: {
            userId: searchDetails.userId,
            status: {
              $in: ['MINTED', 'TRANSFER_FROM', 'TRANSFER_TO', 'CONVERT_TO', 'CONVERT_FROM'],
            },
          },
        },
        {
          $lookup: {
            from: 'offerings',
            localField: 'offeringId',
            foreignField: '_id',
            pipeline: [
              { $match: { iserc20: true } },
              {
                $project: {
                  _id: 0,
                  offeringId: '$_id',
                  erc20Address: 1,
                  fundAddress: 1,
                  deployedDate: 1,
                  wrapperDeployedAt: 1,
                  tokenAddress: 1,
                  offeringName: '$projectDetails.offeringName',
                  tokenTicker: '$projectDetails.tokenTicker',
                  assetName: '$projectDetails.assetName',
                  icon: '$overview.icon',
                  logo: '$overview.logo',
                  assetType: '$projectDetails.assetType',
                  tokenSupply: '$projectDetails.tokenSupply',
                  latestNav: '$projectDetails.latestNav',
                  launchValuation: '$projectDetails.launchValuation',
                },
              },
            ],
            as: 'result',
          },
        },
        { $unwind: '$result' },
        {
          $group: {
            _id: '$result.offeringId',
            offeringData: { $first: '$result' },
          },
        },
        {
          $replaceRoot: { newRoot: '$offeringData' },
        },
        {
          $facet: {
            topOffering: [{ $sort: sort }, { $skip: skip }, { $limit: limit }],
            totalCount: [{ $count: 'count' }],
          },
        },
      ];
      const result = await OrderSchema.aggregate(pipeline);

      const topOffering = result[0].topOffering;
      const totalCount = result[0].totalCount[0]?.count || 0;
      const totalPages = Math.ceil(totalCount / limit);
      const currentPage = page;
      const nextPage = currentPage < totalPages ? currentPage + 1 : null;
      const previousPage = currentPage > 1 ? currentPage - 1 : null;

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.COMMON.RECORD_FETCH,
        data: { topOffering, currentPage, totalPages, totalCount, nextPage, previousPage },
      };
    } catch (error) {
      logger.error(error, 'investedOffering error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * @param {IUpdateOffering} searchDetails
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  singleOfferingReport = async (searchDetails: IUpdateOffering): Promise<PromiseResolve> => {
    try {
      const pipeline: any = [
        {
          $match: {
            userId: searchDetails.userId,
            _id: searchDetails.offeringId,
            status: 'APPROVED',
          },
        },
        {
          $lookup: {
            from: 'orders',
            localField: '_id',
            foreignField: 'offeringId',
            as: 'investorCountry',
            pipeline: [
              { $match: { status: 'MINTED' } },
              {
                $lookup: {
                  from: 'userdetails',
                  localField: 'userId',
                  foreignField: '_id',
                  as: 'userDetails',
                },
              },
              { $unwind: { path: '$userDetails', preserveNullAndEmptyArrays: false } },
              {
                $project: {
                  country: {
                    $ifNull: ['$userDetails.mainInformation.nationality', '$userDetails.primaryContactInfo.personalInformation.citizenship'],
                  },
                  userId: 1,
                },
              },
              {
                $group: {
                  _id: '$country',
                  uniqueUsers: { $addToSet: '$userId' },
                },
              },
              {
                $project: {
                  _id: 0,
                  country: '$_id',
                  totalUser: { $size: '$uniqueUsers' },
                },
              },
            ],
          },
        },
        {
          $lookup: {
            from: 'orders',
            localField: '_id',
            foreignField: 'offeringId',
            as: 'topHolders',
            pipeline: [
              { $match: { status: 'MINTED' } },
              {
                $group: {
                  _id: null,
                  totalSupply: { $sum: { $toDecimal: '$quantity' } },
                  totalInvestors: { $addToSet: '$userId' },
                },
              },
            ],
          },
        },
        { $unwind: '$topHolders' },
        {
          $set: {
            totalSupply: '$topHolders.totalSupply',
            totalInvestorsCount: { $size: '$topHolders.totalInvestors' },
          },
        },
        {
          $lookup: {
            from: 'orders',
            localField: '_id',
            foreignField: 'offeringId',
            as: 'topHolders',
            let: { totalSupply: '$totalSupply' },
            pipeline: [
              { $match: { status: 'MINTED' } },
              {
                $lookup: {
                  from: 'users',
                  localField: 'userId',
                  foreignField: '_id',
                  as: 'users',
                },
              },
              { $unwind: { path: '$users', preserveNullAndEmptyArrays: true } },
              {
                $addFields: {
                  totalAmount: { $toDecimal: '$amount' },
                  totalQuantity: { $toDecimal: '$quantity' },
                },
              },
              {
                $group: {
                  _id: '$userId',
                  totalAmount: { $sum: '$totalAmount' },
                  totalQuantity: { $sum: '$totalQuantity' },
                  name: { $first: '$users.name' },
                  image: { $first: '$users.userImage' },
                },
              },
              {
                $addFields: {
                  totalAmount: { $toString: '$totalAmount' },
                  totalQuantity: { $toString: '$totalQuantity' },
                  percentage: {
                    $toString: {
                      $multiply: [{ $divide: ['$totalQuantity', '$$totalSupply'] }, 100],
                    },
                  },
                },
              },
              {
                $addFields: {
                  totalQuantityDecimal: { $toDecimal: '$totalQuantity' }, // Convert totalAmount to Decimal
                },
              },
              {
                $sort: { totalQuantityDecimal: -1 }, // Sort numerically
              },
              // { $sort: { totalAmount: -1 } },
              { $skip: 0 },
              { $limit: 5 },
            ],
          },
        },
        {
          $project: {
            totalSupply: { $toString: '$totalSupply' },
            totalInvestorsCount: 1,
            topHolders: 1,
            tokenAddress: 1,
            fundAddress: 1,
            offeringName: '$projectDetails.offeringName',
            isPrivate: '$projectDetails.isPrivate',
            title: '$overview.title',
            subTitle: '$overview.subTitle',
            description: '$overview.description',
            icon: '$overview.icon',
            logo: '$overview.logo',
            investorCountry: {
              $let: {
                vars: {
                  countries: {
                    $map: {
                      input: '$investorCountry',
                      as: 'country',
                      in: '$$country.country',
                    },
                  },
                },
                in: {
                  $map: {
                    input: { $setUnion: ['$$countries', []] },
                    as: 'uniqueCountry',
                    in: {
                      country: '$$uniqueCountry',
                      totalUser: {
                        $size: {
                          $filter: {
                            input: '$investorCountry',
                            as: 'item',
                            cond: { $eq: ['$$item.country', '$$uniqueCountry'] },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      ];

      const result = await offeringSchema.aggregate(pipeline);

      return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.COMMON.RECORD_FETCH, data: result[0] };
    } catch (error) {
      logger.error(error, 'singleOfferingReport');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * Generates an offering report based on specified search details including offering IDs and period.
   * It organizes data into time frames (hourly, daily, monthly, or yearly) and calculates cumulative balances.
   *
   * @param {Object} searchDetails - An object containing the offering ID(s) and the time period.
   * @returns {Promise<PromiseResolve>} - A Promise that resolves with the formatted offering data.
   *
   * Function Details:
   * - Extracts the offering ID and period from the searchDetails input.
   * - Determines start and end dates for the specified period using CommonHelper.getDateRange.
   * - Builds appropriate groupings and time frames for the report based on the period:
   *   - For yearly periods: Groups data by year and month.
   *   - For monthly or shorter periods (15 days, 7 days): Groups data by day.
   *   - For a single-day period: Groups data by hour.
   * - Constructs an aggregation pipeline using MongoDB features:
   *   - Matches data within the specified time range and with a status of 'MINTED'.
   *   - Groups and calculates total amounts based on group criteria (year, month, day, hour).
   *   - Sorts data by time-related fields for correct ordering.
   *   - Handles data prior to the start date to calculate a cumulative starting balance.
   * - Processes the main data and maps it against time frames to ensure all time intervals have data,
   *   filling missing intervals with zeros for continuity.
   * - Accumulates cumulative balances over the time frames.
   * - Formats the output to include offering IDs and their respective charts.
   *
   * Key Error Handling:
   * - Throws a `CustomError` for invalid period values.
   * - Handles and logs any unexpected errors, returning a standardized response.
   */

  offeringReport = async (searchDetails: any): Promise<PromiseResolve> => {
    try {
      const { offeringId, period } = searchDetails;
      const { startDate, endDate } = CommonHelper.getDateRange(period);

      let groupBy: any;
      let timeFrames: any[] = [];
      if (['1Y', '2Y', '3Y'].includes(period)) {
        groupBy = { year: { $year: '$createdAt' }, month: { $month: '$createdAt' } };
        timeFrames = Array.from({ length: 12 }, (_, i) => ({
          month: i + 1,
          groupStartTime: moment.unix(startDate).startOf('month').add(i, 'months').toISOString(),
          groupEndTime: moment
            .unix(startDate)
            .startOf('month')
            .add(i + 1, 'months')
            .subtract(1, 'second')
            .toISOString(),
        }));
      } else if (['1M', '15D', '7D'].includes(period)) {
        const daysInRange = moment.unix(endDate).diff(moment.unix(startDate), 'days') + 1;
        groupBy = { year: { $year: '$createdAt' }, month: { $month: '$createdAt' }, day: { $dayOfMonth: '$createdAt' } };
        timeFrames = Array.from({ length: daysInRange }, (_, i) => {
          const date = moment.unix(startDate).add(i, 'days');

          return { day: date.date(), month: date.month() + 1, groupStartTime: date.startOf('day').toISOString(), groupEndTime: date.endOf('day').toISOString() };
        });
      } else if (period === '1D') {
        groupBy = { year: { $year: '$createdAt' }, month: { $month: '$createdAt' }, day: { $dayOfMonth: '$createdAt' }, hour: { $hour: '$createdAt' } };
        timeFrames = Array.from({ length: 24 }, (_, i) => ({
          hour: i,
          groupStartTime: moment.unix(startDate).startOf('day').add(i, 'hours').toISOString(),
          groupEndTime: moment
            .unix(startDate)
            .startOf('day')
            .add(i + 1, 'hours')
            .subtract(1, 'second')
            .toISOString(),
        }));
      } else {
        throw new CustomError('Invalid period specified', RESPONSES.BAD_REQUEST);
      }

      const pipeline: any = [
        {
          $facet: {
            // Main data pipeline
            mainData: [
              {
                $match: {
                  offeringId: { $in: offeringId.map((id: any) => new Types.ObjectId(id)) },
                  createdAt: { $gte: new Date(startDate * 1000), $lte: new Date(endDate * 1000) },
                  status: {
                    $in: [
                      'MINTED',
                      //  'BURN', 'REDEEM', 'TRANSFER_FROM', 'TRANSFER_TO', 'CONVERT_TO', 'CONVERT_FROM'
                    ],
                  },
                },
              },
              {
                $addFields: {
                  quantity: { $toDecimal: '$quantity' },
                  amount: { $toDecimal: '$amount' },
                  feesInPercentage: { $toDecimal: '$feesInPercentage' },
                  price: { $toDecimal: '$price' },
                  wap: { $toDecimal: '$wap' },
                },
              },
              {
                $group: {
                  _id: groupBy,
                  offeringId: { $first: '$offeringId' },
                  currentQuantity: {
                    $sum: {
                      $switch: {
                        branches: [
                          { case: { $eq: ['$status', 'MINTED'] }, then: '$quantity' },
                          // { case: { $eq: ['$status', 'TRANSFER_TO'] }, then: '$quantity' },
                          // { case: { $eq: ['$status', 'CONVERT_TO'] }, then: '$quantity' },
                          // { case: { $in: ['$status', ['BURN', 'REDEEM', 'TRANSFER_FROM', 'CONVERT_FROM']] }, then: { $multiply: ['$quantity', -1] } },
                        ],
                        default: 0,
                      },
                    },
                  },
                  totalAmount: {
                    $sum: {
                      $switch: {
                        branches: [
                          { case: { $eq: ['$status', 'MINTED'] }, then: '$amount' },
                          // { case: { $eq: ['$status', 'TRANSFER_TO'] }, then: '$quantity' },
                          // { case: { $eq: ['$status', 'CONVERT_TO'] }, then: '$quantity' },
                          // { case: { $in: ['$status', ['BURN', 'REDEEM', 'TRANSFER_FROM', 'CONVERT_FROM']] }, then: { $multiply: ['$quantity', -1] } },
                        ],
                        default: 0,
                      },
                    },
                  },
                },
              },
              { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1, '_id.hour': 1 } },
            ],

            // Total Before Start Date pipeline
            totalBeforeStartDate: [
              {
                $match: {
                  offeringId: { $in: offeringId.map((id: any) => new Types.ObjectId(id)) },
                  createdAt: { $lt: new Date(startDate * 1000) }, // This is the condition
                  status: {
                    $in: [
                      'MINTED',
                      //  'BURN', 'REDEEM', 'TRANSFER_FROM', 'TRANSFER_TO', 'CONVERT_TO', 'CONVERT_FROM'
                    ],
                  },
                },
              },
              {
                $addFields: {
                  quantity: { $toDecimal: '$quantity' },
                  amount: { $toDecimal: '$amount' },
                  feesInPercentage: { $toDecimal: '$feesInPercentage' },
                  currentPrice: { $toDecimal: '$currentPrice' },
                  wap: { $toDecimal: '$wap' },
                },
              },
              {
                $group: {
                  _id: null,
                  currentQuantityBeforeStartDate: {
                    $sum: {
                      $switch: {
                        branches: [
                          { case: { $eq: ['$status', 'MINTED'] }, then: '$quantity' },
                          // { case: { $eq: ['$status', 'TRANSFER_TO'] }, then: '$quantity' },
                          // { case: { $eq: ['$status', 'CONVERT_TO'] }, then: '$quantity' },
                          // { case: { $in: ['$status', ['BURN', 'REDEEM', 'TRANSFER_FROM', 'CONVERT_FROM']] }, then: { $multiply: ['$quantity', -1] } },
                        ],
                        default: 0,
                      },
                    },
                  },
                  totalAmountBeforeStartDate: {
                    $sum: {
                      $switch: {
                        branches: [
                          { case: { $eq: ['$status', 'MINTED'] }, then: '$amount' },
                          // { case: { $eq: ['$status', 'TRANSFER_TO'] }, then: '$quantity' },
                          // { case: { $eq: ['$status', 'CONVERT_TO'] }, then: '$quantity' },
                          // { case: { $in: ['$status', ['BURN', 'REDEEM', 'TRANSFER_FROM', 'CONVERT_FROM']] }, then: { $multiply: ['$quantity', -1] } },
                        ],
                        default: 0,
                      },
                    },
                  },
                },
              },
            ],
          },
        },
        // Unwind totalBeforeStartDate to make it consistent
        {
          $unwind: {
            path: '$totalBeforeStartDate',
            preserveNullAndEmptyArrays: true, // Ensures null values are preserved
          },
        },
        { $project: { mainData: 1, cumulativeBalanceBeforeStartDate: '$totalBeforeStartDate.totalAmountBeforeStartDate' } },
      ];
      const results = await OrderSchema.aggregate(pipeline);
      const { mainData, cumulativeBalanceBeforeStartDate } = results[0];
      const formattedResult = offeringId.map((id: any) => {
        const groupedResults = mainData.filter((item: any) => item.offeringId.toString() === id);
        const chart: any = timeFrames.map((timeFrame) => {
          const match = groupedResults.find((item: any) => {
            const group = item._id;
            if (['1Y', '2Y', '3Y'].includes(period)) {
              return group.month === timeFrame.month;
            }
            if (['1M', '15D', '7D'].includes(period)) {
              return group.day === timeFrame.day && group.month === timeFrame.month;
            }
            if (period === '1D') {
              return group.hour === timeFrame.hour;
            }

            return false;
          });

          return {
            totalAmount: match ? match.totalAmount : 0,
            groupStartTime: timeFrame.groupStartTime,
            groupEndTime: timeFrame.groupEndTime,
            ...(period === '1Y' && { month: timeFrame.month }),
            ...(period === '1D' && { hour: timeFrame.hour }),
            ...(['1M', '15D', '7D'].includes(period) && { day: timeFrame.day, month: timeFrame.month }),
          };
        });

        const updatedChart: any = chart.reduce((acc: any[], item: any, index: number) => {
          const previousBalance = acc.length > 0 ? acc[acc.length - 1].cumulativeBalance || 0 : 0;
          if (index === 0) {
            item = { ...item, cumulativeBalance: cumulativeBalanceBeforeStartDate ? calculate('add', cumulativeBalanceBeforeStartDate, item.totalAmount) : item.totalAmount };
          } else if (item.totalAmount > 0) {
            item = { ...item, cumulativeBalance: calculate('add', previousBalance, item.totalAmount) };
          } else if (previousBalance > 0) {
            item = { ...item, cumulativeBalance: previousBalance };
          } else {
            item = { ...item, cumulativeBalance: 0 };
          }

          acc.push(item);

          return acc;
        }, []);

        return { offeringId: id, chart: updatedChart };
      });

      return { status: RESPONSES.SUCCESS, error: false, message: 'Records fetched successfully', data: formattedResult };
    } catch (error) {
      logger.error('offeringReport', error);

      return { status: RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || 'Internal server error' };
    }
  };

  // chart data order only
  //offeringReport = async (searchDetails: any): Promise<PromiseResolve>  =>{
  //   try {
  //     const { offeringId, period } = searchDetails;
  //     const { startDate, endDate } = CommonHelper.getDateRange(period);

  //     const pipeline: any = [
  //       {
  //         $facet: {
  //           // ✅ Get transactions within the date range
  //           mainData: [
  //             {
  //               $match: {
  //                 offeringId: { $in: offeringId.map((id: any) => new Types.ObjectId(id)) },
  //                 createdAt: { $gte: new Date(startDate * 1000), $lte: new Date(endDate * 1000) },
  //                 status: {
  //                   $in: [
  //                     'MINTED',
  //                     // 'BURN', 'REDEEM', 'TRANSFER_FROM', 'TRANSFER_TO', 'CONVERT_TO', 'CONVERT_FROM', 'PRICE_CHANGE'
  //                   ],
  //                 },
  //               },
  //             },
  //             {
  //               $project: {
  //                 createdAt: 1,
  //                 status: 1,
  //                 offeringId: 1,
  //                 currentPrice: 1,
  //                 amount: {
  //                   $switch: {
  //                     branches: [
  //                       {
  //                         case: { $eq: ['$status', 'PRICE_CHANGE'] },
  //                         then: { $multiply: [{ $toDecimal: '$amount' }, { $toDecimal: '$price' }] },
  //                       },
  //                       {
  //                         case: { $eq: ['$status', 'REDEEM'] },
  //                         then: { $multiply: [{ $toDecimal: '$wap' }, -1] },
  //                       },
  //                       {
  //                         case: { $in: ['$status', ['MINTED', 'TRANSFER_TO', 'CONVERT_TO']] },
  //                         then: { $multiply: [{ $toDecimal: '$quantity' }, { $toDecimal: '$price' }] },
  //                       },
  //                       {
  //                         case: { $in: ['$status', ['BURN', 'TRANSFER_FROM', 'CONVERT_FROM']] },
  //                         then: { $multiply: [{ $toDecimal: '$quantity' }, { $toDecimal: '$price' }, -1] },
  //                       },
  //                     ],
  //                     default: 0,
  //                   },
  //                 },
  //               },
  //             },
  //             { $sort: { createdAt: 1 } },
  //           ],

  //           // ✅ Get cumulative balance before the start date
  //           totalBeforeStartDate: [
  //             {
  //               $match: {
  //                 offeringId: { $in: offeringId.map((id: any) => new Types.ObjectId(id)) },
  //                 createdAt: { $lt: new Date(startDate * 1000) },
  //                 status: {
  //                   $in: [
  //                     'MINTED',
  //                     //  'BURN', 'REDEEM', 'TRANSFER_FROM', 'TRANSFER_TO', 'CONVERT_TO', 'CONVERT_FROM', 'PRICE_CHANGE'
  //                   ],
  //                 },
  //               },
  //             },
  //             {
  //               $group: {
  //                 _id: '$offeringId',
  //                 totalAmountBeforeStartDate: {
  //                   $sum: {
  //                     $switch: {
  //                       branches: [
  //                         {
  //                           case: { $eq: ['$status', 'PRICE_CHANGE'] },
  //                           then: { $multiply: [{ $toDecimal: '$amount' }, { $toDecimal: '$price' }] },
  //                         },
  //                         {
  //                           case: { $eq: ['$status', 'REDEEM'] },
  //                           then: { $multiply: [{ $toDecimal: '$wap' }, -1] },
  //                         },
  //                         {
  //                           case: { $in: ['$status', ['MINTED', 'TRANSFER_TO', 'CONVERT_TO']] },
  //                           then: { $multiply: [{ $toDecimal: '$quantity' }, { $toDecimal: '$price' }] },
  //                         },
  //                         {
  //                           case: { $in: ['$status', ['BURN', 'TRANSFER_FROM', 'CONVERT_FROM']] },
  //                           then: { $multiply: [{ $toDecimal: '$quantity' }, { $toDecimal: '$price' }, -1] },
  //                         },
  //                       ],
  //                       default: 0,
  //                     },
  //                   },
  //                 },
  //               },
  //             },
  //           ],
  //         },
  //       },
  //       { $unwind: { path: '$totalBeforeStartDate', preserveNullAndEmptyArrays: true } },
  //       { $project: { mainData: 1, cumulativeBalanceBeforeStartDate: '$totalBeforeStartDate.totalAmountBeforeStartDate' } },
  //     ];

  //     const results = await OrderSchema.aggregate(pipeline);
  //     const { mainData, cumulativeBalanceBeforeStartDate } = results[0];

  //     // ✅ Process results per offeringId
  //     const formattedResult = offeringId.map((id: any) => {
  //       const filteredData = mainData.filter((txn: any) => txn.offeringId.toString() === id);
  //       let cumulativeBalance = cumulativeBalanceBeforeStartDate ? cumulativeBalanceBeforeStartDate.toString() : '0';

  //       const chart = filteredData.map((txn: any) => {
  //         const amount = txn.amount.toString();
  //         if (txn.status == 'PRICE_CHANGE') {
  //           cumulativeBalance = amount;
  //         } else {
  //           cumulativeBalance = calculate('add', cumulativeBalance, amount);
  //         }

  //         return {
  //           groupStartTime: txn.createdAt,
  //           amount: amount,
  //           cumulativeBalance: cumulativeBalance,
  //         };
  //       });

  //       return { offeringId: id, chart };
  //     });

  //     return { status: RESPONSES.SUCCESS, error: false, message: 'Records fetched successfully', data: formattedResult };
  //   } catch (error) {
  //     logger.error('offeringReport', error);
  //     return { status: RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || 'Internal server error' };
  //   }
  // },

  /**
   * @param {IUpdateOffering} offeringId
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  tokenValuation = async (offeringId: IUpdateOffering, eventData?: any): Promise<PromiseResolve> => {
    try {
      let nav;
      const { error, data, message, status } = await this.fetchOfferingDetails({ _id: offeringId });
      if (error) throw new CustomError(message, status);

      if (data?.status === offeringStatusEnum.APPROVED) {
        const divisor = BigInt(10 ** data.projectDetails.tokenDecimals);
        if (eventData && eventData.newValuation) {
          nav = eventData.newValuation;
        } else {
          const provider = new ethers.JsonRpcProvider(CONFIG.RPC_URL);
          const contractInstance = new ethers.Contract(data.fundAddress, data.projectDetails.assetType === AssetTypeEnum.Equity ? equityAbi : realStateAbi, provider);
          const latestNav = data.projectDetails.assetType === AssetTypeEnum.Equity ? await contractInstance.getCurrentValuation() : await contractInstance.getNAV();
          nav = latestNav / divisor;
        }
        const finalResult = Number(nav);
        const price = calculate('div', nav, data.projectDetails?.tokenSupply) || 0;

        // Fetch unique users' orders related to offeringId
        const uniqueUsers = await OrderSchema.distinct('userId', { offeringId });

        // Update offering details
        const updateOfferingResp = await this.updateOfferingDetails(
          {
            projectDetails: data.projectDetails.assetType === AssetTypeEnum.Equity ? { previousValuation: finalResult } : { latestNav: finalResult },
          },
          { _id: offeringId },
        );

        if (updateOfferingResp.error) {
          throw new CustomError(updateOfferingResp.message, updateOfferingResp.status);
        }

        // Fetch portfolio details for each user and create new orders
        const newOrders = await Promise.all(
          uniqueUsers.map(async (userId) => {
            const { data } = await UserService.fetchUserPortfolio(userId?.toString(), false, {}, offeringId?._id.toString());
            return {
              userId,
              offeringId,
              quantity: '0',
              amount: data?.portfolio[0]?.currentQuantity || '0',
              status: 'PRICE_CHANGE',
              orderType: 'PRICE_CHANGE',
              txHash: `${eventData.txHash}?userId=${userId}`,
              price,
              currentPrice: price,
              createdAt: eventData?.createdAt,
              updatedAt: eventData?.updatedAt,
            };
          }),
        );
        // Insert new orders in bulk
        if (newOrders.length > 0) {
          await OrderSchema.insertMany(newOrders);
        }

        // Save NAV history
        const history = await navHistorySchema.create({
          price,
          txHash: eventData.txHash,
          navAmount: nav,
          offeringId,
          createdAt: eventData?.createdAt,
          updatedAt: eventData?.updatedAt,
        });

        if (!history) {
          throw new Error('Failed to create history');
        }

        await OrderSchema.updateMany({ offeringId }, { currentPrice: price });

        return {
          status: RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.SUCCESS_MSG.OFFERING_UPDATION_SUCCESS,
          data: finalResult,
        };
      }

      throw new CustomError(RES_MSG.COMMON.BAD_REQUEST, RESPONSES.BAD_REQUEST);
    } catch (error) {
      logger.error('tokenValuation', error);

      return {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      };
    }
  };

  /**
   * Adds a country to the authorizedCountries list of an offering.
   * @param complianceData - Data containing compliance, countryCode, name, and isoCode
   * @returns Promise<PromiseResolve>
   */
  countryAllow = async (complianceData: CountryData): Promise<PromiseResolve> => {
    try {
      const { compliance, countryCode, name, isoCode } = complianceData;

      // Validate input data
      if (!compliance || !countryCode || !name || !isoCode) {
        throw new CustomError(RES_MSG.COMMON.BAD_REQUEST, RESPONSES.BAD_REQUEST);
      }

      // Check for duplicate country by isoCode
      const existing = await offeringSchema.findOne({
        compliance,
        'projectDetails.authorizedCountries.isoCode': isoCode,
      });
      if (existing) {
        throw new CustomError('Country already authorized', RESPONSES.BAD_REQUEST);
      }

      const result = await offeringSchema.findOneAndUpdate(
        { compliance },
        {
          $addToSet: {
            'projectDetails.authorizedCountries': {
              name,
              isoCode,
              countryCode,
            },
          },
        },
        { new: true, runValidators: true },
      );

      if (!result) {
        throw new CustomError(RES_MSG.COMMON.NO_OFFERING, RESPONSES.NOTFOUND);
      }

      return {
        message: 'Country added successfully',
        status: RESPONSES.ACCEPTED,
        error: false,
        data: result,
      };
    } catch (error) {
      logger.error('countryAllow', error);
      return {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      };
    }
  };

  /**
   * Removes a country from the authorizedCountries list of an offering.
   * @param complianceData - Data containing compliance, countryCode, name, and isoCode
   * @returns Promise<PromiseResolve>
   */
  countryRestrict = async (complianceData: CountryData): Promise<PromiseResolve> => {
    try {
      const { compliance, countryCode, name, isoCode } = complianceData;

      // Validate input data
      if (!compliance || !countryCode || !name || !isoCode) {
        throw new CustomError(RES_MSG.COMMON.BAD_REQUEST, RESPONSES.BAD_REQUEST);
      }

      const result = await offeringSchema.findOneAndUpdate(
        { compliance },
        {
          $pull: {
            'projectDetails.authorizedCountries': {
              isoCode, // Match by isoCode to ensure the correct country is removed
            },
          },
        },
        { new: true, runValidators: true },
      );

      if (!result) {
        throw new CustomError(RES_MSG.COMMON.NO_OFFERING, RESPONSES.NOTFOUND);
      }

      // Check if the country was actually removed
      const wasRemoved = !result.projectDetails.authorizedCountries.some((country) => country.isoCode === isoCode);
      if (!wasRemoved) {
        throw new CustomError('Country not found in authorized list', RESPONSES.BAD_REQUEST);
      }

      return {
        message: 'Country removed successfully',
        status: RESPONSES.ACCEPTED,
        error: false,
        data: result,
      };
    } catch (error) {
      logger.error('countryRestrict', error);
      return {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      };
    }
  };
}

export default new OfferingService();
