import * as <PERSON><PERSON> from 'joi';
import { JoiValidationResult } from '../../utils/common.interface';
import * as joiOptions from '../../helpers/joi.helper';
import { RES_MSG, RESPONSES } from '../../utils/responseUtils';
import logger from '../../helpers/logging/logger.helper';
import { IOffering, IUpdateOffering } from './models/offerings.model';
import { nameMaxLength, nameMinLength, urlPattern } from '../../utils/constant';

class OfferingValidation {
  /**
   * offering list Validate .
   * @returns {Promise<JoiValidationResult>}
   */
  async offeringListValidation(params: Record<string, any>): Promise<JoiValidationResult> {
    try {
      const schema = joiOptions.paginationSchema
        .keys({
          status: joiOptions.offeringStatusSchema,
          iserc20: Joi.boolean().optional().default(false),
          isDeleted: Joi.boolean().optional().default(false),
          isCsv: Joi.boolean().default(false).optional(),
        })
        .unknown(false); // Disallow any keys not explicitly defined in the schema
      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'topOfferingValidation Error');

      return { error: true, value: '', message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR };
    }
  }

  /**
   * offering list Validate .
   * @returns {Promise<JoiValidationResult>}
   */
  async whitlistOfferingListValidation(params: Record<string, any>): Promise<JoiValidationResult> {
    try {
      const schema = Joi.object({
        page: Joi.number().integer().min(1).default(1).required(),
        limit: Joi.number().integer().min(1).default(10).required(),
      });
      const { error, value } = schema.validate(params, joiOptions.options);

      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'whitlistOfferingListValidation Validation Error');

      return { error: true, value: '', message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR };
    }
  }

  /**
   * Validate the offering details based on the current step
   * @returns {Promise<JoiValidationResult>}
   */
  async createOfferingsValidation(params: IOffering): Promise<JoiValidationResult> {
    try {
      let schemaToValidate;
      switch (params.currentStep) {
        case 1:
          schemaToValidate = joiOptions.currentStepSchema.append({ overview: joiOptions.overviewSchema.required(), offeringId: Joi.string().optional() });
          break;
        case 2:
          schemaToValidate = joiOptions.currentStepSchema.append({ projectDetails: joiOptions.projectDetailsSchema.required(), offeringId: Joi.string().required() });
          break;
        case 3:
          if (!params.documents?.assetType) {
            // If `assetType` does not exist, skip validation and return the raw input
            return { error: false, value: params };
          }
          schemaToValidate = joiOptions.currentStepSchema.append({ documents: joiOptions.offeringDocumentsSchema.required(), offeringId: Joi.string().required() });
          break;
        case 4:
          schemaToValidate = joiOptions.currentStepSchema.append({ team: joiOptions.teamSchema.required(), offeringId: Joi.string().required() });

          break;
        case 5:
          // Step 5: Final submission (Validate all fields)
          schemaToValidate = joiOptions.currentStepSchema.append({
            isFinalSubmission: Joi.boolean().required().default(true),
            offeringId: Joi.string().required(),
          });
          break;
        default:
          return { error: false, value: { currentStep: 0 } };
      }

      const { error, value } = schemaToValidate.validate(params, joiOptions.options);
      if (error) {
        if (JSON.stringify(error)?.includes('Start Date must be greater than or equal to')) {
          return { error: true, value: '', message: RES_MSG.ERROR_MSG.START_DATE, status: RESPONSES.BAD_REQUEST };
        }

        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'createOfferingsValidation Error');

      return { error: true, value: '', message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR };
    }
  }

  /**
   * Validate the offering  details based on the status
   * @returns {Promise<JoiValidationResult>}
   */
  async updateOfferingsValidation(params: IUpdateOffering): Promise<JoiValidationResult> {
    try {
      const schemaToValidate = Joi.object({
        overview: {
          title: Joi.string()
            .trim()
            .optional()
            .label('Title')
            .min(2)
            .max(100)
            // .pattern(alphanumericPattern)
            .messages({
              'string.empty': '{#label} is required',
              'any.required': '{#label} is required',
              'string.min': '{#label} must be at least {#limit} characters long',
              'string.max': '{#label} must be at most {#limit} characters long',
              'string.pattern.base': '{#label} must contain only alphanumeric characters and spaces',
            }),
          subTitle: Joi.string()
            .trim()
            .optional()
            .label('Sub Title')
            .min(2)
            .max(200)
            // .pattern(alphanumericPattern)
            .messages({
              'string.empty': '{#label} is required',
              'any.required': '{#label} is required',
              'string.min': '{#label} must be at least {#limit} characters long',
              'string.max': '{#label} must be at most {#limit} characters long',
              'string.pattern.base': '{#label} must contain only alphanumeric characters and spaces',
            }),
          description: Joi.string()
            .trim()
            .optional()
            .label('Description')
            .min(2)
            .max(500)
            // .pattern(alphanumericPattern)
            .messages({
              'string.empty': '{#label} is required',
              'any.required': '{#label} is required',
              'string.min': '{#label} must be at least {#limit} characters long',
              'string.max': '{#label} must be at most {#limit} characters long',
              'string.pattern.base': '{#label} must contain only alphanumeric characters and spaces',
            }),
          companyDescription: Joi.string()
            .trim()
            .optional()
            .label('Company Description')
            .min(2)
            .max(500)
            // .pattern(alphanumericPattern)
            .messages({
              'string.empty': '{#label} is required',
              'any.required': '{#label} is required',
              'string.min': '{#label} must be at least {#limit} characters long',
              'string.max': '{#label} must be at most {#limit} characters long',
              'string.pattern.base': '{#label} must contain only alphanumeric characters and spaces',
            }),
          webUrl: Joi.string().optional().pattern(urlPattern).label('Website').messages({ 'string.empty': '{#label}  must be a valid URL', 'string.pattern.base': '{#label} must be a valid URL' }),
          icon: Joi.string().optional().label('icon').messages({ 'string.empty': '{#label} is required', 'any.required': '{#label} is required', 'string.pattern.base': '{#label} must contain only alphanumeric characters and spaces' }),
          cover: Joi.string().optional().label('cover').messages({ 'string.empty': '{#label} is required', 'any.required': '{#label} is required', 'string.pattern.base': '{#label} must contain only alphanumeric characters and spaces' }),
          logo: Joi.string().optional().label('logo').messages({ 'string.empty': '{#label} is required', 'any.required': '{#label} is required', 'string.pattern.base': '{#label} must contain only alphanumeric characters and spaces' }),
        },

        // --
        projectDetails: {
          startDate: Joi.date()
            .optional()
            .label('Start Date')
            .custom((value, helpers) => {
              const currentDate = joiOptions.normalizeDate(new Date());
              const inputDate = joiOptions.normalizeDate(value);

              if (inputDate < currentDate) {
                return helpers.error('date.min', {
                  message: "Start Date must be greater than or equal to today's date", // Set the message directly here
                });
              }

              return value; // Return original value if no error
            })
            .messages({
              'any.required': '{#label} is required',
              'date.base': '{#label} must be a valid date',
              'string.min': '{#label} must be greater than or equal to today date',
              'string.CustomError': '{#label} must be greater than or equal to today date',
            }),

          endDate: Joi.date()
            .greater(Joi.ref('startDate'))
            .optional()
            .label('End Date')
            .messages({ 'any.required': '{#label} is required', 'date.base': '{#label} must be a valid date', 'date.greater': '{#label} must be greater than the Start Date' }),

          isPrivate: Joi.boolean().optional(),
          offeringMembers: Joi.array()
            .items(Joi.string().email().optional())
            .when('isPrivate', {
              is: true, // If isPrivate is true
              then: Joi.array()
                .items(Joi.string().email())
                .min(1) // Ensure at least one email is present
                .required()
                .label('Offering members')
                .default([])
                .messages({
                  'array.base': '{#label} must be an array',
                  'string.base': 'Each member must be a valid string',
                  'string.email': 'Please send a valid email in {#label}',
                  'any.required': 'Offering members are required when the offering is private',
                  'array.min': 'At least one email is required in {#label}', // Custom message for minimum requirement
                }),
              otherwise: Joi.optional(),
            }),

          isAuthorized: Joi.boolean().optional().label('Authorized').messages({ 'any.required': '{#label} is required' }),
          authorizedCountries: Joi.array()
            .items(
              Joi.object({
                name: Joi.string().required().label('Country Name'),
                isoCode: Joi.string().required().label('ISO Code'),
                countryCode: Joi.string().required().label('Country Code'),
              }),
            )
            .label('Countries')
            .optional()
            .messages({
              'array.base': '{#label} must be an array',
              'string.base': 'Each Countries must be a valid string',
              'string.empty': '{#label} cannot be empty',
              'object.base': '{#label} must contain valid objects',
              'any.required': '{#label} is required',
            }),
          isTransferAgent: Joi.boolean().optional().label('Transfer Agent').messages({ 'any.required': '{#label} is required' }),
          taId: Joi.string()
            .trim()
            .label('Transfer Agent')
            .when('isTransferAgent', {
              is: true,
              then: Joi.string()
                .required()
                .messages({
                  'string.empty': '{#label} cannot be empty',
                  'any.required': 'Please Select {#label}',
                  'string.min': `{#label} must contain at least ${nameMinLength} characters.`,
                  'string.max': `{#label} must be at most ${nameMaxLength} characters long.`,
                  'string.pattern.base': '{#label} must contain only alphanumeric characters and spaces',
                }),
              otherwise: Joi.optional(),
            }),
          customFields: Joi.array()
            .items(
              Joi.object({
                label: Joi.string().trim().required().min(2).max(100).required().label('Custom Field Label').messages({ 'any.required': '{#label} is required' }),
                type: Joi.string().trim().required().min(2).max(100).label('Custom Field Type').messages({ 'any.required': '{#label} is required' }),
                value: Joi.any().required().label('Custom Field Value').messages({ 'any.required': '{#label} is required' }),
              }),
            )
            .optional(),
        },
        documents: {
          pitchDeck: Joi.string().uri().optional().label('Pitch Desk').messages({ 'string.uri': '{#label} must be a valid URL', 'any.required': '{#label} is required when assetType is provided' }),

          confidentialInformationMemorendum: Joi.string().uri().optional().label('Confidential Information Memorandum').messages({ 'string.uri': '{#label} must be a valid URL', 'any.required': '{#label} is required when assetType is provided' }),

          landRegistration: Joi.string().uri().optional().label('Land Registration Document').messages({ 'string.uri': '{#label} must be a valid URL', 'any.required': '{#label} is required' }),
          titleDocs: Joi.string().uri().optional().label('Title Document').messages({ 'string.uri': '{#label} must be a valid URL', 'any.required': '{#label} is required' }),
          bankApproval: Joi.string().uri().optional().label('Bank Approval Document').messages({ 'string.uri': '{#label} must be a valid URL', 'any.required': '{#label} is required' }),
          encumbranceCertificate: Joi.string().uri().optional().label('Encumbrance Certificate').messages({ 'string.uri': '{#label} must be a valid URL', 'any.required': '{#label} is required' }),
          propertyTaxReceipt: Joi.string().uri().optional().label('Property Tax Receipt').messages({ 'string.uri': '{#label} must be a valid URL', 'any.required': '{#label} is required' }),

          // Equity
          articlesOfAssociation: Joi.string().uri().optional().label('Articles of Association').messages({ 'string.uri': '{#label} must be a valid URL' }),

          operatingAgreement: Joi.string().uri().optional().label('Operating Agreement').messages({ 'string.uri': '{#label} must be a valid URL', 'any.required': '{#label} is required' }),

          taxAssignmentLetter: Joi.string().uri().optional().label('Tax Assignment Letter').messages({ 'string.uri': '{#label} must be a valid URL', 'any.required': '{#label} is required' }),

          certificateOfRegistration: Joi.string().uri().optional().label('Certificate Of Registration').messages({ 'string.uri': '{#label} must be a valid URL', 'any.required': '{#label} is required' }),
          registerOfManagers: Joi.string().uri().optional().label('Register of Managers').messages({ 'string.uri': '{#label} must be a valid URL', 'any.required': '{#label} is required' }),

          customDocs: Joi.array()
            .items(
              Joi.object({
                docsLabel: Joi.string().required().label('Custom Document Label'),
                value: Joi.string().uri().required().label('Custom Document URL').messages({ 'string.uri': '{#label} must be a valid URL', 'any.required': '{#label} is required' }),
              }),
            )
            .optional(),
        },
        team: joiOptions.teamSchema.optional(),
        offeringId: Joi.string().required(),
      });

      const { error, value } = schemaToValidate.validate(params, joiOptions.options);
      if (error) {
        if (JSON.stringify(error)?.includes('Start Date must be greater than or equal to')) {
          return { error: true, value: '', message: RES_MSG.ERROR_MSG.START_DATE, status: RESPONSES.BAD_REQUEST };
        }

        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'updateOfferingsValidation Error');

      return { error: true, value: '', message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR };
    }
  }

  /**
   * Validate the offering nav based on the status
   * @returns {Promise<JoiValidationResult>}
   */
  async updateOfferingsNavValidation(params: IUpdateOffering): Promise<JoiValidationResult> {
    try {
      const schemaToValidate = Joi.object({
        // latestNav: Joi.number().required().unsafe().min(0).max(999999999999999).label('Latest NAV').messages({
        //   'any.required': '{#label} is required',
        //   'number.min': '{#label} must be a positive number',
        //   'number.max': '{#label} cannot exceed 15 digits.',
        //   'number.base': '{#label} must be a number',
        // }),
        offeringId: Joi.string().required(),
      });

      const { error, value } = schemaToValidate.validate(params, joiOptions.options);
      if (error) {
        if (JSON.stringify(error)?.includes('Start Date must be greater than or equal to')) {
          return { error: true, value: '', message: RES_MSG.ERROR_MSG.START_DATE, status: RESPONSES.BAD_REQUEST };
        }

        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'updateOfferingsValidation Error');

      return { error: true, value: '', message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR };
    }
  }

  /**
   * Validate the subscribe details .
   * @returns {Promise<JoiValidationResult>}
   */
  async subscribeValidation(params: IOffering): Promise<JoiValidationResult> {
    try {
      const ethAddressRegex = /^0x[a-fA-F0-9]{40}$/;

      const schema = Joi.object({
        offeringId: Joi.string().length(24).hex().required().messages({ 'string.length': 'Invalid Offering Id', 'string.hex': '"offeringId" must be a valid MongoDB ObjectId' }),
        address: Joi.string()
          .trim()
          .max(42)
          .min(42)
          .required()
          .lowercase()
          .pattern(ethAddressRegex)
          .label('Address')
          .messages({ 'string.empty': '{#label} is required', 'any.required': '{#label} is required', 'string.max': '{#label} must be at most 42 characters long', 'string.pattern.base': 'Invalid Address' }),
      });
      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'subscribeValidation Error');

      return { error: true, value: '', message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR };
    }
  }

  /**
   * Validate the subscribe details .
   * @returns {Promise<JoiValidationResult>}
   */
  async offeringReportValidation(params: IOffering): Promise<JoiValidationResult> {
    try {
      const schema = Joi.object({
        offeringId: Joi.array().items(Joi.string().length(24).hex().required()).min(1).max(5).required().messages({
          'array.base': 'offeringId must be an array',
          'array.includes': 'offeringId must contain valid MongoDB ObjectIds',
          'string.length': 'Invalid Offering Id',
          'string.hex': 'offeringId must be a valid MongoDB ObjectId',
          'array.min': 'offeringId must contain at least 1 item',
          'array.max': 'offeringId must contain at most 5 items',
        }),
        period: Joi.string()
          .valid('1Y', '2Y', '3Y', '1M', '15D', '7D', '1D')
          .optional()
          .default('1year')
          .messages({ 'string.base': 'period must be a string', 'string.empty': 'period cannot be empty', 'any.only': 'period must be one of [1Y, 2Y, 3Y, 1M, 15D, 7D, 1D]' }),
      });
      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'offeringReportValidation Error');

      return { error: true, value: '', message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR };
    }
  }

  /**
   * top offering Validate .
   * @returns {Promise<JoiValidationResult>}
   */
  async topOfferingValidation(params: IOffering): Promise<JoiValidationResult> {
    try {
      const schema = Joi.object({
        type: Joi.string().valid('AUM', 'INVESTOR').required().default('INVESTOR').messages({ 'string.base': 'period must be a string', 'string.empty': 'period cannot be empty', 'any.only': 'period must be one of [AUM, INVESTOR]' }),
      });
      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'topOfferingValidation Error');

      return { error: true, value: '', message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR };
    }
  }

  async updateWhitelistedOffering(query: { whitelistId: string; status?: string }): Promise<JoiValidationResult> {
    try {
      const schema = Joi.object({
        whitelistId: Joi.string().required(), // Validate offeringId as a required string
        status: Joi.string().required().default('REJECTED'),
      });

      const { error, value } = schema.validate(query, joiOptions.options);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'getOrdersfromOfferingIdValidation Error');

      return { error: true, value: '', message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR };
    }
  }

  async getOrdersfromOfferingIdValidation(query: { page?: number; limit?: number; offeringId: string; status?: string; search?: string; isCsv?: any }): Promise<JoiValidationResult> {
    try {
      const schema = Joi.object({
        offeringId: Joi.string().required(), // Validate offeringId as a required string
        page: Joi.number().integer().min(1).default(1).optional(),
        limit: Joi.number().integer().min(1).default(10).optional(),
        status: Joi.string().default('').optional(),
        search: Joi.string().default('').optional(),
        isCsv: Joi.boolean().default(false).optional(),
      });

      const { error, value } = schema.validate(query, joiOptions.options);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'getOrdersfromOfferingIdValidation Error');

      return { error: true, value: '', message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR };
    }
  }
}

export default new OfferingValidation();
