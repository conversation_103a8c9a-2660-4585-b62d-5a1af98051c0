import { Request, Response } from 'express';
import { Types } from 'mongoose';
import { DocumentFolderTypesEnum, offeringStatusEnum, notificationEnum, PromiseResolve } from '../../utils/common.interface';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import CustomError from '../../helpers/customError.helper';
import { ResponseHandler } from '../../helpers/response.helper';
import { IOffering } from './models/offerings.model';
import { userSchema } from '../userAuthentications/models/user.model';
import CommonHelper from '../../helpers/common.helper';
import UserService from '../userAuthentications/service';
import logger from '../../helpers/logging/logger.helper';
import DocuSignApi from '../docuSign/service';
import offeringValidation from '../../component/offerings/validation';
import { IWhitelist, whitelistSchema } from './models/whitelist.model';
import CloudHelper from '../../helpers/cloud.helper';
import OfferingService from './service';
import kafkaService from '../../service/kafkaService';
import { navHistorySchema } from './models/navHistory.model';
import { calculate } from '../../helpers/bigMath';
import { socketHelper } from '../../helpers/socket.helper';
import emailHelper from '../../helpers/email.helper';
import { UserDetailsSchema } from '../userAuthentications/models/userDetails.model';
// import adminClient from '../../_grpc/clients/admin.client';

/**
 * OfferingController handles offering related operations.
 */
class OfferingController {
  /**
   * @export
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise < PromiseResolve >}
   */
  public rejectWalletWhiteList = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { whitelistId } = req.body;
      const updatedStatus = await OfferingService.rejectWalletWhiteList(whitelistId, offeringStatusEnum.REJECTED);

      return ResponseHandler.success(res, {
        status: updatedStatus.status || RESPONSES.SUCCESS,
        error: updatedStatus.error || false,
        message: RES_MSG.USER.OFFERING_SUBSCRIBE_REJECT,
        // data: updatedStatus,
      });
    } catch (error) {
      return ResponseHandler.error(res, { message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true });
    }
  };

  /**
   * @export
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise < PromiseResolve >}
   */

  public getOfferingDetails = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { id } = req.params;
      const { userId } = req.userInfo;
      if (id && !Types.ObjectId.isValid(id)) {
        return ResponseHandler.error(res, { message: RES_MSG.COMMON.BAD_REQUEST, status: RESPONSES.BAD_REQUEST, error: true });
      }

      const offeringId = new Types.ObjectId(id);

      const { error, message, status, data } = await OfferingService.fetchOfferingDetails({ _id: offeringId }, [], [], userId);

      if (error) throw new CustomError(message, status);
      const { _id, overview, projectDetails, documents, team, currentStep, isActive, status: offeringStatus, isSubscribe, isWhitelisted, identityRegistry, tokenAddress, deployedDate, fundAddress, erc20Address, dividends } = data;

      return ResponseHandler.success(res, {
        status: status || RESPONSES.SUCCESS,
        error: false,
        message: message || RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
        data: { _id, overview, projectDetails, documents, team, currentStep, isActive, status: offeringStatus, isSubscribe, isWhitelisted, identityRegistry, tokenAddress, deployedDate, fundAddress, erc20Address, dividends },
      });
    } catch (error: any) {
      logger.error(error, 'getOfferingDetails Error');

      return ResponseHandler.error(res, { message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true });
    }
  };

  /**
   * @export
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise < PromiseResolve >}
   */
  public deleteOffering = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      if (req.params.id && !Types.ObjectId.isValid(req.params.id as string)) {
        return ResponseHandler.error(res, { message: RES_MSG.COMMON.BAD_REQUEST, status: RESPONSES.BAD_REQUEST, error: true });
      }
      const userId = new Types.ObjectId(req.userInfo.userId);
      const offeringId = new Types.ObjectId(req.params.id);
      const offeringDetails: PromiseResolve = await OfferingService.fetchOfferingDetails({ _id: offeringId, userId });
      if (offeringDetails.error) throw new CustomError(offeringDetails.message, offeringDetails.status);

      if (offeringDetails.data?.isTokenDeploy && offeringDetails.data?.isFundDeploy) {
        throw new CustomError(RES_MSG.USER.DELETE_ALREADY_APPROVED_OFFERING, RESPONSES.FORBIDDEN);
      }

      const updateOfferingResp = await OfferingService.updateOfferingDetails({ isDelete: true, status: offeringStatusEnum.DELETED }, { _id: offeringId });
      if (updateOfferingResp.error) {
        throw new CustomError(updateOfferingResp.message || RES_MSG.ERROR_MSG.UPDATE_FAILED, updateOfferingResp.status);
      }

      return ResponseHandler.success(res, { status: offeringDetails.status || RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.DELETE_SUCCESS });
    } catch (error: any) {
      logger.error(error, 'getOfferingDetails Error');

      return ResponseHandler.error(res, { message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true });
    }
  };

  /**
   * @export
   * @param {Request} req
   * @returns {Promise < PromiseResolve >}
   */
  public rejectOffering = async (req: IOffering): Promise<PromiseResolve> => {
    try {
      const { id, status, reason } = req;
      const updateData: any = { status, reason };
      const updateOfferingResp = await OfferingService.updateOfferingDetails(updateData, { _id: id });
      if (updateOfferingResp.error) {
        throw new CustomError(updateOfferingResp.message, updateOfferingResp.status);
      }
      return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.USER.USER_UPDATION_SUCCESS };
    } catch (error) {
      logger.error(error, 'rejectOffering Error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * @export
   * @param {Request} req
   * @returns {Promise < PromiseResolve >}
   */
  public whiteList = async (req: IWhitelist): Promise<PromiseResolve> => {
    try {
      const { _id, whiteListReason } = req;
      // const updateData: any = {
      //   taStatus: taStatus,
      // };
      const updateOfferingResp = await whitelistSchema.findOneAndUpdate({ _id }, { status: offeringStatusEnum.REJECTED, whiteListReason });
      if (!updateOfferingResp) throw new CustomError(RES_MSG.COMMON.NO_USER, RESPONSES.UN_AUTHORIZED);

      // emit socket reject whitelist
      const { userId } = updateOfferingResp;

      socketHelper.sendMessageToUser(userId.toString(), 'reject-whitelist', updateOfferingResp);

      return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.USER.USER_UPDATION_SUCCESS };
    } catch (error) {
      logger.error(error, 'whiteList Error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * @export
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise < PromiseResolve >}
   */
  public getOrdersFromOfferingId = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { page, limit, status, search = '', isCsv } = req.query;
      const userId = new Types.ObjectId(req.userInfo.userId);
      const offeringId = new Types.ObjectId(req.params.offeringId);
      let invitedBy: any = [];
      if (req.userInfo.isRepresentatives) {
        const userDetails: PromiseResolve = await UserService.fetchUserDetails({ _id: req.userInfo.userId }, [], [], true);
        invitedBy = userDetails?.data?.invitedBy.map((id: string) => new Types.ObjectId(id)) || [];
      }

      const orders4Offering = await OfferingService.fetchOrdersFromOffering(userId as unknown as string, offeringId as unknown as string, Number(page), Number(limit), status as string, search as string, isCsv, invitedBy);

      return ResponseHandler.success(res, { error: orders4Offering.error, message: orders4Offering.message, status: orders4Offering.status, data: orders4Offering.data });
    } catch (error: any) {
      logger.error(error, 'getOrdersFromOfferingId Error');

      return ResponseHandler.error(res, { message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true });
    }
  };

  public getOrdersFromOfferingIdCsv = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { status } = req.query; // Retain only the status query parameter
      const userId = new Types.ObjectId(req.userInfo.userId);
      const offeringId = new Types.ObjectId(req.params.offeringId);

      // Call the service to fetch orders without search or pagination
      const orders4Offering = await OfferingService.fetchOrdersFromOfferingCsv(
        userId as unknown as string,
        offeringId as unknown as string,
        status as string, // Only passing the status filter
      );

      return ResponseHandler.success(res, {
        error: orders4Offering.error,
        message: orders4Offering.message,
        status: orders4Offering.status,
        data: orders4Offering.data,
      });
    } catch (error: any) {
      logger.error(error, 'getOrdersFromOfferingId Error');

      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };

  /**
   * @export
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise < PromiseResolve >}
   */
  public createOffering = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { userId: userIdStr, email } = req.userInfo;
      const { offeringId: offeringIdRaw, ...offeringDetails } = req.body;
      const offeringId = offeringIdRaw ? new Types.ObjectId(offeringIdRaw) : null;
      const userId = new Types.ObjectId(userIdStr);

      let updateOfferingResp: PromiseResolve;
      let currentOfferingResp: PromiseResolve;

      if (offeringId) {
        // Fetch current offering if offeringId exists
        currentOfferingResp = await OfferingService.fetchOfferingDetails({ _id: offeringId, userId });
        if (currentOfferingResp.error) {
          throw new CustomError(currentOfferingResp.message, currentOfferingResp.status);
        }

        const { data: currentOfferingData } = currentOfferingResp;
        const { currentStep = 0, status } = currentOfferingData;
        const requestedStep = offeringDetails.currentStep;
        const updateData = { ...offeringDetails };

        // Step-based validation
        if (requestedStep === currentStep + 1) {
          updateData.currentStep = requestedStep;
        } else if (requestedStep <= currentStep) {
          delete updateData.currentStep;
        } else {
          return ResponseHandler.error(res, { status: RESPONSES.BAD_REQUEST, error: true, message: `Invalid step progression. Current step: ${currentStep}. Proceed to step ${currentStep + 1}.` });
        }

        // Status validations
        if (status === offeringStatusEnum.APPROVED) {
          throw new CustomError(RES_MSG.USER.ALREADY_APPROVED_OFFERING, RESPONSES.CONFLICT);
        }
        if (status === offeringStatusEnum.PENDING) {
          throw new CustomError(RES_MSG.USER.ALREADY_PENDING_OFFERING, RESPONSES.CONFLICT);
        }

        // Merge and validate fields
        const { projectDetails } = offeringDetails;
        const { projectDetails: currentProjectDetails = {} } = currentOfferingData;

        if (projectDetails?.isPrivate && projectDetails?.offeringMembers) {
          projectDetails.offeringMembers = CommonHelper.mergeWithoutDuplicates(projectDetails.offeringMembers, currentProjectDetails.offeringMembers || []);
        }

        if (projectDetails?.offeringMembers?.includes(email)) {
          throw new CustomError(RES_MSG.ERROR_MSG.OFFERING_OWN_EMAIL, RESPONSES.BAD_REQUEST);
        }

        // if (offeringDetails?.team) {
        //   offeringDetails.team = CommonHelper.mergeWithoutDuplicates(
        //     offeringDetails.team,
        //     currentOfferingData.team || [],
        //     ['email']
        //   );
        // }

        if (projectDetails?.customFields) {
          projectDetails.customFields = CommonHelper.mergeWithoutDuplicates(projectDetails.customFields, currentProjectDetails.customFields || [], ['label', 'type']);
        }

        // if (offeringDetails?.documents?.customDocs) {
        //   offeringDetails.documents.customDocs = CommonHelper.mergeWithoutDuplicates(
        //     offeringDetails.documents.customDocs,
        //     currentOfferingData.documents?.customDocs || [],
        //     ['docsLabel']
        //   );
        // }

        if (offeringDetails?.documents) {
          req.body.documents.assetType = currentProjectDetails.assetType;

          const validateRequest = await offeringValidation.createOfferingsValidation({ ...req.body });
          if (validateRequest.error) {
            throw new CustomError(validateRequest.message, validateRequest.status);
          }
          offeringDetails.documents = validateRequest.value.documents;
        }
        if (projectDetails && !projectDetails?.isTransferAgent) {
          projectDetails.taId = '';
        }

        // Update the offering details
        updateOfferingResp = await OfferingService.updateOfferingDetails(updateData, { _id: offeringId });
        if (updateOfferingResp.error) {
          throw new CustomError(updateOfferingResp.message || RES_MSG.ERROR_MSG.UPDATE_FAILED, updateOfferingResp.status);
        }

        if (offeringDetails.isFinalSubmission) {
          const docuSignTemplateId = await DocuSignApi.generateDocuSignTemplateId(offeringId.toString());
          const finalData = await OfferingService.updateOfferingDetails({ status: offeringStatusEnum.PENDING, template_id: docuSignTemplateId }, { _id: offeringId });
          // send message to notification topic
          await kafkaService.sendMessageToNotification({
            value: JSON.stringify({
              type: 'create-offering',
              details: finalData,
            }),
          });
          // Save NAV history
          const nav = currentOfferingData.projectDetails?.navLaunchPrice || currentOfferingData.projectDetails?.launchValuation;
          const price = calculate('div', nav, currentOfferingData.projectDetails?.tokenSupply) || 0;
          await navHistorySchema.create({
            price,
            txHash: offeringIdRaw,
            navAmount: nav,
            offeringId,
          });
        }
      } else {
        if (offeringDetails?.taId && (await userSchema.exists({ _id: offeringDetails.taId }))) {
          throw new CustomError(RES_MSG.ERROR_MSG.TA_EXIST, RESPONSES.BAD_REQUEST);
        }
        // If no offeringId, insert new offering
        updateOfferingResp = await OfferingService.createOffering({ ...offeringDetails, userId, currentStep: 0, createdBy: userId });

        if (updateOfferingResp.error) {
          throw new CustomError(updateOfferingResp.message || RES_MSG.ERROR_MSG.UPDATE_FAILED, updateOfferingResp.status);
        }
      }

      return ResponseHandler.success(res, { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.OFFERING_CREATE_SUCCESS, data: updateOfferingResp.data });
    } catch (error) {
      logger.error(error, 'createOffering Error');

      return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
    }
  };

  /**
   * @export
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise < PromiseResolve >}
   */
  public updateOffering = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const offeringDetails: IOffering = req.body;
      const userId = new Types.ObjectId(req.userInfo.userId);
      const offeringId = req.body.offeringId ? new Types.ObjectId(req.body.offeringId) : null;
      const { email } = req.userInfo;

      // Fetch current offering if offeringId exists
      const currentOffering = await OfferingService.fetchOfferingDetails({ _id: offeringId, userId });
      if (currentOffering.error) throw new CustomError(currentOffering.message, currentOffering.status);

      if (currentOffering.data?.status === offeringStatusEnum.APPROVED || currentOffering.data?.status === offeringStatusEnum.PENDING) {
        if (offeringDetails?.projectDetails?.offeringMembers && offeringDetails?.projectDetails?.offeringMembers.includes(email)) {
          throw new CustomError(RES_MSG.ERROR_MSG.OFFERING_OWN_EMAIL, RESPONSES.BAD_REQUEST);
        } else if (offeringDetails?.projectDetails?.offeringMembers && !offeringDetails?.projectDetails?.isPrivate) {
          offeringDetails.projectDetails.offeringMembers = currentOffering.data?.projectDetails.offeringMembers || [];
        } else if (offeringDetails?.projectDetails?.offeringMembers) {
          offeringDetails.projectDetails.offeringMembers = CommonHelper.mergeWithoutDuplicates(offeringDetails?.projectDetails?.offeringMembers, currentOffering.data?.projectDetails.offeringMembers || [], []);
        }

        // if (offeringDetails?.team) {
        //   offeringDetails.team = CommonHelper.mergeWithoutDuplicates(offeringDetails.team, currentOffering.data?.team || [], ['key']);
        // }

        if (offeringDetails?.projectDetails?.customFields) {
          offeringDetails.projectDetails.customFields = CommonHelper.mergeWithoutDuplicates(offeringDetails.projectDetails.customFields, currentOffering.data?.projectDetails?.customFields || [], ['label', 'type']);
        }
        const updateOfferingResp = await OfferingService.updateOfferingDetails(offeringDetails, { _id: offeringId });
        if (updateOfferingResp.error) {
          throw new CustomError(updateOfferingResp.message, updateOfferingResp.status);
        }

        return ResponseHandler.success(res, { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.OFFERING_UPDATION_SUCCESS, data: updateOfferingResp.data });
      }
      throw new CustomError(RES_MSG.COMMON.BAD_REQUEST, RESPONSES.BAD_REQUEST);
    } catch (error) {
      logger.error(error, 'createOffering Error');

      return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
    }
  };

  /**
   * @export
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise < PromiseResolve >}
   */
  public getUserOfferingList = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { page = 1, limit = 10, sort = '', search = '', status = '', iserc20, isDeleted, isCsv = 'false' } = req.query;
      const userId = new Types.ObjectId(req.userInfo.userId);

      let invitedBy: any = [];
      if (req.userInfo.isRepresentatives) {
        const userDetails: PromiseResolve = await UserService.fetchUserDetails({ _id: req.userInfo.userId }, [], [], true);
        invitedBy = userDetails?.data?.invitedBy.map((id: string) => new Types.ObjectId(id)) || [];
      }

      const filters = { ...(status && { status }), ...{ userId }, ...{ iserc20 }, ...{ isDeleted }, ...{ invitedBy } };
      const result: PromiseResolve = await OfferingService.fetchOfferingList(filters, [''], { page: Number(page), limit: Number(limit), ...(search && { search }), ...(sort && { sort }) }, isCsv);

      return ResponseHandler.success(res, { status: result.status || RESPONSES.SUCCESS, error: false, message: result.message || RES_MSG.USER.USERS_FETCH, data: result.data || [] });
    } catch (error) {
      logger.error(error, 'getUserOfferingList Error');

      return ResponseHandler.error(res, { message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true });
    }
  };

  /**
   * @export
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise < PromiseResolve >}
   */
  public getNavHistory = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { page = 1, limit = 10 } = req.query;
      const offeringId = new Types.ObjectId(req.params.id);
      const filters = { offeringId };
      const result: PromiseResolve = await OfferingService.fetchNavHistory(filters, [''], { page: Number(page), limit: Number(limit) });

      return ResponseHandler.success(res, { status: result.status || RESPONSES.SUCCESS, error: false, message: result.message || RES_MSG.USER.USERS_FETCH, data: result.data || [] });
    } catch (error) {
      logger.error(error, 'getUserOfferingList Error');

      return ResponseHandler.error(res, { message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true });
    }
  };

  /**
   * @export
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise < PromiseResolve >}
   */
  public primaryMarketOfferings = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { page = 1, limit = 10, sort = '', search = '', status = '', iserc20, projectedYield, assetType } = req.query;
      const userId = new Types.ObjectId(req.userInfo.userId);
      const { email } = req.userInfo;
      const filters = { ...(status && { status }), ...{ iserc20 }, ...{ userId }, ...{ projectedYield }, ...{ assetType }, email };
      const projection = [
        'overview.title',
        'overview.subTitle',
        'overview.icon',
        'overview.logo',
        'overview.cover',
        'projectDetails.assetName',
        'projectDetails.blockChainType',
        'projectDetails.assetType',
        'projectDetails.offeringName',
        'projectDetails.minInvestment',
        'projectDetails.projectedYield',
        'projectDetails.navLaunchPrice',
        'projectDetails.tokenSupply',
        'projectDetails.latestNav',
        'projectDetails.startDate',
        'projectDetails.endDate',
        'projectDetails.isPrivate',
        'tokenAddress',
        'fundAddress',
        'isSubscribe',
        'isWhitelisted',
        'deployedDate',
        'projectDetails.launchValuation',
        'projectDetails.previousValuation',
        'projectDetails.deRatio',
      ];

      const userDetails: PromiseResolve = await OfferingService.offeringList(filters, projection, { page: Number(page), limit: Number(limit), ...(search && { search }), ...(sort && { sort }) });

      return ResponseHandler.success(res, { status: userDetails.status || RESPONSES.SUCCESS, error: false, message: userDetails.message || RES_MSG.USER.USERS_FETCH, data: userDetails.data || [] });
    } catch (error) {
      logger.error(error, 'primaryMarketOfferings Error');

      return ResponseHandler.error(res, { message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true });
    }
  };

  /**
   * @export
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise < PromiseResolve >}
   */
  public requestOffering = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const userId = new Types.ObjectId(req.userInfo.userId);
      const { error, message, status } = await OfferingService.requestOffering({ userId });

      if (error) throw new CustomError(message, status);

      return ResponseHandler.success(res, { status: RESPONSES.CREATED, error: false, message: RES_MSG.COMMON.REQ_SENT });
    } catch (error) {
      logger.error(error, 'requestOffering Error');

      return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
    }
  };

  /**
   * @export
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise < PromiseResolve >}
   */
  public requestedOfferings = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { page = 1, limit = 10, sort = '', search = '', status = '' } = req.query;
      const userId = new Types.ObjectId(req.userInfo.userId);

      const filters = { ...(status && { status }), userId };

      const userDetails: PromiseResolve = await OfferingService.requestedOfferings(filters, { page: Number(page), limit: Number(limit), ...(search && { search }), ...(sort && { sort }) });

      return ResponseHandler.success(res, { status: userDetails.status || RESPONSES.SUCCESS, error: false, message: userDetails.message || RES_MSG.USER.USERS_FETCH, data: userDetails.data || [] });
    } catch (error) {
      logger.error(error, 'requestedOfferings Error');

      return ResponseHandler.error(res, { message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true });
    }
  };

  /**
   * @export
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise < PromiseResolve >}
   */
  public subscribe = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { userId: rowUserId } = req.userInfo; // Destructure user ID from req.userInfo
      const { offeringId: rowOfferingId, address: rawAddress } = req.body; // Destructure offeringId and address from req.body
      const address = rawAddress.toLowerCase();
      const offeringId = new Types.ObjectId(rowOfferingId);
      const userId = new Types.ObjectId(rowUserId);

      const { data: userData } = await UserService.fetchUserDetails({ _id: userId }, [], [], true);
      const { kycDetails } = userData;
      const { wallets: userWallets = [] } = kycDetails || {};
      const userCountryCode = kycDetails?.primaryContactInfo?.personalInformation?.citizenship || kycDetails?.mainInformation?.nationality;

      // Check if the user's wallet address is connected
      const wallet = userWallets.find(({ address: walletAddress }: { address: string }) => walletAddress === address);
      if (!wallet) {
        throw new CustomError(RES_MSG.ERROR_MSG.WALLET_NOT_CONNECTED, RESPONSES.BAD_REQUEST);
      }

      // Fetch offering details
      const { data: offeringData, error: offeringError, message: offeringMessage, status: offeringStatus } = await OfferingService.fetchOfferingDetails({ _id: offeringId });

      if (offeringError) throw new CustomError(offeringMessage, offeringStatus);

      const { userId: offeringOwnerId, status: offeringStatusEnumValue, projectDetails } = offeringData;

      // Prevent self-subscription
      if (offeringOwnerId.toString() === userId.toString()) {
        throw new CustomError(RES_MSG.ERROR_MSG.OFFERING_OWN_SUBS, RESPONSES.FORBIDDEN);
      }

      // Ensure offering status is valid
      if (offeringStatusEnumValue !== offeringStatusEnum.APPROVED) {
        throw new CustomError(RES_MSG.COMMON.FORBIDDEN_ACCESS, RESPONSES.FORBIDDEN);
      }
      const { authorizedCountries = [], isAuthorized } = projectDetails || {};
      const isCountryAuthorized = (code: any) => authorizedCountries?.some((country: { isoCode: any }) => country?.isoCode === code);

      if (isAuthorized) {
        if (authorizedCountries?.length > 0 && !isCountryAuthorized(userCountryCode)) {
          throw new CustomError(RES_MSG.USER.OFFERING_UNAUTHORIZED_COUNTRY, RESPONSES.FORBIDDEN);
        }
      } else if (authorizedCountries?.length > 0 && isCountryAuthorized(userCountryCode)) {
        throw new CustomError(RES_MSG.USER.OFFERING_UNAUTHORIZED_COUNTRY, RESPONSES.FORBIDDEN);
      }
      // Check if the user is already subscribed
      const { error: subscriberError } = await OfferingService.fetchSubscriberDetails({ offeringId, userId });
      if (!subscriberError) throw new CustomError(RES_MSG.COMMON.ALREADY_EXIST, RESPONSES.CONFLICT);

      // Add user to the whitelist
      const { error: whitelistError, message: whitelistMessage, status: whitelistStatus } = await OfferingService.createWhitelist({ userId, address, offeringId, status: offeringStatusEnum.PENDING });

      if (whitelistError) throw new CustomError(whitelistMessage, whitelistStatus);
      const user = await userSchema.findOne({ _id: userId });
      const investorName = user?.name;
      const investorEmail = user?.email;
      // const investorWalletAddress = user;
      const investordetail = await UserDetailsSchema.findOne({ _id: userId });
      const investorWalletAddress = investordetail.wallets[0].address;
      const issuerId = offeringData?.userId;
      const { name, email } = await userSchema.findOne({ _id: issuerId });
      const detail = {
        investorName: investorName,
        issuerName: name,
        offeringName: projectDetails?.offeringName,
        investorEmail: investorEmail,
        offeringId: offeringId,
        investorWalletAddress,
      };
      emailHelper.sendEmailTemplate(email, 'walletWhiteListRequest', detail);
      // send this notification to kafka topic
      await kafkaService.sendMessageToNotification({
        value: JSON.stringify({
          type: notificationEnum.WALLET_WHITELIST,
          details: {
            userAddress: address,
            offeringId: offeringId,
          },
        }),
      });

      // Return success response
      return ResponseHandler.success(res, { status: RESPONSES.CREATED, error: false, message: RES_MSG.USER.OFFERING_SUBSCRIBE_SUCCESS });
    } catch (error) {
      logger.error(error, 'subscribe Error');

      return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
    }
  };

  /**
   * @export
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise < PromiseResolve >}
   */
  public getOfferingSubscriberList = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { page = 1, limit = 10, sort, status = offeringStatusEnum.APPROVED, search = '', isCsv = 'false' } = req.query;
      if (req.params.id && !Types.ObjectId.isValid(req.params.id as string)) {
        return ResponseHandler.error(res, { message: RES_MSG.COMMON.BAD_REQUEST, status: RESPONSES.BAD_REQUEST, error: true });
      }
      const offeringId = new Types.ObjectId(req.params.id);
      const userId = new Types.ObjectId(req.userInfo.userId);

      let invitedBy: any = [];
      if (req.userInfo.isRepresentatives) {
        const userDetails: PromiseResolve = await UserService.fetchUserDetails({ _id: req.userInfo.userId }, [], [], true);
        invitedBy = userDetails?.data?.invitedBy.map((id: string) => new Types.ObjectId(id)) || [];
      }

      const filters = { ...(status && { status }), ...{ userId, offeringId, invitedBy } };
      const subscriberList: PromiseResolve = await OfferingService.getOfferingSubscriberList(filters, { page: Number(page), limit: Number(limit), ...(search && { search }), ...(sort && { sort: JSON.parse(sort as string) }) }, isCsv);

      return ResponseHandler.success(res, { status: subscriberList.status || RESPONSES.SUCCESS, error: false, message: subscriberList.message || RES_MSG.USER.USERS_FETCH, data: subscriberList.data || [] });
    } catch (error) {
      logger.error(error, 'getOfferingList Error');

      return ResponseHandler.error(res, { message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true });
    }
  };

  /**
   * @export
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise < PromiseResolve >}
   */
  public duplicateOffering = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      if (req.params.offeringId && !Types.ObjectId.isValid(req.params.offeringId as string)) {
        return ResponseHandler.error(res, { message: RES_MSG.COMMON.BAD_REQUEST, status: RESPONSES.BAD_REQUEST, error: true });
      }

      const offeringId = new Types.ObjectId(req.params.offeringId);
      const newOfferingId = new Types.ObjectId();
      const userId = new Types.ObjectId(req.userInfo.userId);

      // Fetch offering details
      const offeringDetails: PromiseResolve = await OfferingService.fetchOfferingDetails({ _id: offeringId, userId });
      if (offeringDetails.error) throw new CustomError(offeringDetails.message, offeringDetails.status);

      // Prepare data for the new offering
      const { data } = offeringDetails;
      data.projectDetails.CUSIP = `valuit${await CommonHelper.generateRandomKey()}`;
      data.projectDetails.tokenTicker = `valuit${await CommonHelper.generateRandomKey()}`;
      delete data.projectDetails?.navLaunchPrice;
      delete data.projectDetails?.latestNav;
      delete data.projectDetails?.launchValuation;
      delete data.projectDetails?.previousValuation;

      // Copy related files
      const copyOfferingImg = await CloudHelper.copyFolder(offeringId.toString(), newOfferingId.toString(), userId.toString(), DocumentFolderTypesEnum.OFFERING);
      if (copyOfferingImg.error && copyOfferingImg.status !== RESPONSES.NOTFOUND) throw new CustomError(copyOfferingImg.message, copyOfferingImg.status);
      // Replace old offeringId with newOfferingId dynamically in all fields
      const offeringData = {
        _id: newOfferingId,
        overview: data.overview,
        projectDetails: data.projectDetails,
        documents: data.documents,
        team: data.team,
        currentStep: data.currentStep,
        isActive: data.isActive,
        status: offeringStatusEnum.IN_PROGRESS,
        userId,
        createdBy: userId,
      };

      // Replace offeringId wherever it appears in the data
      CommonHelper.replaceIdsInObject(offeringData, offeringId.toString(), newOfferingId.toString()); // Create the new offering
      const createOffering = await OfferingService.createOffering(offeringData);

      return ResponseHandler.success(res, { status: RESPONSES.CREATED, error: createOffering.error || false, message: createOffering.message || RES_MSG.SUCCESS_MSG.CREATE_SUCCESS, data: createOffering.data || {} });
    } catch (error: any) {
      logger.error(error, 'duplicateOffering Error');

      return ResponseHandler.error(res, { message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true });
    }
  };

  /**
   * @export
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise < PromiseResolve >}
   */
  public getTotalCount = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const userId = new Types.ObjectId(req.userInfo.userId);
      const result: PromiseResolve = await OfferingService.getTotalCount({ userId });
      if (result.error) throw new CustomError(result.message, result.status);

      return ResponseHandler.success(res, { status: result.status || RESPONSES.SUCCESS, error: false, message: result.message || RES_MSG.USER.USERS_FETCH, data: result.data || [] });
    } catch (error) {
      logger.error(error, 'getTotalCount Error');

      return ResponseHandler.error(res, { message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true });
    }
  };

  /**
   * @export
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise < PromiseResolve >}
   */
  public getTopOffering = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { type } = req.body;
      const userId = new Types.ObjectId(req.userInfo.userId);
      const { page = 1, limit = 5, sort, search = '' } = req.query;
      const result: PromiseResolve = await OfferingService.getTopOffering({ userId }, { page: Number(page), limit: Number(limit), ...(search && { search }), ...(sort && { sort: JSON.parse(sort as string) }) }, type);
      if (result.error) throw new CustomError(result.message, result.status);

      return ResponseHandler.success(res, { status: result.status || RESPONSES.SUCCESS, error: false, message: result.message || RES_MSG.USER.USERS_FETCH, data: result.data || [] });
    } catch (error) {
      logger.error(error, 'getTotalCount Error');

      return ResponseHandler.error(res, { message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true });
    }
  };

  /**
   * @export
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise < PromiseResolve >}
   */
  public investedOffering = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const userId = new Types.ObjectId(req.userInfo.userId);
      const { page = 1, limit = 10, sort, search = '' } = req.query;
      const result: PromiseResolve = await OfferingService.investedOffering({ userId }, { page: Number(page), limit: Number(limit), ...(search && { search }), ...(sort && { sort: JSON.parse(sort as string) }) });
      if (result.error) throw new CustomError(result.message, result.status);

      return ResponseHandler.success(res, { status: result.status || RESPONSES.SUCCESS, error: false, message: result.message || RES_MSG.USER.USERS_FETCH, data: result.data || [] });
    } catch (error) {
      logger.error(error, 'investedOffering Error');

      return ResponseHandler.error(res, { message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true });
    }
  };

  /**
   * @export
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise < PromiseResolve >}
   */
  public singleOfferingReport = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const userId = new Types.ObjectId(req.userInfo.userId);
      const offeringId = new Types.ObjectId(req.params.offeringId);
      const result: PromiseResolve = await OfferingService.singleOfferingReport({ userId, offeringId });
      if (result.error) throw new CustomError(result.message, result.status);

      return ResponseHandler.success(res, { status: result.status || RESPONSES.SUCCESS, error: false, message: result.message || RES_MSG.USER.USERS_FETCH, data: result.data || [] });
    } catch (error) {
      logger.error(error, 'singleOfferingReport Error');

      return ResponseHandler.error(res, { message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true });
    }
  };

  /**
   * @export
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise < PromiseResolve >}
   */
  public offeringReport = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const userId = new Types.ObjectId(req.userInfo.userId);
      const { offeringId, period } = req.body;

      await Promise.all(
        offeringId.map(async (id: string) => {
          const response: PromiseResolve = await OfferingService.fetchOfferingDetails({ _id: new Types.ObjectId(id), userId });

          if (response.error) throw new CustomError(response.message, response.status);
        }),
      );

      const result: PromiseResolve = await OfferingService.offeringReport({ userId, offeringId, period });
      if (result.error) throw new CustomError(result.message, result.status);

      return ResponseHandler.success(res, { status: result.status || RESPONSES.SUCCESS, error: false, message: result.message || RES_MSG.USER.USERS_FETCH, data: result.data || [] });
    } catch (error) {
      logger.error(error, 'offeringReport Error');

      return ResponseHandler.error(res, { message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true });
    }
  };
}

export default new OfferingController();
