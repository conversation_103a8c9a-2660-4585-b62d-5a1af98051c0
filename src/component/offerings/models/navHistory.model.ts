import mongoose, { Schema, Document, Types } from 'mongoose';

export interface IWhitelist extends Document {
  offeringId: Types.ObjectId | string;
  price?: string;
  navAmount: number;
  txHash: string;
  createdAt?: Date;
  updatedAt?: Date;
}

const navHistory = new Schema<IWhitelist>(
  {
    offeringId: { type: Schema.Types.ObjectId, ref: 'offerings', required: true },
    price: { type: String, default: null },
    navAmount: { type: Number, required: true },
    txHash: { type: String, required: true, unique: true },
  },
  { timestamps: true, versionKey: false },
);

const navHistorySchema = mongoose.model<IWhitelist>('navHistory', navHistory);

export { navHistorySchema };
