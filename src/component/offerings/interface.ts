import { FilterQuery, Types } from 'mongoose';
import { IPagination, PromiseResolve } from '../../utils/common.interface';
import { IOffering, IUpdateOffering } from './models/offerings.model';
import { <PERSON>Up<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from './models/whitelist.model';
import { IRequestedOffering } from './models/requestedOfferings.model';

/**
 * Interface defining the contract for asset tokenization and offering management services.
 * Provides comprehensive methods for offering lifecycle management, whitelist operations,
 * investor management, NAV tracking, and reporting functionality for tokenized assets.
 *
 * @interface IOfferingsService
 * @description Service interface for complete offering and tokenization platform management
 */
export interface IOfferingsService {
  /**
   * Retrieves detailed information about specific offerings based on search criteria.
   * Supports flexible field selection and user-specific data filtering.
   *
   * @method fetchOfferingDetails
   * @param {FilterQuery<IOffering>} searchDetails - MongoDB query filter for offering lookup
   * @param {string[]} [fields] - Optional array of specific fields to include in response
   * @param {string[]} [excludeFields] - Optional array of fields to exclude from response
   * @param {string} [userId] - Optional user ID for user-specific offering data
   * @returns {Promise<PromiseResolve>} Promise resolving to offering details with optional user context
   * @memberof IOfferingsService
   *
   * @description
   * Supports lookup by:
   * - Offering ID, name, or status
   * - Asset type and category filtering
   * - User-specific offering access
   * - Custom field projection for optimized queries
   */
  fetchOfferingDetails(searchDetails: FilterQuery<IOffering>, fields?: string[], excludeFields?: string[], userId?: string): Promise<PromiseResolve>;

  /**
   * Updates offering information including project details, status, and configuration.
   * Handles comprehensive offering lifecycle management with validation and notifications.
   *
   * @method updateOfferingDetails
   * @param {IUpdateOffering} body - Updated offering data including project details, pricing, and status
   * @param {FilterQuery<IOffering>} filter - MongoDB filter to identify the offering to update
   * @returns {Promise<PromiseResolve>} Promise resolving to updated offering data and operation status
   * @memberof IOfferingsService
   *
   * @description
   * Update capabilities include:
   * - Project details and asset information
   * - Pricing and valuation updates
   * - Status transitions and lifecycle management
   * - Fee structure modifications
   * - Regulatory compliance updates
   */
  updateOfferingDetails(body: IUpdateOffering, filter: FilterQuery<IOffering>): Promise<PromiseResolve>;

  /**
   * Updates whitelist entries for investor access control and compliance management.
   * Manages investor eligibility, approval status, and wallet address validation.
   *
   * @method updateWhitelistDetails
   * @param {IUpdateWhitelist} body - Whitelist data including status, approval details, and wallet addresses
   * @param {FilterQuery<IWhitelist>} filter - MongoDB filter to identify the whitelist entry to update
   * @returns {Promise<PromiseResolve>} Promise resolving to updated whitelist status and details
   * @memberof IOfferingsService
   *
   * @description
   * Whitelist management includes:
   * - Approval status updates (PENDING, APPROVED, REJECTED)
   * - Wallet address validation and updates
   * - Compliance verification tracking
   * - Access control enforcement
   */
  updateWhitelistDetails(body: IUpdateWhitelist, filter: FilterQuery<IWhitelist>): Promise<PromiseResolve>;

  /**
   * Creates a new tokenized offering with complete project setup and configuration.
   * Establishes offering structure, pricing, compliance, and investor access parameters.
   *
   * @method createOffering
   * @param {IUpdateOffering} body - Complete offering creation data including project details, asset info, and configuration
   * @returns {Promise<PromiseResolve>} Promise resolving to created offering data and setup confirmation
   * @memberof IOfferingsService
   *
   * @description
   * Offering creation includes:
   * - Project and asset detail configuration
   * - Token economics and pricing setup
   * - Compliance and regulatory parameter definition
   * - Investor access and whitelist initialization
   * - Fee structure and revenue sharing setup
   */
  createOffering(body: IUpdateOffering): Promise<PromiseResolve>;

  /**
   * Retrieves paginated list of offerings with filtering and search capabilities.
   * Supports comprehensive offering discovery with status filtering and export options.
   *
   * @method fetchOfferingList
   * @param {IUpdateOffering} searchDetails - Search criteria for offering filtering
   * @param {string[]} [projection] - Optional fields to include in the response
   * @param {IPagination} [pagination] - Pagination parameters including page, limit, and search
   * @param {any} [isCsv] - Flag for CSV export format
   * @returns {Promise<PromiseResolve>} Promise resolving to paginated offering list with metadata
   * @memberof IOfferingsService
   *
   * @description
   * List features include:
   * - Status-based filtering (ACTIVE, PENDING, COMPLETED)
   * - Asset type and category filtering
   * - Search by name, description, or ticker
   * - Pagination with total count and navigation
   * - CSV export for reporting and analysis
   */
  fetchOfferingList(searchDetails: IUpdateOffering, projection?: string[], pagination?: IPagination, isCsv?: any): Promise<PromiseResolve>;

  /**
   * Retrieves Net Asset Value (NAV) history for performance tracking and reporting.
   * Provides historical valuation data with date range filtering and pagination.
   *
   * @method fetchNavHistory
   * @param {any} searchDetails - Search criteria for NAV history filtering including date ranges
   * @param {string[]} [projection] - Optional fields to include in the response
   * @param {IPagination} [pagination] - Pagination parameters for large datasets
   * @returns {Promise<PromiseResolve>} Promise resolving to NAV history data with performance metrics
   * @memberof IOfferingsService
   *
   * @description
   * NAV history features:
   * - Historical valuation tracking
   * - Date range filtering for specific periods
   * - Performance metrics calculation
   * - Trend analysis support
   * - Export capabilities for reporting
   */
  fetchNavHistory(searchDetails: any, projection?: string[], pagination?: IPagination): Promise<PromiseResolve>;

  /**
   * Retrieves public offering list for investor discovery and marketplace display.
   * Provides filtered view of available offerings with public information.
   *
   * @method offeringList
   * @param {any} searchDetails - Public search criteria for offering discovery
   * @param {string[]} [projection] - Optional fields to include in public view
   * @param {IPagination} [pagination] - Pagination for offering discovery
   * @returns {Promise<PromiseResolve>} Promise resolving to public offering list
   * @memberof IOfferingsService
   *
   * @description
   * Public offering features:
   * - Investor-friendly offering discovery
   * - Public information filtering
   * - Category and asset type browsing
   * - Investment opportunity presentation
   */
  offeringList(searchDetails: any, projection?: string[], pagination?: IPagination): Promise<PromiseResolve>;

  /**
   * Processes new offering requests from asset sponsors and institutional clients.
   * Handles initial offering submission with validation and approval workflow.
   *
   * @method requestOffering
   * @param {IRequestedOffering} body - Offering request data including asset details and sponsor information
   * @returns {Promise<PromiseResolve>} Promise resolving to request submission confirmation and next steps
   * @memberof IOfferingsService
   *
   * @description
   * Request processing includes:
   * - Asset and project detail validation
   * - Sponsor credential verification
   * - Regulatory compliance initial check
   * - Approval workflow initiation
   * - Notification to administrators
   */
  requestOffering(body: IRequestedOffering): Promise<PromiseResolve>;

  /**
   * Retrieves list of requested offerings awaiting approval or review.
   * Provides administrative view of pending offering submissions with filtering.
   *
   * @method requestedOfferings
   * @param {any} filters - Filter criteria for requested offering lookup
   * @param {IPagination} pagination - Pagination parameters for administrative review
   * @returns {Promise<PromiseResolve>} Promise resolving to requested offerings list with status
   * @memberof IOfferingsService
   *
   * @description
   * Administrative features:
   * - Pending request management
   * - Status-based filtering
   * - Priority and date sorting
   * - Bulk approval capabilities
   */
  requestedOfferings(filters: any, pagination: IPagination): Promise<PromiseResolve>;

  /**
   * Creates whitelist entries for investor access control and compliance management.
   * Establishes investor eligibility and wallet address verification.
   *
   * @method createWhitelist
   * @param {IUpdateWhitelist} body - Whitelist creation data including investor details and wallet addresses
   * @returns {Promise<PromiseResolve>} Promise resolving to whitelist creation confirmation
   * @memberof IOfferingsService
   *
   * @description
   * Whitelist creation includes:
   * - Investor eligibility verification
   * - Wallet address validation
   * - Compliance status initialization
   * - Access permission setup
   */
  createWhitelist(body: IUpdateWhitelist): Promise<PromiseResolve>;

  /**
   * Retrieves subscriber details for offering participant management.
   * Provides comprehensive view of investors and their participation status.
   *
   * @method fetchSubscriberDetails
   * @param {FilterQuery<IOffering>} searchDetails - Search criteria for subscriber lookup
   * @param {string[]} [fields] - Optional fields to include in subscriber data
   * @param {string[]} [excludeFields] - Optional fields to exclude from subscriber data
   * @returns {Promise<PromiseResolve>} Promise resolving to subscriber details and participation info
   * @memberof IOfferingsService
   *
   * @description
   * Subscriber management features:
   * - Investor profile and status tracking
   * - Investment amount and position details
   * - Communication preferences
   * - Compliance and verification status
   */
  fetchSubscriberDetails(searchDetails: FilterQuery<IOffering>, fields?: string[], excludeFields?: string[]): Promise<PromiseResolve>;

  /**
   * Retrieves paginated list of offering subscribers with comprehensive investor data.
   * Supports administrative and sponsor view of investor participation.
   *
   * @method getOfferingSubscriberList
   * @param {any} searchDetails - Search criteria for subscriber filtering
   * @param {any} isCsv - Flag for CSV export format
   * @param {IPagination} [pagination] - Pagination parameters for large subscriber lists
   * @returns {Promise<PromiseResolve>} Promise resolving to subscriber list with participation metrics
   * @memberof IOfferingsService
   *
   * @description
   * Subscriber list features:
   * - Comprehensive investor data
   * - Investment tracking and metrics
   * - Communication and notification status
   * - Export capabilities for investor relations
   */
  getOfferingSubscriberList(searchDetails: any, isCsv: any, pagination?: IPagination): Promise<PromiseResolve>;

  /**
   * Retrieves total count metrics for offerings with various filtering options.
   * Provides aggregated statistics for dashboard and reporting purposes.
   *
   * @method getTotalCount
   * @param {IUpdateOffering} searchDetails - Filter criteria for count calculation
   * @returns {Promise<PromiseResolve>} Promise resolving to count metrics and statistics
   * @memberof IOfferingsService
   *
   * @description
   * Count metrics include:
   * - Total offerings by status and category
   * - Investment volume and participation
   * - Performance and completion statistics
   * - Time-based trend analysis
   */
  getTotalCount(searchDetails: IUpdateOffering): Promise<PromiseResolve>;

  /**
   * Generates comprehensive single offering performance report with detailed analytics.
   * Provides in-depth analysis of individual offering performance and metrics.
   *
   * @method singleOfferingReport
   * @param {IUpdateOffering} searchDetails - Criteria for specific offering report generation
   * @returns {Promise<PromiseResolve>} Promise resolving to detailed offering performance report
   * @memberof IOfferingsService
   *
   * @description
   * Report includes:
   * - Financial performance and ROI analysis
   * - Investor participation and demographics
   * - Transaction history and volume
   * - Compliance and regulatory status
   */
  singleOfferingReport(searchDetails: IUpdateOffering): Promise<PromiseResolve>;

  /**
   * Generates comprehensive multi-offering performance report with portfolio analytics.
   * Provides aggregated analysis across multiple offerings for platform overview.
   *
   * @method offeringReport
   * @param {IUpdateOffering} searchDetails - Criteria for multi-offering report generation
   * @returns {Promise<PromiseResolve>} Promise resolving to comprehensive platform performance report
   * @memberof IOfferingsService
   *
   * @description
   * Portfolio report includes:
   * - Cross-offering performance comparison
   * - Platform-wide investment metrics
   * - Asset class and sector analysis
   * - Market trends and insights
   */
  offeringReport(searchDetails: IUpdateOffering): Promise<PromiseResolve>;

  /**
   * Retrieves top-performing offerings based on various performance metrics and criteria.
   * Supports different ranking algorithms and time-based filtering.
   *
   * @method getTopOffering
   * @param {IUpdateOffering} searchDetails - Filter criteria for top offering calculation
   * @param {IPagination} pagination - Pagination for top offerings list
   * @param {string} type - Type of ranking metric (performance, volume, growth, etc.)
   * @returns {Promise<PromiseResolve>} Promise resolving to ranked list of top offerings
   * @memberof IOfferingsService
   *
   * @description
   * Ranking criteria include:
   * - Financial performance and returns
   * - Investment volume and liquidity
   * - Growth rate and appreciation
   * - Investor satisfaction and retention
   */
  getTopOffering(searchDetails: IUpdateOffering, pagination: IPagination, type: string): Promise<PromiseResolve>;

  /**
   * Retrieves offerings based on user investment history and portfolio.
   * Provides personalized view of user's invested offerings with performance tracking.
   *
   * @method investedOffering
   * @param {IUpdateOffering} searchDetails - User-specific search criteria for invested offerings
   * @param {IPagination} pagination - Pagination for user's investment portfolio
   * @returns {Promise<PromiseResolve>} Promise resolving to user's invested offerings with performance data
   * @memberof IOfferingsService
   *
   * @description
   * Investment tracking includes:
   * - User's current positions and holdings
   * - Investment performance and returns
   * - Transaction history and activity
   * - Portfolio diversification analysis
   */
  investedOffering(searchDetails: IUpdateOffering, pagination: IPagination): Promise<PromiseResolve>;

  /**
   * Processes whitelist rejection for investor access control and compliance.
   * Handles status updates with comprehensive notification and audit trail.
   *
   * @method rejectWalletWhiteList
   * @param {Types.ObjectId} whitelistId - Unique identifier of the whitelist entry to reject
   * @param {string} status - New status for the whitelist entry (typically 'REJECTED')
   * @returns {Promise<PromiseResolve>} Promise resolving to rejection confirmation and notification status
   * @memberof IOfferingsService
   *
   * @description
   * Rejection process includes:
   * - Status validation and conflict prevention
   * - User notification via email and socket
   * - Administrative audit trail
   * - Compliance documentation
   */
  rejectWalletWhiteList(whitelistId: Types.ObjectId, status: string): Promise<PromiseResolve>;

  /**
   * Calculates and updates token valuation based on asset performance and market conditions.
   * Handles NAV calculations, price updates, and valuation methodology application.
   *
   * @method tokenValuation
   * @param {IUpdateOffering} offeringId - Offering identifier for valuation calculation
   * @param {any} [data] - Optional additional data for valuation methodology
   * @returns {Promise<PromiseResolve>} Promise resolving to updated valuation data and pricing
   * @memberof IOfferingsService
   *
   * @description
   * Valuation process includes:
   * - Asset performance analysis
   * - Market condition evaluation
   * - NAV calculation and updates
   * - Price distribution to investors
   * - Historical valuation tracking
   */
  tokenValuation(offeringId: IUpdateOffering, data?: any): Promise<PromiseResolve>;

  /**
   * Retrieves detailed order information for a specific offering with comprehensive filtering.
   * Provides administrative and sponsor view of all orders related to an offering.
   *
   * @method fetchOrdersFromOffering
   * @param {string} userId - User ID of the offering sponsor or administrator
   * @param {any} offeringId - Unique identifier of the offering
   * @param {number} page - Page number for pagination
   * @param {number} limit - Number of orders per page
   * @param {string} status - Filter by order status (PENDING, APPROVED, REJECTED, etc.)
   * @param {string} searchQuery - Search query for filtering orders by user details
   * @param {any} isCsv - Flag for CSV export format
   * @returns {Promise<PromiseResolve>} Promise resolving to filtered order list with pagination and user details
   * @memberof IOfferingsService
   *
   * @description
   * Order data includes:
   * - Complete order details and status
   * - User information and verification status
   * - Payment and transaction information
   * - Minting and blockchain transaction data
   * - Search by user name, email, or wallet address
   */
  fetchOrdersFromOffering(userId: string, offeringId: any, page: number, limit: number, status: string, searchQuery: string, isCsv: any): Promise<PromiseResolve>;
}

/**
 * Interface representing country data for compliance and regulatory purposes.
 * Used for geographic restrictions, regulatory compliance, and investor eligibility.
 *
 * @interface CountryData
 * @description Country information structure for compliance and regulatory management
 */
export interface CountryData {
  /**
   * Compliance status or regulatory classification for the country.
   * Indicates allowed, restricted, or prohibited status for investment.
   *
   * @type {string}
   */
  compliance: string;

  /**
   * International dialing code for the country.
   * Used for phone number validation and SMS delivery.
   *
   * @type {string}
   */
  countryCode: string;

  /**
   * Full name of the country in English.
   * Used for display purposes and regulatory documentation.
   *
   * @type {string}
   */
  name: string;

  /**
   * ISO 3166-1 alpha-2 country code.
   * Standard two-letter country code for international compliance.
   *
   * @type {string}
   */
  isoCode: string;
}
