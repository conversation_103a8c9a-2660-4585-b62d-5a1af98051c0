import * as Jo<PERSON> from 'joi';
import { JoiValidationResult } from '../../utils/common.interface';
import { RES_MSG, RESPONSES } from '../../utils/responseUtils';
import logger from '../../helpers/logging/logger.helper';
import * as joiOptions from '../../helpers/joi.helper';

export default class ProposalValidation {
  static createProposalSchema() {
    return Joi.object({
      offeringId: Joi.string().length(24).hex().required().messages({ 'string.length': 'Invalid Offering Id', 'string.hex': '"offeringId" must be a valid MongoDB ObjectId', 'any.required': '"offeringId" is a required field' }),
      title: Joi.string().max(80).required().messages({ 'string.empty': '"title" cannot be empty', 'string.max': '"title" must not exceed 80 characters', 'any.required': '"title" is a required field' }),
      startDate: Joi.date().greater('now').required().messages({ 'date.base': '"startDate" must be a valid date', 'date.greater': '"startDate" must be a future date', 'any.required': '"startDate" is a required field' }),
      endDate: Joi.date().greater(Joi.ref('startDate')).required().messages({ 'date.base': '"endDate" must be a valid date', 'date.greater': '"endDate" must be after "startDate"', 'any.required': '"endDate" is a required field' }),
      description: Joi.string().min(10).required().messages({ 'string.empty': '"description" is a required field', 'string.min': '"description" must be at least 10 characters long', 'any.required': '"description" is a required field' }),
      holderPercent: Joi.number()
        .min(0)
        .max(100)
        .precision(6)
        .required()
        .messages({ 'number.base': '"holderPercents" must be a number', 'number.min': '"holderPercents" must be at least 0.000001', 'number.max': '"holderPercents" must not exceed 100', 'any.required': '"holderPercents" is a required field' }),
      voteCast: Joi.string()
        .valid('Yes,No,None', 'Yes,None,No', 'No,None,Yes', 'No,Yes,None', 'None,Yes,No', 'None,No,Yes')
        .required()
        .messages({ 'any.only': '"voteCast" must be one of "Yes", "No", "None"', 'any.required': '"voteCast" is a required field' }),
    });
  }

  /**
   * Validates proposal creation parameters
   * @param params - The parameters to validate
   * @returns Validation result
   */
  static async createProposal(params: Record<string, any>): Promise<JoiValidationResult> {
    try {
      const schema = this.createProposalSchema(); // Use the updated schema
      const { error, value } = schema.validate(params, joiOptions.options);

      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'Validation Error');

      return { error: true, value: '', message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR };
    }
  }

  static async voteProposal(params: Record<string, any>): Promise<JoiValidationResult> {
    try {
      const schema = Joi.object({
        proposalId: Joi.string().length(24).hex().required().messages({ 'string.length': 'Invalid proposal Id', 'string.hex': '"proposalId" must be a valid MongoDB ObjectId', 'any.required': '"proposalId" is a required field' }),
        voteValue: Joi.string().valid('Yes', 'No', 'None').required().messages({ 'any.only': '"voteValue" must be one of "Yes", "No", or "None"', 'any.required': '"voteValue" is a required field' }),
      });
      const { error, value } = schema.validate(params, joiOptions.options);

      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'Validation Error');

      return { error: true, value: '', message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR };
    }
  }

  static async getProposal(params: Record<string, any>): Promise<JoiValidationResult> {
    try {
      const schema = Joi.object({
        offeringId: Joi.string().length(24).hex().required().messages({ 'string.length': 'Invalid Offering Id', 'string.hex': '"offeringId" must be a valid MongoDB ObjectId', 'any.required': '"offeringId" is a required field' }),
        page: Joi.number().integer().min(1).max(100).required().messages({ 'number.base': '"page" must be a number', 'number.integer': '"page" must be an integer', 'number.min': '"page" must be at least 1' }),
        limit: Joi.number()
          .integer()
          .min(1)
          .max(10)
          .required()
          .messages({ 'number.base': '"limit" must be a number', 'number.integer': '"limit" must be an integer', 'number.min': '"limit" must be at least 1', 'number.max': '"limit" cannot exceed 100' }),
        search: Joi.string().max(50).optional().messages({ 'string.max': '"search" cannot exceed 50 characters' }),
        status: Joi.string().valid('ALL', 'ACTIVE', 'COMPLETED', 'UPCOMING').optional().messages({ 'any.only': '"status" must be one of "ALL", "ACTIVE", "COMPLETED", or "UPCOMING"' }),
      });

      const { error, value } = schema.validate(params, joiOptions.options);

      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'Validation Error');

      return { error: true, value: '', message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR };
    }
  }

  static async getProposalDetails(params: Record<string, any>): Promise<JoiValidationResult> {
    try {
      const schema = Joi.object({
        proposalId: Joi.string().length(24).hex().required().messages({ 'string.length': 'Invalid proposalId Id', 'string.hex': '"proposalId" must be a valid MongoDB ObjectId', 'any.required': '"proposalId" is a required field' }),
      });

      const { error, value } = schema.validate(params, joiOptions.options);

      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'Validation Error');

      return { error: true, value: '', message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR };
    }
  }

  static async getInvestorProposal(params: Record<string, any>): Promise<JoiValidationResult> {
    try {
      const schema = Joi.object({
        offeringId: Joi.string().length(24).hex().required().messages({ 'string.length': 'Invalid offeringId Id', 'string.hex': '"offeringId" must be a valid MongoDB ObjectId', 'any.required': '"offeringId" is a required field' }),
        page: Joi.number().integer().min(1).max(100).required().messages({ 'number.base': '"page" must be a number', 'number.integer': '"page" must be an integer', 'number.min': '"page" must be at least 1' }),
        limit: Joi.number()
          .integer()
          .min(1)
          .max(10)
          .required()
          .messages({ 'number.base': '"limit" must be a number', 'number.integer': '"limit" must be an integer', 'number.min': '"limit" must be at least 1', 'number.max': '"limit" cannot exceed 100' }),
        search: Joi.string().max(50).optional().messages({ 'string.max': '"search" cannot exceed 50 characters' }),
        status: Joi.string().valid('ALL', 'ACTIVE', 'COMPLETED', 'UPCOMING').optional().messages({ 'any.only': '"status" must be one of "all", "active", "closed", or "upcoming"' }),
      });

      const { error, value } = schema.validate(params, joiOptions.options);

      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'Validation Error');

      return { error: true, value: '', message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR };
    }
  }

  static async setInvestorCapProposal(params: Record<string, any>): Promise<JoiValidationResult> {
    try {
      const schema = Joi.object({
        offeringId: Joi.string().length(24).hex().required().messages({ 'string.length': '"proposalId" must be 24 characters long', 'string.hex': '"proposalId" must be a valid MongoDB ObjectId', 'any.required': '"proposalId" is a required field' }),
        capValue: Joi.string().max(6).required().messages({ 'string.max': '"capValue" must be at most 6 characters', 'any.required': '"capValue" is a required field' }),
      });

      const { error, value } = schema.validate(params, joiOptions.options);

      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'Validation Error');

      return { error: true, value: '', message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR };
    }
  }
}
