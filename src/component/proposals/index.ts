import { Request, Response } from 'express';
import { ResponseHandler } from '../../helpers/response.helper';
import { PromiseResolve } from '../../utils/common.interface';
import { RES_MSG, RESPONSES } from '../../utils/responseUtils';
import ProposalService from './service';

/**
 * @export
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise < PromiseResolve >}
 */

class Dividents {
  createProposal = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { error, status, message } = await ProposalService.createProposal(req, req.userInfo.userId);

      return ResponseHandler.success(res, { status, error, message });
    } catch (error) {
      console.log('createProposal error', error);
      return ResponseHandler.error(res, { message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true });
    }
  };

  voteProposal = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { error, status, message } = await ProposalService.voteProposal(req, req.userInfo.userId);

      return ResponseHandler.success(res, { status, error, message });
    } catch (error) {
      return ResponseHandler.error(res, { message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true });
    }
  };

  getWhitelistOffering = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { error, status, message, data } = await ProposalService.getWhitelistedOfferingList(req, req.userInfo.userId);

      return ResponseHandler.success(res, { data, status, error, message });
    } catch (error) {
      return ResponseHandler.error(res, { message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true });
    }
  };

  getIssuerProposal = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { error, status, message, data } = await ProposalService.getIssuerProposal(req, req.userInfo.userId);

      return ResponseHandler.success(res, { status, error, message, data });
    } catch (error) {
      return ResponseHandler.error(res, { message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true });
    }
  };

  getProposalDetails = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { error, status, message, data } = await ProposalService.getIssuerProposalDetails(req, req.userInfo.userType, req.userInfo.userId);

      return ResponseHandler.success(res, { status, error, message, data });
    } catch (error) {
      return ResponseHandler.error(res, { message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true });
    }
  };

  getInvestorProposal = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { error, status, message, data } = await ProposalService.getInvestorProposals(req, req.userInfo.userId);

      return ResponseHandler.success(res, { status, error, message, data });
    } catch (error) {
      return ResponseHandler.error(res, { message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true });
    }
  };

  setInvestorProposalCap = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { error, status, message } = await ProposalService.setInvestorProposalsCap(req, req.userInfo.userId);

      return ResponseHandler.success(res, { status, error, message });
    } catch (error) {
      return ResponseHandler.error(res, { message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true });
    }
  };
}

export default new Dividents();
