import mongoose, { Schema } from 'mongoose';

const vote = new Schema(
  {
    proposalId: { type: Schema.Types.ObjectId, ref: 'proposal', required: true },
    userId: { type: Schema.Types.ObjectId, ref: 'users', required: true },
    userAddress: { type: String, required: true },
    weight: { type: Number, required: true, min: 0 }, // Voting weight
    voteValue: { type: String, required: true },
  },
  { timestamps: true, versionKey: false },
);

vote.index({ proposalId: 1, userId: 1 }, { unique: true });

const votingModel = mongoose.model('voting', vote);

export { votingModel };
