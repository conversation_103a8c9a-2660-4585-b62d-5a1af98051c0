import mongoose, { Schema } from 'mongoose';

const Proposal = new Schema(
  {
    offeringId: { type: Schema.Types.ObjectId, ref: 'offerings', required: true },
    issuerId: { type: Schema.Types.ObjectId, ref: 'offerings', required: true },
    title: { type: String, required: true },
    description: { type: String, required: true },
    holderPercent: { type: String, required: true, min: 0, max: 100 },
    voteCast: { type: String, required: true },
    documentUrl: { type: String, required: false },
    status: { type: String, default: 'UPCOMING', enum: ['ACTIVE', 'COMPLETED', 'CANCELLED', 'UPCOMING'] },
    tokenAddress: { type: String, required: false, default: '' },
    startDate: { type: Date, required: true },
    endDate: { type: Date, required: true },
    yesVotes: { type: Number, default: 0, required: false },
    noVotes: { type: Number, default: 0, required: false },
    noneVotes: { type: Number, default: 0, required: false },
    totalVotes: { type: Number, default: 0, required: false },
  },
  { timestamps: true, versionKey: false },
);

Proposal.index({ offeringId: 1, title: 1 }, { unique: true });

const proposalModel = mongoose.model('proposal', Proposal);

export { proposalModel };
