/* eslint-disable no-restricted-globals */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { Request } from 'express';
import axios, { HttpStatusCode } from 'axios';
import mongoose from 'mongoose';
import CustomError from '../../helpers/customError.helper';
import CloudHelper from '../../helpers/cloud.helper';
import { DocumentFolderTypesEnum, DocumentTypesEnum, PromiseResolve } from '../../utils/common.interface';
import { RES_MSG, RESPONSES } from '../../utils/responseUtils';
import { offeringSchema } from '../offerings/models/offerings.model';
import config from '../../config/env';
import { proposalModel } from './modals/proposal';
import { votingModel } from './modals/voting';
import { whitelistSchema } from '../offerings/models/whitelist.model';
import logger from '../../helpers/logging/logger.helper';
import emailHelper from '../../helpers/email.helper';
import { OrderSchema } from '../order/models/order.model';
import { userSchema } from '../userAuthentications/models/user.model';

/**
 * Service class managing governance proposals and voting operations for tokenized assets.
 * Handles proposal creation, validation, voting mechanisms, and democratic decision-making processes.
 * Supports issuer and investor proposals with different eligibility requirements and voting rights.
 * Integrates with blockchain data, user management, and notification systems.
 *
 * @class ProposalService
 * @description Comprehensive governance and voting service for tokenized asset management
 *
 */
class ProposalService {
  /**
   * Validates if the proposal start date is valid (current or future date).
   * Ensures proposals cannot be backdated for governance integrity.
   *
   * @private
   * @method validateStartDate
   * @param {string} date - The start date to validate in ISO string format
   * @returns {boolean} Returns true if start date is valid (current or future), false otherwise
   * @memberof ProposalService
   *
   * @description
   * Start date validation ensures:
   * - Proposals cannot be backdated
   * - Maintains governance timeline integrity
   * - Allows sufficient time for stakeholder notification
   * - Supports proper voting schedule management
   *
   */
  private validateStartDate(date: string): boolean {
    return new Date(date) >= new Date();
  }

  /**
   * Validates if the proposal end date is after the start date.
   * Ensures proper voting period duration and timeline consistency.
   *
   * @private
   * @method validateEndDate
   * @param {string} startDate - The proposal start date in ISO string format
   * @param {string} endDate - The proposal end date in ISO string format
   * @returns {boolean} Returns true if end date is after start date, false otherwise
   * @memberof ProposalService
   *
   * @description
   * End date validation ensures:
   * - Voting period has positive duration
   * - Maintains logical proposal timeline
   * - Prevents invalid date configurations
   * - Supports governance scheduling integrity
   *
   */
  private validateEndDate(startDate: string, endDate: string): boolean {
    return new Date(endDate) > new Date(startDate);
  }

  /**
   * Creates a new governance proposal with comprehensive validation and document management.
   * Handles both issuer-initiated and investor-initiated proposals with different eligibility criteria.
   * Supports document uploads, permission validation, and stakeholder notifications.
   *
   * @async
   * @public
   * @method createProposal
   * @param {Request} req - Express request object containing proposal data and optional file upload
   * @param {Request} req.body.offeringId - ID of the offering for which the proposal is created
   * @param {Request} req.body.title - Title of the proposal (must be unique per offering)
   * @param {Request} req.body.startDate - Voting start date for the proposal
   * @param {Request} req.body.endDate - Voting end date for the proposal
   * @param {Request} req.body.description - Detailed description of the proposal
   * @param {Request} req.body.holderPercent - Minimum token holding percentage for voting eligibility
   * @param {Request} req.body.voteCast - Voting options or methodology for the proposal
   * @param {Request} req.file - Optional proposal document attachment
   * @param {Request} req.userInfo - Authenticated user information with permissions
   * @param {string} userId - ID of the user creating the proposal
   * @returns {Promise<any>} Promise resolving to created proposal with all details
   * @throws {CustomError} Throws errors for validation failures, authorization issues, or creation problems
   * @memberof ProposalService
   *
   * @description
   * Proposal creation process:
   * 1. Date validation for voting period
   * 2. Offering existence and access verification
   * 3. Proposal title uniqueness check
   * 4. User permission and eligibility validation
   * 5. Token holding percentage verification (for investors)
   * 6. Document upload and URL generation
   * 7. Proposal database creation
   * 8. Stakeholder notification via email
   *
   * User types and permissions:
   * - Issuer: Can create proposals if they own the offering
   * - Investor: Can create proposals if they meet minimum holding requirements
   * - Representative: May have delegated proposal rights
   *
   * Validation rules:
   * - Start date must be current or future
   * - End date must be after start date
   * - Proposal title must be unique per offering
   * - User must be authorized for the offering
   * - Investors must meet minimum token holding percentage
   * - Offering must support governance proposals
   *
   * Document management:
   * - Optional proposal document upload
   * - Cloud storage integration
   * - Document URL generation and storage
   * - File type and size validation
   *
   */
  public async createProposal(req: Request, userId: string) {
    try {
      const { file } = req;
      const { offeringId, title, startDate, endDate, description, holderPercent, voteCast } = req.body;

      // Validate record date
      if (!this.validateStartDate(startDate)) {
        throw new CustomError('Invalid start date!', HttpStatusCode.BadRequest);
      }
      if (!this.validateEndDate(startDate, endDate)) {
        throw new CustomError('Invalid end date!', HttpStatusCode.BadRequest);
      }

      // Check if offering exists
      const offering = await offeringSchema.findById(offeringId);
      if (!offering) {
        throw new CustomError('Offering not found!', HttpStatusCode.NotFound);
      }
      const percentageCap = offering.proposalHoldingPercentage[offering.proposalHoldingPercentage.length - 1];

      // Check if proposal already exists
      const isProposalExist = await proposalModel.findOne({ offeringId, title });
      if (isProposalExist) {
        throw new CustomError('A proposal with this title already exists. Please choose a different title.', HttpStatusCode.Conflict);
      }

      // Check user permissions
      const { isIssuer, userType, userId } = req.userInfo;
      // case 1:  if investor holds more than some X specified percentage
      // if (!isIssuer && userType === 'investor') {
      if (!isIssuer && userType) {
        const whitelistedUsers = await whitelistSchema
          .findOne({ userId: new mongoose.Types.ObjectId(userId), offeringId: new mongoose.Types.ObjectId(offeringId.toString()), status: 'APPROVED' })
          .select('address')
          .lean();

        if (!whitelistedUsers) {
          throw new CustomError('You are not whitelisted for this offering!', RESPONSES.BAD_REQUEST);
        }

        const { tokenAddress } = offering;
        const userAddress = whitelistedUsers.address;

        if (!percentageCap) {
          throw new CustomError("You're not eligible to create a proposal", RESPONSES.BAD_REQUEST);
        }

        const response = await this.isEligibleForVote(tokenAddress, userAddress, String(percentageCap), offeringId.toString());
        if (!response.data.eligible) {
          throw new CustomError(response.data.message, RESPONSES.BAD_REQUEST);
        }
      }
      // case 2: If user is Issuer then check if this user is the owner of offering then only he will create the proposals
      if (Number(percentageCap) <= 0 && offering.userId.toString() !== userId.toString()) {
        throw new CustomError('Owner can create the Proposal', HttpStatusCode.BadGateway);
      }

      const _id = new mongoose.Types.ObjectId();
      let documentUrl: string | undefined;

      if (file) {
        const uploadResult: PromiseResolve = await CloudHelper.uploadFiles(userId, file, DocumentTypesEnum.PROPOSAL, _id.toString(), offeringId);
        if (uploadResult.error) {
          throw new CustomError(uploadResult.message || 'File upload failed!', uploadResult.status || HttpStatusCode.BadGateway);
        }
        documentUrl = uploadResult?.data?.url;
      }

      const proposalData = await proposalModel.create({
        _id,
        offeringId,
        issuerId: userId,
        title,
        description,
        holderPercent,
        voteCast,
        startDate,
        endDate,
        documentUrl,
        tokenAddress: offering.tokenAddress,
      });

      const { name } = await userSchema.findOne({ _id: proposalData.issuerId });
      const { projectDetails } = await offeringSchema.findOne({ _id: offeringId });
      const userIds = await OrderSchema.find({ offeringId: proposalData.offeringId }).distinct('userId');

      const userEmails = await userSchema.find({ _id: { $in: userIds } }).select('email name'); // fetch both email and name

      userEmails.forEach((user) => {
        const emailDetail = {
          investorName: user.name, // personalize the email with user's name
          name,
          proposalTittle: title,
          startDate,
          endDate,
          tokenTicker: projectDetails?.tokenTicker,
          offeringName: projectDetails?.offeringName,
        };

        emailHelper.sendEmailTemplate(user.email, 'VotingProposalSubmitted', emailDetail);
      });

      return { status: RESPONSES.CREATED, error: false, message: RES_MSG.SUCCESS_MSG.CREATE_SUCCESS };
    } catch (error) {
      logger.error(error);
      console.log('error', error);
      return { status: error?.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error?.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  }

  getIssuerProposal = async (req: Request, userId: string): Promise<PromiseResolve> => {
    try {
      const { offeringId, page = 1, limit = 10, search = '', status = '' } = req.body;

      // Validate offeringId format
      if (!mongoose.Types.ObjectId.isValid(offeringId)) {
        throw new CustomError('Invalid offering ID format!', RESPONSES.BAD_REQUEST);
      }

      const query: Record<string, any> = {
        offeringId: new mongoose.Types.ObjectId(offeringId),
        issuerId: new mongoose.Types.ObjectId(userId),
      };

      // Apply search filter (case-insensitive title match)
      if (search) {
        query.title = { $regex: search, $options: 'i' };
      }

      // Apply status filter based on current date
      const currentDate = new Date();
      if (status && status !== 'ALL') {
        if (status === 'ACTIVE') {
          query.startDate = { $lte: currentDate };
          query.endDate = { $gte: currentDate };
        } else if (status === 'UPCOMING') {
          query.startDate = { $gt: currentDate };
        } else if (status === 'COMPLETED') {
          query.endDate = { $lt: currentDate };
        }
      }

      // Fetch proposals with pagination
      const existingProposals = await proposalModel
        .find(query)
        .skip((page - 1) * limit)
        .limit(limit)
        .sort({ createdAt: -1 })
        .lean();

      // Check if any proposals exist
      if (!existingProposals || existingProposals.length === 0) {
        return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.FETCHED, data: { proposals: [], pagination: { page, limit, totalItems: 0 } } };
      }

      // Fetch total count for pagination metadata
      const totalCount = await proposalModel.countDocuments({ offeringId: new mongoose.Types.ObjectId(offeringId), issuerId: new mongoose.Types.ObjectId(userId) });

      return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.FETCHED, data: { proposals: existingProposals, pagination: { page, limit, totalItems: totalCount } } };
    } catch (error) {
      logger.error('Error in fetching issuer Proposal: ', error);

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  getIssuerProposalDetails = async (req: Request, userType: string, userId: string): Promise<PromiseResolve> => {
    try {
      const { proposalId } = req.body;

      // Validate proposalId format
      if (!mongoose.Types.ObjectId.isValid(proposalId)) {
        throw new CustomError('Invalid proposalId format!', RESPONSES.BAD_REQUEST);
      }

      // Fetch the proposal
      const existingProposal = await proposalModel.findOne({ _id: proposalId }).lean();
      if (!existingProposal) {
        throw new CustomError('Proposal not found!', RESPONSES.NOTFOUND);
      }

      // Determine and update proposal status
      const currentTime = Date.now();
      const { startDate, endDate } = existingProposal;
      const start = new Date(startDate).getTime();
      const end = new Date(endDate).getTime();

      // if (currentTime < start) {
      //   throw new CustomError('Proposal is not active yet!', RESPONSES.BAD_REQUEST);
      // } else

      if (currentTime >= start && currentTime <= end) {
        await proposalModel.updateOne({ _id: proposalId }, { $set: { status: 'ACTIVE' } });
      } else if (currentTime > end) {
        await proposalModel.updateOne({ _id: proposalId }, { $set: { status: 'COMPLETED' } });
      }

      // Fetch proposal details with aggregation
      const [data] = await proposalModel.aggregate([
        { $match: { _id: new mongoose.Types.ObjectId(proposalId) } },
        { $lookup: { from: 'votings', localField: '_id', foreignField: 'proposalId', as: 'votings' } },
        { $lookup: { from: 'users', localField: 'votings.userId', foreignField: '_id', as: 'users' } },
        {
          $project: {
            _id: 1,
            title: 1,
            description: 1,
            yesVotes: 1,
            noVotes: 1,
            noneVotes: 1,
            totalVotes: 1,
            documentUrl: 1,
            offeringId: 1,
            issuerId: 1,
            startDate: 1,
            endDate: 1,
            status: 1,
            participants: {
              $map: {
                input: '$votings',
                as: 'voting',
                in: {
                  userAddress: '$$voting.userAddress',
                  weight: '$$voting.weight',
                  voteValue: '$$voting.voteValue',
                  userDetails: { $arrayElemAt: [{ $filter: { input: '$users', as: 'user', cond: { $eq: ['$$user._id', '$$voting.userId'] } } }, 0] },
                },
              },
            },
          },
        },
        {
          $addFields: {
            participants: {
              $map: {
                input: '$participants',
                as: 'participant',
                in: {
                  userAddress: '$$participant.userAddress',
                  weight: '$$participant.weight',
                  voteValue: '$$participant.voteValue',
                  name: '$$participant.userDetails.name',
                  email: '$$participant.userDetails.email',
                  image: '$$participant.userDetails.userImage',
                },
              },
            },
          },
        },
        {
          $addFields: {
            participants: {
              $sortArray: { input: '$participants', sortBy: { weight: -1 } }, // Sorting according to weight in descending order
            },
          },
        },
      ]);

      if (userType === 'investor') {
        const isExistVote = await votingModel.findOne({
          userId: new mongoose.Types.ObjectId(userId),
          proposalId: new mongoose.Types.ObjectId(proposalId),
        });

        data.investorVote = isExistVote ? isExistVote.voteValue : 'Vote does not exist';
      }

      // Return success response
      return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.FETCHED, data };
    } catch (error) {
      logger.error('Error in fetching issuer proposal details:', error);

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  getInvestorProposals = async (req: Request, userId: string): Promise<PromiseResolve> => {
    try {
      const { offeringId, page, limit, search = '', status = '' } = req.query;

      // Validate offeringId format
      if (!mongoose.Types.ObjectId.isValid(offeringId.toString())) {
        throw new CustomError('Invalid offeringId ID format!', RESPONSES.BAD_REQUEST);
      }

      // check if user is whitelisted on this offering
      const isWhitelistedOnOffering = await whitelistSchema.findOne({ offeringId: new mongoose.Types.ObjectId(offeringId.toString()), userId: new mongoose.Types.ObjectId(userId), status: 'APPROVED' });
      if (!isWhitelistedOnOffering) {
        throw new CustomError('User is not whitlisted on this offering', RESPONSES.BAD_REQUEST);
      }

      // Apply status filter based on current date
      const currentDate = new Date();
      const matchStatusCondition =
        status && status !== 'ALL'
          ? status === 'ACTIVE'
            ? { $and: [{ 'proposals.startDate': { $lte: currentDate } }, { 'proposals.endDate': { $gte: currentDate } }] }
            : status === 'UPCOMING'
              ? { 'proposals.startDate': { $gt: currentDate } }
              : status === 'COMPLETED'
                ? { 'proposals.endDate': { $lt: currentDate } }
                : {}
          : {}; // No filter if status is 'ALL' or undefined

      const data = await whitelistSchema.aggregate([
        {
          $match: {
            offeringId: new mongoose.Types.ObjectId(offeringId.toString()),
            userId: new mongoose.Types.ObjectId(userId),
            status: 'APPROVED', // Match records with status "APPROVED"
          },
        },
        {
          $lookup: {
            from: 'proposals', // Join with the "proposals" collection
            localField: 'offeringId', // offeringId in current collection
            foreignField: 'offeringId', // offeringId in proposals collection
            as: 'proposals', // Store joined results in "proposals"
          },
        },
        {
          $unwind: '$proposals', // Flatten the proposals array
        },
        ...(Object.keys(matchStatusCondition).length ? [{ $match: matchStatusCondition }] : []), // Apply dynamic status filtering if needed
        {
          $sort: { 'proposals.createdAt': -1 }, // Sort by proposal createdAt in descending order
        },
        { $skip: (Number(page) - 1) * Number(limit) },
        { $limit: Number(limit) },
        {
          $project: {
            _id: '$proposals._id',
            title: '$proposals.title',
            description: '$proposals.description',
            offeringId: '$proposals.offeringId',
            issuerId: '$proposals.issuerId',
            documentUrl: '$proposals.documentUrl',
            yesVotes: '$proposals.yesVotes',
            noVotes: '$proposals.noVotes',
            noneVotes: '$proposals.noneVotes',
            totalVotes: '$proposals.totalVotes',
            startDate: '$proposals.startDate',
            endDate: '$proposals.endDate',
          },
        },
      ]);

      // Return success response
      return { status: RESPONSES.SUCCESS, error: false, message: data.length > 0 ? RES_MSG.SUCCESS_MSG.FETCHED : 'No proposal found!', data: data || [] };
    } catch (error) {
      logger.error('Error in fetching issuer Proposal:', error);

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  private async isEligibleForVote(tokenAddress: string, userAddress: string, holderPercent: string, offeringId: string): Promise<{ data: any; error: boolean }> {
    try {
      // Fetch the offering details
      const offering = await offeringSchema.findById(offeringId);
      if (!offering || !offering.projectDetails?.maxTokenHolding) {
        throw new Error('Invalid offering or missing maxTokenHolding');
      }

      // Prepare payload for the CAP_TABLE_URL request
      const capTablePayload = { address: [userAddress], contractaddress: tokenAddress, total_dividend_amount: 1 };

      // Fetch user balance from CAP_TABLE_URL

      const responseFromCapTable = await axios.post(config.CAP_TABLE_URL, capTablePayload);

      const response = responseFromCapTable.data;

      if (response.data?.error === true) {
        throw new Error(response.data.message);
      }
      const capTableData = response.data;

      // Validate CAP Table response
      if (!Array.isArray(capTableData) || capTableData.length === 0) {
        throw new Error('CAP Table response is empty or invalid');
      }

      const userBalance = Number(capTableData[0].balance);
      if (isNaN(userBalance)) {
        throw new Error('Invalid balance value in CAP Table response');
      }

      const url = `${config.CAP_TABLE_URL.split('cap-table')[0]}totalSupply`;
      const totalSupplyParams = { contractaddress: tokenAddress };
      const responseTotalSupply = await axios.get(url, { params: totalSupplyParams });
      const { totalSupply } = responseTotalSupply.data;

      // Validate `totalSupply`
      if (totalSupply === 0 || !totalSupply) {
        throw new Error(`Error in fetching totalSupply: ${totalSupply} for contractAddress: ${tokenAddress}`);
      }

      // Ensure `totalSupply` is a valid number
      const totalSupplyValue = parseFloat(totalSupply);
      if (isNaN(totalSupplyValue) || totalSupplyValue <= 0) {
        throw new Error(`Invalid totalSupply received: ${totalSupply} for contractAddress: ${tokenAddress}`);
      }

      // Calculate the user's percentage of total tokens
      const existingPercentage = ((userBalance / totalSupplyValue) * 100).toFixed(2);

      const msg = 'You’re not eligible to vote in this proposal';

      // Check eligibility based on holder percentage
      const eligible = Number(existingPercentage) >= Number(holderPercent);

      return { data: { eligible, message: msg, existingPercentage }, error: false };
    } catch (error) {
      logger.error('Error in isEligibleForVote:', error.message || error);

      // Return a structured error response
      return { data: { message: error.message || 'An unexpected error occurred' }, error: true };
    }
  }

  public async voteProposal(req: Request, userId: string) {
    try {
      const { proposalId, voteValue } = req.body;

      // Validate proposalId format
      if (!mongoose.Types.ObjectId.isValid(proposalId)) {
        throw new CustomError('Invalid proposal ID format!', RESPONSES.BAD_REQUEST);
      }

      // Check if the proposal exists
      const existingProposal = await proposalModel.findOne({ _id: new mongoose.Types.ObjectId(proposalId) }).lean();

      if (!existingProposal) {
        throw new CustomError('Proposal not found!', RESPONSES.NOTFOUND);
      }

      // Check if the proposal is active for voting
      const currentDate = new Date();
      if (currentDate < new Date(existingProposal.startDate) || currentDate > new Date(existingProposal.endDate)) {
        throw new CustomError('Voting for this proposal is not active!', RESPONSES.BAD_REQUEST);
      }

      // check if userID of proposal creator is same as voting the reject the request
      if (userId === existingProposal.issuerId.toString()) {
        throw new CustomError('Propsal creator can not vote!', RESPONSES.BAD_REQUEST);
      }

      // Check if the user has already voted for this proposal
      const existingVote = await votingModel.findOne({ proposalId: new mongoose.Types.ObjectId(proposalId), userId: new mongoose.Types.ObjectId(userId) }).lean();
      if (existingVote) {
        throw new CustomError('You have already voted for this proposal!', RESPONSES.BAD_REQUEST);
      }

      const whitelistedUsers = await whitelistSchema
        .findOne({ userId: new mongoose.Types.ObjectId(userId), offeringId: new mongoose.Types.ObjectId(existingProposal.offeringId.toString()), status: 'APPROVED' })
        .select('address')
        .lean();

      if (!whitelistedUsers) {
        throw new CustomError('You are not whitelisted for this offering!', RESPONSES.BAD_REQUEST);
      }
      const userAddress = whitelistedUsers.address;
      const { tokenAddress, holderPercent } = existingProposal;

      if (!tokenAddress || !userAddress) {
        throw new CustomError('tokenAddress or userAddress not found!', RESPONSES.BAD_REQUEST);
      }

      const response = await this.isEligibleForVote(tokenAddress, userAddress, holderPercent, existingProposal.offeringId.toString());
      if (!response.data.eligible) {
        throw new CustomError(String(response.data.message), RESPONSES.BAD_REQUEST);
      }

      const weight = parseFloat(response.data.existingPercentage);

      // Increment vote counts in the proposal
      const voteFields: { [key: string]: { [key: string]: number } } = { Yes: { yesVotes: weight, totalVotes: 1 }, No: { noVotes: weight, totalVotes: 1 }, None: { noneVotes: weight, totalVotes: 1 } };

      const updateFields = voteFields[voteValue] || { totalVotes: 0 };

      const [newVote, updateToProposal] = await Promise.all([
        await votingModel.create({ proposalId, userId, userAddress, weight, voteValue }),
        await proposalModel.findOneAndUpdate({ _id: new mongoose.Types.ObjectId(proposalId) }, { $inc: updateFields }),
      ]);
      const userdetails = await userSchema.findOne({ _id: userId }).select('email name');
      const { email, name } = userdetails;
      const Investorname = await userSchema.findOne({ _id: updateToProposal.issuerId }).select('name');
      const offeringDetails = await offeringSchema.findOne({ _id: updateToProposal.offeringId }).select('projectDetails');
      const offeringName = offeringDetails?.projectDetails?.offeringName;
      const tokenTicker = offeringDetails?.projectDetails?.tokenTicker;

      const emailDetail = {
        email,
        name,
        Investorname: Investorname?.name,
        proposalTittle: updateToProposal.title,
        voteValue: newVote?.voteValue,
        date: new Date().toLocaleString(),
        offeringName: offeringName,
        token: tokenTicker,
      };
      emailHelper.sendEmailTemplate(email, 'VoteSuccessfully', emailDetail);
      // email for voting success
      return { status: RESPONSES.CREATED, error: false, message: RES_MSG.SUCCESS_MSG.VOTING_SUCCESS, data: newVote };
    } catch (error) {
      logger.error('Error in voteProposal:', error);

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  }

  public getWhitelistedOfferingList = async (req: Request, userId: string): Promise<PromiseResolve> => {
    try {
      // Fetch the whitelisted offerings with pagination
      const offeringList = await whitelistSchema.aggregate([
        { $match: { userId: new mongoose.Types.ObjectId(userId), status: 'APPROVED' } },
        { $lookup: { from: 'offerings', localField: 'offeringId', foreignField: '_id', as: 'offerings' } },
        { $unwind: { path: '$offerings', preserveNullAndEmptyArrays: true } },
        { $project: { offeringId: 1, offeringName: '$offerings.projectDetails.offeringName', offeringTicker: '$offerings.projectDetails.tokenTicker', offeringImage: '$offerings.overview.logo', createdAt: '$offerings.createdAt' } },
        { $sort: { createdAt: -1 } },
      ]);

      return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.FETCHED, data: offeringList || [] };
    } catch (error) {
      logger.error('Error fetching whitelisted offerings:', error.message);

      return { message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR, error: true };
    }
  };

  setInvestorProposalsCap = async (req: Request, userId: string): Promise<PromiseResolve> => {
    try {
      const { offeringId, capValue } = req.body;
      // Validate offeringId format
      if (!mongoose.Types.ObjectId.isValid(offeringId?.toString())) {
        throw new CustomError('Invalid offeringId format!', RESPONSES.BAD_REQUEST);
      }

      // Fetch existing offering
      const existingOffering = await offeringSchema.findById(offeringId);
      if (!existingOffering) {
        throw new CustomError('Offering does not exist!', RESPONSES.BAD_REQUEST);
      }

      // Check ownership
      if (existingOffering.userId.toString() !== userId.toString()) {
        throw new CustomError('Only the offering owner can edit this functionality!', RESPONSES.BAD_REQUEST);
      }

      // Update proposalHoldingPercentage
      await offeringSchema.updateOne({ _id: offeringId }, { $push: { proposalHoldingPercentage: capValue.toString() } });

      // Return success response
      return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.OFFERING_UPDATION_SUCCESS };
    } catch (error) {
      logger.error('Error in setInvestorProposalsCap:', error);

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };
}

export default new ProposalService();
