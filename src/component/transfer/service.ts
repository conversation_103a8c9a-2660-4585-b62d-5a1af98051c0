import { FilterQuery, Types } from 'mongoose';
import { ObjectId } from 'bson';
import TransferRequest, { ITransferRequest } from './models/transfer';
import logger from '../../helpers/logging/logger.helper';
import CustomError from '../../helpers/customError.helper';
import { RES_MSG, RESPONSES } from '../../utils/responseUtils';
import { whitelistSchema } from '../offerings/models/whitelist.model';
import kafkaService from '../../service/kafkaService';
import { AssetTypeEnum, orderStatusEnum, PromiseResolve, queueMessageTypeEnum, transferStatusEnum } from '../../utils/common.interface';
import OfferingService from '../offerings/service';
import { UserDetailsSchema } from '../userAuthentications/models/userDetails.model';
import OrderService from '../order/service';
import { calculate } from '../../helpers/bigMath';
import UserService from '../userAuthentications/service';
import emailHelper from '../../helpers/email.helper';
import { socketHelper } from '../../helpers/socket.helper';

/**
 * Service class managing token transfer operations and requests for tokenized assets.
 * Handles peer-to-peer transfers, force transfers, whitelist validation, and compliance checking.
 * Provides comprehensive transfer lifecycle management including approval workflows and notifications.
 * Integrates with offering services, user management, and notification systems.
 *
 * @class TransferRequestService
 * @description Complete token transfer management service with compliance and approval workflows
 *
 * // Get user's transfer requests
 * const transfers = await TransferRequestService.getAllTransferRequests(
 *   1, 10, 'search_query', 'userId123'
 * );
 * ```
 */
export default class TransferRequestService {
  /**
   * Validates and creates a new token transfer request with comprehensive compliance checking.
   * Determines if the transfer is regular or forced based on whitelist status and user permissions.
   * Handles whitelist validation, transfer type determination, and administrative notifications.
   *
   * @async
   * @static
   * @method checkTransfer
   * @param {ObjectId} userId - User ID requesting the token transfer
   * @param {ObjectId} offeringId - Offering ID for the tokens being transferred
   * @param {any} transferRequestData - Transfer request details including recipient and amount
   * @param {string} transferRequestData.newWalletAddress - Destination wallet address for token transfer
   * @param {string} transferRequestData.quantity - Number of tokens to transfer
   * @param {string} [transferRequestData.reason] - Optional reason for the transfer
   * @param {string} [transferRequestData.notes] - Optional additional notes
   * @returns {Promise<PromiseResolve>} Promise resolving to transfer request creation result
   * @throws {CustomError} Throws errors for whitelist violations or validation failures
   * @memberof TransferRequestService
   *
   * @description
   * Transfer validation process:
   * 1. Validates recipient wallet is whitelisted for the offering
   * 2. Determines transfer type based on sender's whitelist status
   * 3. Creates transfer request with appropriate force transfer flag
   * 4. Sends administrative notifications via Kafka
   * 5. Returns success confirmation with transfer request details
   *
   * Transfer types:
   * - Regular Transfer: When sender is whitelisted for the offering
   * - Force Transfer: When sender is not whitelisted (requires admin approval)
   *
   * Validation rules:
   * - Recipient address must be whitelisted for the offering
   * - Transfer request data must be complete and valid
   * - User must have sufficient token balance (checked separately)
   * - Offering must be active and allow transfers
   *
   * // Force transfer (sender not whitelisted)
   * const forceTransfer = await TransferRequestService.checkTransfer(
   *   new ObjectId('nonWhitelistedUser'),
   *   new ObjectId('offering456'),
   *   {
   *     newWalletAddress: '0x742d35Cc6634C0532925a3b8D7389c5e8dc07C',
   *     quantity: '50',
   *     reason: 'Compliance requirement'
   *   }
   * );
   * ```
   */
  static checkTransfer = async (userId: ObjectId, offeringId: ObjectId, transferRequestData: any) => {
    try {
      let isForceTransfer;
      const isNewUserWhitelisted = await whitelistSchema.findOne({ offeringId, address: transferRequestData.newWalletAddress });
      const isTokenOwnerWhitelisted = await whitelistSchema.findOne({ offeringId, userId });
      if (!isNewUserWhitelisted) {
        throw new CustomError('User address has not whitelisted yet !', RESPONSES.BAD_REQUEST);
      } else if (isTokenOwnerWhitelisted?.userId?.toString() === userId?.toString()) {
        isForceTransfer = false;
      } else if (!isTokenOwnerWhitelisted) {
        isForceTransfer = true;
      }
      const newTransferRequest = await this.createTransferRequest({ ...transferRequestData, isForceTransfer, userId });
      if (newTransferRequest) {
        const finalData = JSON.parse(JSON.stringify(newTransferRequest));
        await kafkaService.sendMessageToAdmin({ value: { ...finalData, type: queueMessageTypeEnum.TRANSFER } });
      }

      if (!newTransferRequest) {
        throw new CustomError('Failed to create Transfer Request', RESPONSES.BAD_REQUEST);
      }

      return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.CREATE_SUCCESS, data: newTransferRequest };
    } catch (error) {
      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * Creates a new transfer request record in the database with provided transfer details.
   * Handles database persistence and error management for transfer request creation.
   *
   * @async
   * @static
   * @method createTransferRequest
   * @param {ITransferRequest} transferRequestData - Complete transfer request data including user, offering, and transfer details
   * @param {ObjectId} transferRequestData.userId - User ID requesting the transfer
   * @param {ObjectId} transferRequestData.offeringId - Offering ID for the tokens
   * @param {string} transferRequestData.newWalletAddress - Destination wallet address
   * @param {string} transferRequestData.quantity - Number of tokens to transfer
   * @param {boolean} transferRequestData.isForceTransfer - Whether this is a force transfer
   * @param {string} [transferRequestData.reason] - Optional transfer reason
   * @returns {Promise<ITransferRequest>} Promise resolving to saved transfer request document
   * @throws {Error} Throws error if database operation fails
   * @memberof TransferRequestService
   *
   * @description
   * Creates transfer request with:
   * - Complete transfer details and metadata
   * - User and offering associations
   * - Transfer type and approval requirements
   * - Timestamp and status tracking
   * - Audit trail for compliance
   *
   */
  static createTransferRequest = async (transferRequestData: ITransferRequest) => {
    try {
      const transferRequest = new TransferRequest(transferRequestData);

      return await transferRequest.save();
    } catch (error) {
      logger.error(error, 'Error while creating transfer request');
      throw new Error('Failed to create transfer request');
    }
  };

  /**
   * Retrieves paginated list of transfer requests for a specific user with comprehensive filtering and search.
   * Supports search across multiple fields and automatic status updates for time-expired requests.
   * Provides detailed transfer information with offering and user context.
   *
   * @async
   * @static
   * @method getAllTransferRequests
   * @param {number} page - Page number for pagination (starts from 1)
   * @param {number} limit - Number of transfer requests per page
   * @param {string} searchQuery - Search query for filtering by wallet address, email, name, or security name
   * @param {string} userId - User ID to filter transfer requests for
   * @returns {Promise<any>} Promise resolving to paginated transfer requests with metadata
   * @memberof TransferRequestService
   *
   * @description
   * Retrieval features:
   * - Pagination with configurable page size
   * - Multi-field search across addresses, emails, names, and security names
   * - Automatic status updates for time-expired pending requests
   * - Comprehensive transfer request details and context
   * - Sort by creation date (newest first)
   * - Efficient database queries with lean objects
   *
   * Search capabilities:
   * - Wallet address matching (case-insensitive)
   * - Registered email address search
   * - User name search
   * - Security/offering name search
   * - Regex-based partial matching
   *
   * Status management:
   * - Automatic expiration of pending requests after 24 hours
   * - In-memory status modification without database updates
   * - Timestamp-based expiration logic
   * - Status transparency for users
   *
   * // Search transfer requests by wallet address
   * const searchResults = await TransferRequestService.getAllTransferRequests(
   *   1, 10, '0x742d35', 'userId123'
   * );
   *
   * // Get large page for export
   * const exportData = await TransferRequestService.getAllTransferRequests(
   *   1, 100, '', 'userId123'
   * );
   * ```
   */
  static getAllTransferRequests = async (page: number, limit: number, searchQuery: string, userId: string) => {
    try {
      const searchConditions: any = { userId };

      if (searchQuery !== undefined && searchQuery !== '' && searchQuery !== 'undefined') {
        const searchRegex = new RegExp(searchQuery, 'i'); // Case insensitive regex search
        searchConditions.$or = [{ walletAddress: { $regex: searchRegex } }, { registeredEmailId: { $regex: searchRegex } }, { registeredName: { $regex: searchRegex } }, { securityName: { $regex: searchRegex } }];
      }
      const transferRequests = await TransferRequest.find(searchConditions)
        .sort({ createdAt: -1 })
        .skip((page - 1) * limit)
        .limit(limit)
        .lean() // Using lean() to get plain JavaScript objects instead of Mongoose documents
        .exec();

      // Calculate timestamp for 24 hours ago
      const twentyFourHoursAgo = new Date();
      twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24);

      // Modify the status in memory for items older than 24 hours with PENDING status
      // This doesn't update the database, only changes the response data
      const modifiedTransferRequests: any = transferRequests.map((request) => {
        // Create a new object to avoid modifying the original
        const modifiedRequest: any = { ...request };

        // Check if this request is PENDING and older than 24 hours
        if (modifiedRequest.status === 'PENDING' && new Date(modifiedRequest.createdAt) < twentyFourHoursAgo) {
          // Only update the status in memory, not in the database
          modifiedRequest.status = 'CANCELLED';

          // Log for debugging purposes
          logger.info(`Display status changed to CANCELLED for request ${modifiedRequest._id} (older than 24 hours)`);
        }

        return modifiedRequest;
      });

      const totalCount = await TransferRequest.countDocuments(searchConditions);
      const totalPages = Math.ceil(totalCount / limit);
      const nextPage = page < totalPages ? page + 1 : null;
      const previousPage = page > 1 ? page - 1 : null;

      // Return an empty response if no data found
      if (!modifiedTransferRequests.length) {
        return {
          transferRequests: modifiedTransferRequests || [],
          totalCount: 0,
          currentPage: page,
          totalPages: 0,
          message: 'No transfer requests found',
        };
      }

      return {
        transferRequests: modifiedTransferRequests, // Return the modified data
        totalCount,
        currentPage: page,
        totalPages,
        nextPage,
        previousPage,
      };
    } catch (error) {
      logger.error(error, 'Error while fetching transfer requests');
      throw new Error('Failed to fetch transfer requests');
    }
  };

  /**
   * This method updates the transfer request with the new wallet address and status.
   * @param data The data to be updated.
   * @param filter The filter to use to find the transfer request.
   * @returns A promise with the response object containing the transfer request details and a status code.
   */
  static updateForceTransferDetails = async (data: any, filter: FilterQuery<ITransferRequest>): Promise<PromiseResolve> => {
    try {
      if (data.type === 'ForceTransferred') {
        data.status = transferStatusEnum.APPROVED;
        await this.processForceTransfer(data);
      }
      const search = { _id: filter?._id };
      const updateUserResp: any = await TransferRequest.findOneAndUpdate(search, data, { new: true, runValidators: true, upsert: true });
      const userName = updateUserResp?.registeredName;
      const userEmail = updateUserResp?.registeredEmailId;
      const fromUserEmail = updateUserResp?.newRegisteredEmailId;
      const newUserName = updateUserResp?.newRegisteredName;

      if (updateUserResp) {
        const user = await UserService.fetchUserDetails({ _id: updateUserResp?.userId });
        const offering = await OfferingService.fetchOfferingDetails({ _id: updateUserResp?.offeringId });
        const { email, name } = user?.data ?? 'user';
        // if ((data.status == transferStatusEnum.REJECTED)) {
        const { offeringName } = offering?.data?.projectDetails ?? 'valuit';
        const emailDetail: any = {
          email,
          name,
          amount: updateUserResp?.tokenQuantity ?? '0',
          reason: data?.reason ?? 'invalid',
          offeringName,
          date: new Date().toLocaleString('en-US', { timeZone: 'UTC' }),
          txHash: data?.txHash,
          newWalletAddress: updateUserResp?.newWalletAddress ?? 'invalid',
        };
        if (!updateUserResp?.isForceTransfer) {
          if (data.status == transferStatusEnum.APPROVED) {
            emailHelper.sendEmailTemplate(email, 'transferapproval', emailDetail);
          }
          if (data.status == transferStatusEnum.REJECTED) {
            emailHelper.sendEmailTemplate(email, 'transferrejected', emailDetail);
          }
        } else {
          if (data.status == transferStatusEnum.APPROVED) {
            emailHelper.sendEmailTemplate(userEmail, 'forceTransfer', {
              userEmail,
              name: userName,
              amount: updateUserResp?.tokenQuantity ?? '0',
              reason: data?.reason ?? 'invalid',
              offeringName,
              date: new Date().toLocaleString('en-US', { timeZone: 'UTC' }),
              txHash: data?.txHash,
              newWalletAddress: updateUserResp?.newWalletAddress ?? 'invalid',
            });
            emailHelper.sendEmailTemplate(fromUserEmail, 'fromforceTransfer', {
              fromUserEmail,
              name: newUserName,
              amount: updateUserResp?.tokenQuantity ?? '0',
              reason: data?.reason ?? 'invalid',
              offeringName,
              date: new Date().toLocaleString('en-US', { timeZone: 'UTC' }),
              txHash: data?.txHash,
              newWalletAddress: updateUserResp?.walletAddress ?? 'invalid',
            });
          }
          if (data.status == transferStatusEnum.REJECTED) {
            emailHelper.sendEmailTemplate(email, 'transferrejected', emailDetail);
          }
        }
        const finalData = JSON.parse(JSON.stringify(updateUserResp));

        await kafkaService.sendMessageToAdmin({ value: { ...finalData, type: queueMessageTypeEnum.TRANSFER } });

        // emit socket to issuer
        const socketData = data;
        socketData._id = filter?._id;
        console.log('\n\n force-transferred------->  ', socketData);
        socketHelper.broadcast('force-transferred', socketData);

        return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.USER.ORDER_UPDATION_SUCCESS, data: updateUserResp };
      }
      await kafkaService.sendMessageToAdmin({ value: JSON.stringify({ ...updateUserResp, type: queueMessageTypeEnum.ORDER }) });

      // emit socket to issuer
      socketHelper.broadcast('force-transferred', data);

      return { status: RESPONSES.BAD_REQUEST, error: true, message: RES_MSG.ERROR_MSG.USER_UPDATION_ERROR };
    } catch (error) {
      // Enhanced error handling
      logger.error(error, 'updateForceTransferDetails error');
      // General error handling
      return { status: RESPONSES.BAD_REQUEST, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * Get all transfer requests associated with an offering Id
   * @param {Number} page - The page number
   * @param {Number} limit - The number of records per page
   * @param {String} searchQuery - The search query, can be empty or undefined
   * @param {ObjectId} offeringId - The offering Id
   * @returns {Promise<Object>} - An object containing the transfer requests, total count, current page, total pages, next page and previous page
   */
  static getAllTransferRequestsByOfferingId = async (page: number, limit: number, searchQuery: string, offeringId: Types.ObjectId, isCsv: any) => {
    try {
      const matchStage: any = { offeringId };
      if (searchQuery !== undefined && searchQuery !== '' && searchQuery !== 'undefined') {
        const searchRegex = new RegExp(searchQuery, 'i'); // Case-insensitive search
        matchStage.$or = [{ walletAddress: { $regex: searchRegex } }, { registeredEmailId: { $regex: searchRegex } }, { registeredName: { $regex: searchRegex } }, { securityName: { $regex: searchRegex } }];
      }

      const pipeline: any = [
        { $match: matchStage },
        // get multisig details start query
        { $lookup: { from: 'offerings', localField: 'offeringId', foreignField: '_id', as: 'offeringDetails' } },
        { $unwind: { path: '$offeringDetails', preserveNullAndEmptyArrays: true } },

        {
          $lookup: {
            from: 'multisigs',
            let: { userIdFromOffering: '$offeringDetails.userId' },
            pipeline: [
              {
                $match: {
                  $expr: { $eq: ['$issuerId', '$$userIdFromOffering'] },
                },
              },
            ],
            as: 'multisigDetails',
          },
        },
        { $unwind: { path: '$multisigDetails', preserveNullAndEmptyArrays: true } },
        // get multisig details end query

        {
          $addFields: {
            isOlderThan24Hours: {
              $lt: ['$createdAt', new Date(Date.now() - 24 * 60 * 60 * 1000)],
            },
          },
        },
        {
          $addFields: {
            status: {
              $cond: {
                if: { $and: ['$isForceTransfer', '$isOlderThan24Hours'] },
                then: 'REJECTED',
                else: '$status',
              },
            },
          },
        },
        { $unset: 'offeringDetails' },
        { $sort: { createdAt: -1 } },
        {
          $facet: {
            metadata: [{ $count: 'totalCount' }],
            // Modify pagination based on `isCsv`
            transferRequests: isCsv
              ? [] // If isCsv is true, no pagination, return all items
              : [{ $skip: (page - 1) * limit }, { $limit: limit }], // Otherwise, apply pagination
          },
        },
      ];
      const [result] = await TransferRequest.aggregate(pipeline);
      const totalCount = result.metadata[0]?.totalCount || 0;
      const totalPages = Math.ceil(totalCount / limit);
      const nextPage = page < totalPages ? page + 1 : null;
      const previousPage = page > 1 ? page - 1 : null;

      return {
        transferRequests: result.transferRequests || [],
        totalCount,
        currentPage: page,
        totalPages,
        nextPage,
        previousPage,
      };
    } catch (error) {
      logger.error(error, 'Error while fetching transfer requests');
      throw new CustomError(error.message || 'Failed to fetch transfer request', error.status || RESPONSES.BAD_REQUEST);
    }
  };

  static processForceTransfer = async (data: any): Promise<PromiseResolve> => {
    try {
      const { fromAddress, toAddress, amount, txHash, tokenAddress } = data;

      const { error, data: offeringData, status, message } = await OfferingService.fetchOfferingDetails({ tokenAddress });

      if (error || !offeringData) throw new CustomError(message, status);

      const { _id: offeringId, projectDetails } = offeringData;

      const [fromUserResp, toUserResp] = await Promise.all([UserDetailsSchema.findOne({ 'wallets.address': fromAddress }), UserDetailsSchema.findOne({ 'wallets.address': toAddress })]);

      if (!fromUserResp || !toUserResp) {
        throw new Error(!fromUserResp ? 'User not found for fromAddress' : 'User not found for toAddress');
      }

      const { _id: fromUserId } = fromUserResp;
      const { _id: toUserId } = toUserResp;

      let currentPrice;
      const { assetType, latestNav, previousValuation, tokenSupply, tokenDecimals } = projectDetails;

      if (assetType === AssetTypeEnum.RealEstate && latestNav) {
        currentPrice = calculate('div', latestNav, tokenSupply) || 0;
      } else if (assetType === AssetTypeEnum.Equity && previousValuation) {
        currentPrice = calculate('div', previousValuation, tokenSupply) || 0;
      }

      const baseData = { offeringId, quantity: calculate('div', amount, BigInt(10 ** tokenDecimals).toString()), feesInPercentage: 0, currentPrice, orderMinted: new Date().toISOString() };
      const { data: portFolio } = await UserService.fetchUserPortfolio(fromUserId?.toString(), false, {}, offeringId?._id.toString());
      const avgPrice = portFolio?.portfolio?.[0]?.averagePricePerToken || 0;
      //token transfer From one user to another user
      const fromData = {
        ...baseData,
        userId: fromUserId,
        walletAddress: fromAddress,
        orderType: orderStatusEnum.TRANSFER,
        status: orderStatusEnum.TRANSFER_FROM,
        amount: calculate('mul', baseData.quantity, avgPrice),
        txHash: `${txHash}?from`,
        price: avgPrice,
      };

      //token received from another user

      const toData = {
        ...baseData,
        userId: toUserId,
        walletAddress: toAddress,
        orderType: orderStatusEnum.TRANSFER,
        status: orderStatusEnum.TRANSFER_TO,
        amount: calculate('mul', baseData.quantity, currentPrice),
        txHash: `${txHash}?to`,
        price: currentPrice,
      };

      await OrderService.createOrder(toData, toData.userId);
      await OrderService.createOrder(fromData, fromData.userId);

      return { status: RESPONSES.SUCCESS, error: false, message: 'Force transfer processed successfully', data: { fromData, toData } };
    } catch (error) {
      logger.error(error, 'Error while processing force transfer');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };
}
