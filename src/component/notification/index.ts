import { Request, Response } from 'express';
import { PromiseResolve } from '../../utils/common.interface';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import { ResponseHandler } from '../../helpers/response.helper';
import logger from '../../helpers/logging/logger.helper';
import notificationClient from '../../_grpc/clients/notification.client';

/**
 * @export
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise < PromiseResolve >}
 */
export async function getOfferingNotification(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { userInfo } = req;
    const { userId } = userInfo;
    const payload = {
      page: req.query.page,
      limit: req.query.limit,
      userId: userId,
    };
    await notificationClient.client.getIssuerNotification(payload, async (error: any, response: any) => {
      if (error || response?.error) {
        const errorMessage = error?.message || response?.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR;
        const errorStatus = error?.status || RESPONSES.INTERNAL_SERVER_ERROR;

        console.error('gRPC Error:', error || response);
        return ResponseHandler.error(res, {
          status: errorStatus,
          error: true,
          message: errorMessage,
        });
      }
      // unReadCount: JSON.parse(response.unReadCount),

      // console.log(' JSON.parse(response.data) --->>', userInfo, response.data, response.unReadCount);
      return ResponseHandler.success(res, {
        status: response.status || RESPONSES.SUCCESS,
        error: false,
        message: response.message || RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
        data: {
          notification: JSON.parse(response.data),
          unReadCount: response?.unReadCount,
        },
      });
    });
  } catch (error: any) {
    logger.error(error, 'getOffering Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

/**
 * @export
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise < PromiseResolve >}
 */
export async function seenOfferingNotification(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { userInfo } = req;
    const { userId } = userInfo;
    const payload = {
      _id: req.query._id,
      userId: userId,
    };
    await notificationClient.client.seenIssuerNotification(payload, async (error: any, response: any) => {
      if (error || response?.error) {
        const errorMessage = error?.message || response?.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR;
        const errorStatus = error?.status || RESPONSES.INTERNAL_SERVER_ERROR;

        console.error('gRPC Error:', error || response);
        return ResponseHandler.error(res, {
          status: errorStatus,
          error: true,
          message: errorMessage,
        });
      }

      return ResponseHandler.success(res, {
        status: response.status || RESPONSES.SUCCESS,
        error: false,
        message: response.message || RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
        data: JSON.parse(response.data),
      });
    });
  } catch (error: any) {
    logger.error(error, 'getOffering Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}
