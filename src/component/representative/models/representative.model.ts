import mongoose, { Schema } from 'mongoose';
import { RepresentativeModel } from '../interface';

const RepresentativeSchema: Schema<RepresentativeModel> = new Schema(
  {
    issuer_id: { type: Schema.Types.ObjectId, ref: 'users', required: true },
    name: { type: String, required: true },
    email: { type: String, required: true },
    address: { type: String, default: null },
  },
  { timestamps: true, versionKey: false },
);

// Compound index: Unique combination of issuer_id and email
RepresentativeSchema.index({ issuer_id: 1, email: 1 }, { unique: true });

// Error handling
RepresentativeSchema.post('save', (error: any, doc: any, next: any) => {
  if (error.code === 11000) {
    if (error.keyPattern?.email && error.keyPattern?.issuer_id) {
      next(new Error('Representative already exists with this issuer and email.'));
    } else {
      next(new Error('Duplicate key error.'));
    }
  } else {
    next(error);
  }
});
export const representativeModel = mongoose.model<RepresentativeModel>('Representative', RepresentativeSchema);
