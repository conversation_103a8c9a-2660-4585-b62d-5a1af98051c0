import * as <PERSON><PERSON> from 'joi';
import { JoiValidationResult } from '../../utils/common.interface';
import logger from '../../helpers/logging/logger.helper';

interface IInvite extends Document {
  name: string;
  email: string;
  address: string;
}

export class RepresentativeRequestValidation {
  /**
   * Validate the invite request creation request
   * @returns {Promise<JoiValidationResult>}
   */

  static async inviteRequestValidation(inviteData: IInvite): Promise<JoiValidationResult> {
    try {
      const schema = Joi.object({
        email: Joi.string().email().required().messages({ 'string.email': 'email must be a valid email' }),
        name: Joi.string().min(3).required().messages({
          'string.min': 'name must be at least 3 characters long',
          'string.max': 'name must not exceed 50 characters',
        }),
        address: Joi.string()
          .pattern(/^0x[a-fA-F0-9]{40}$/)
          .allow(null, '')
          .messages({
            'string.pattern.base': 'Address must be a valid address',
          }),
      }).custom((value) => {
        console.log('value', value);
        return value;
      });

      const { error, value } = schema.validate(inviteData);
      if (error) {
        // return { error: true, value: '', message: error.details[0]?.context.message, status: 400 };
        return { error: true, value: '', message: error.details[0].message, status: 400 };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'inviteRequestValidation error');
      return {
        error: true,
        value: '',
        message: 'Internal Server Error',
        status: 500, // Internal Server Error
      };
    }
  }
}
