import { Request, Response } from 'express';
import CustomError from '../../helpers/customError.helper';
import { PromiseResolve } from '../../utils/common.interface';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import { ResponseHandler } from '../../helpers/response.helper';
import logger from '../../helpers/logging/logger.helper';
import UserService from '../userAuthentications/service';
import { service } from './service';
import { Types } from 'mongoose';
class RepresentativeController {
  /**
   * Handles the user Representative add process.
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise<PromiseResolve>}
   */
  public invite = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { name, email, address = '' } = req.body;
      const { userId } = req?.userInfo || {};
      let issuer_id = '';
      if (userId) {
        issuer_id = userId;
      } else {
        issuer_id = req.body.issuer_id;
      }
      let representative_id: any;
      const { error: userDetailsError, data: userDetails } = await UserService.fetchUserDetails({ email }, ['email', 'mobile', 'isKyc', 'invitedBy', 'isRepresentatives', 'issuerStatus']);
      if (!userDetailsError) {
        if (userDetails?.issuerStatus.toUpperCase() === 'APPROVED') {
          throw new CustomError(RES_MSG?.ERROR_MSG.REPRESENTATIVE_ALREADY_EXIST, RESPONSES.CONFLICT);
        }
        representative_id = userDetails._id.toString();
      }

      const { error: representativeError, message, status, data } = await service.create({ issuer_id: new Types.ObjectId(issuer_id), name, email, address });
      if (representativeError) {
        throw new CustomError(message || RES_MSG?.ERROR_MSG.INVALID_EMAIL, status || RESPONSES.BAD_REQUEST);
      }

      // update user
      if (representative_id) {
        userDetails.invitedBy.push(issuer_id);
        const updateUserResp = await UserService.updateUserDetails({ isRepresentatives: true, invitedBy: userDetails.invitedBy }, { _id: representative_id });
        if (updateUserResp.error) {
          throw new CustomError(updateUserResp.message, updateUserResp.status);
        }
      }

      return ResponseHandler.success(res, {
        message: RES_MSG?.SUCCESS_MSG.REPRESENTATIVE_INVITE_SUCCESS,
        status: RESPONSES.CREATED,
        error: false,
        data: data,
      });
    } catch (error) {
      logger.error(error, 'invite representative Error');

      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };

  /**
   * Get representatives for the current user with optional search, CSV export, and pagination.
   *
   * @param {Request} req - Express request object containing query parameters:
   *   - page {string | number} [optional] - Page number for pagination (default: 1).
   *   - limit {string | number} [optional] - Number of records per page (default: 10).
   *   - search {string} [optional] - Search term to filter representatives by email.
   *   - isCsv {string} [optional] - If 'true', disables pagination for CSV export.
   * @param {Response} res - Express response object.
   * @returns {Promise<PromiseResolve>} - API response with representatives and pagination data.
   */

  public getRepresentative = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { page = '1', limit = '10', search = '', isCsv = 'false' } = req.query || {};

      const pageNo = page ? parseInt(page as string, 10) : 1;
      const limitNo = limit ? parseInt(limit as string, 10) : 10;
      const isExport = isCsv === 'true';

      const filter = {
        userId: req?.userInfo?.userId || '',
        search: search as string,
      };

      const { error, data, message } = await service.getRepresentative(pageNo, limitNo, isExport, filter);

      if (error) {
        throw new CustomError(message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, RESPONSES.INTERNAL_SERVER_ERROR);
      }

      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
        data,
      });
    } catch (error: any) {
      logger.error(error, 'Error while fetching representative by user');
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };
}

export default new RepresentativeController();
