import { representativeModel } from './models/';
import { RepresentativeInput, RepresentativeModel } from './interface';
import { PromiseResolve } from '../../utils/common.interface';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import logger from '../../helpers/logging/logger.helper';
import emailHelper from '../../helpers/email.helper';
import { Types } from 'mongoose';
export const service = {
  async create(data: RepresentativeInput): Promise<PromiseResolve> {
    try {
      const result: RepresentativeModel = await representativeModel.create(data);
      if (result) {
        const resultObject = result.toObject();
        // send email
        const detail = { name: data?.name, email: data?.email, date: new Date().toLocaleString('en-US', { timeZone: 'UTC' }) };
        await emailHelper.sendEmailTemplate(detail.email, 'invitingRepresentative', detail);

        return { status: RESPONSES.CREATED, error: false, message: RES_MSG.SUCCESS_MSG.REPRESENTATIVE_INVITE_SUCCESS, data: resultObject };
      }
      return { status: RESPONSES.BAD_REQUEST, error: true, message: RES_MSG.COMMON.SOMETHING_WRONG, data: {} };
    } catch (error) {
      logger.error(error, 'add Representative error');
      console.log('error --->', error.status, error.message);
      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, data: {} };
    }
  },

  /**
   * Get representatives with optional filters and pagination.
   *
   * @param {number} [page=1] - The page number for pagination.
   * @param {number} [limit=10] - The number of records per page.
   * @param {boolean} [isCsv=false] - If true, returns all data without pagination (e.g., for CSV export).
   * @param {Object} [filter={}] - Optional filters.
   * @param {string} [filter.userId] - Optional issuer/user ID to filter representatives.
   * @param {string} [filter.search] - Optional search query to filter by email.
   * @returns {Promise<PromiseResolve>} - The representatives list and pagination metadata.
   */

  async getRepresentative(page = 1, limit = 10, isCsv: boolean = false, filter: { userId?: string; search?: string } = {}): Promise<PromiseResolve> {
    try {
      const skip = (page - 1) * limit;
      const { userId, search = '' } = filter;

      const pipeline: any[] = [];

      // Add userId match if provided
      if (userId) {
        pipeline.push({
          $match: { issuer_id: new Types.ObjectId(userId) },
        });
      }

      pipeline.push(
        {
          $lookup: {
            from: 'users',
            localField: 'email',
            foreignField: 'email',
            as: 'representativeDetails',
          },
        },
        {
          $unwind: {
            path: '$representativeDetails',
            preserveNullAndEmptyArrays: true,
          },
        },
        // Lookup userdetails collection using users._id
        {
          $lookup: {
            from: 'userdetails',
            localField: 'representativeDetails._id',
            foreignField: '_id',
            as: 'userDetails',
          },
        },
        {
          $unwind: {
            path: '$userDetails',
            preserveNullAndEmptyArrays: true,
          },
        },
        // Final projection
        {
          $project: {
            _id: 1,
            issuer_id: 1,
            name: 1,
            email: 1,
            address: 1,
            createdAt: 1,
            updatedAt: 1,
            representativeDetails: {
              _id: '$representativeDetails._id',
              name: '$representativeDetails.name',
              kycStatus: '$representativeDetails.kycStatus',
              countryCode: '$representativeDetails.countryCode',
              mobile: '$representativeDetails.mobile',
              issuerStatus: '$representativeDetails.issuerStatus',
              // wallets: '$userDetails.wallets',
              address: {
                $cond: [{ $gt: [{ $size: { $ifNull: ['$userDetails.wallets', []] } }, 0] }, { $arrayElemAt: ['$userDetails.wallets.address', 0] }, null],
              },
              signerAddress: {
                $cond: [{ $gt: [{ $size: { $ifNull: ['$userDetails.wallets', []] } }, 0] }, { $arrayElemAt: ['$userDetails.wallets.signerAddress', 0] }, null],
              },
            },
          },
        },
      );

      if (search) {
        pipeline.push({
          $match: { email: { $regex: search, $options: 'i' } },
        });
      }

      pipeline.push(
        { $sort: { createdAt: -1 } },
        {
          $facet: {
            representatives: isCsv ? [] : [{ $skip: skip }, { $limit: limit }],
            totalCount: [{ $count: 'count' }],
          },
        },
      );

      const [result] = await representativeModel.aggregate(pipeline).exec();
      const totalCount = result?.totalCount?.[0]?.count || 0;
      const representatives = result?.representatives || [];
      const totalPages = Math.ceil(totalCount / limit);

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
        data: {
          representatives,
          currentPage: page,
          totalPages,
          totalCount,
          nextPage: page < totalPages ? page + 1 : null,
          previousPage: page > 1 ? page - 1 : null,
        },
      };
    } catch (error: any) {
      logger.error(error, 'getRepresentative');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        data: {},
      };
    }
  },
  async findAll(filter = {}): Promise<RepresentativeModel[]> {
    return await representativeModel.find(filter).lean();
  },

  async findById(id: string): Promise<RepresentativeModel | null> {
    return await representativeModel.findById(id).lean();
  },

  async update(id: string, data: Partial<RepresentativeInput>): Promise<RepresentativeModel | null> {
    return await representativeModel
      .findByIdAndUpdate(id, data, {
        new: true,
        runValidators: true,
      })
      .lean();
  },

  async remove(id: string): Promise<RepresentativeModel | null> {
    return await representativeModel.findByIdAndDelete(id).lean();
  },
};
