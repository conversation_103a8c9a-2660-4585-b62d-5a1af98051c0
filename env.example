NODE_ENV=qa
PROJECT_NAME=
LOG_LEVEL="debug"

PORT=4000
SOCKET_PORT=9004

MONGODB_HOST=*******
MONGODB_USER=
MONGODB_PASSWORD=
MONGODB_PORT=
MONGODB_DATABASE=

REDIS_HOST=redis://*******
LOGIN_MAX_ATTEMPT=5
LOGIN_BLOCK_TIME=900             #IN SECONDS
OTP_EXPIRY=300

JWT_AUTH_SECRET=MVP
JWT_AUTH_EXPIRE=7hr                        # 1 hour
JWT_REFRESH_SECRET=mySuperSecretRefreshKey
JWT_REFRESH_EXPIRE=86400                   # 1 day
JWT_FORGOT_EXPIRE=10m                      # 10 minutes
JWT_EMAIL_FORGOT_EXPIRE=10m                # 10 minutes
JWT_EMAIL_LOGIN_EXPIRE=10m                 # 10 minutes
JWT_2FA_EXPIRE=10m

SENDGRID_API_KEY=SG..M0mGfug1lucMY8QA2yiSXyZgmLx6aNGUkuzY7I
SENDER=<EMAIL>

GOOGLE_CLIENT_ID=
MAX_SIZE=5
GOOGLE_PROJECT_ID=
BUCKET_NAME=
GOOGLE_KEY_FILE_NAME=

# KAFKA_BROKER=*******
# KAFKA_BROKER_PORT=29093
KAFKA_BROKER=localhost
KAFKA_BROKER_PORT=9092

USER_SERVICE_GRPC_PORT=40001
USER_SERVICE_GRPC_CONTAINER_NAME=0.0.0.0
ADMIN_SERVICE_GRPC_CONTAINER_NAME=
ADMIN_SERVICE_GRPC_PORT=
NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME=
NOTIFICATION_SERVICE_GRPC_PORT=
GRPC_SSL=

SUMSUB_SECRET_KEY=
SUMSUB_TOKEN=

LOGIN_URL=
BASEURL=
CAP_TABLE_URL=

TWILIO_ACCOUNT_SID=AC8efd4 ******************444ea
TWILIO_AUTH_TOKEN=c**********************
TWILIO_PHONE_NUMBER=+1111111111111111111111

FRONTEND_URL=

RPC_URL=
FUND_CONTRACT_ADDRESS=
USDC_ADDRESS=

AWS_BUCKET_NAME=
AWS_REGION=

# common

DOCUSIGN_CLIENT_ID=833******************************99
DOCUSIGN_IMPERSONATED_USER_ID=1*********************d2568eac9e55
DOCUSIGN_ACCOUNT_ID=***********************d3d3
DOCUSIGN_OAUTH_URL=https://account-d.docusign.com
DOCUSIGN_WEBHOOK_USERNAME=dev
DOCUSIGN_WEBHOOK_PWD=
DOCUSIGN_PRIVATE_KEY="-----BEGIN RSA PRIVATE KEY-----
***********************************************
-----END RSA PRIVATE KEY-----"
